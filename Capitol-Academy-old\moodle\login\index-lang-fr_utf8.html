<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="fr" xml:lang="fr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Capitol Academy: Se connecter sur le site "/>
    <title>Capitol Academy: Se connecter sur le site</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/login/index-lang-fr_utf8.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
addonload(function() { if(el = document.getElementById('username')) el.focus(); });
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="login course-1 notloggedin dir-ltr lang-fr_utf8" id="login-index">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">Capitol Academy</h1>
        <div class="headermenu"><div class="logininfo">Non connecté. (<a href="../../moodle/login/index.html">Connexion</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">Vous êtes ici</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../../moodle.html">Academy</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> Se connecter sur le site</li></ul></div>
        <div class="navbutton"><div class="langmenu"><form action="/" method="get" id="chooselang" class="popupform"><div><label for="chooselang_jump"><span class="accesshide ">Langue</span></label><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=ar_utf8">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=en_utf8">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=fr_utf8" selected="selected">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="UCuov3BJ1i"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="Valider"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div></div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix"><div class="loginbox clearfix twocolumns">
  <div class="loginpanel">
    <h2>Vous possédez déjà un compte ?</h2>
      <div class="subcontent loginsub">
        <div class="desc">
          Connectez-vous ici en utilisant votre nom d'utilisateur<br/>et mot de passe<br/>(Votre navigateur doit supporter les cookies)<span class="helplink"><a title="Aide sur Votre navigateur doit supporter les cookies (nouvelle fenêtre)" href="../../moodle/help-module-moodle-file-cookies_html-forcelang.html" onclick="this.target='/'; return openpopup('/', 'popup', '/', 0);"></a></span>        </div>
                <form action="../../moodle/login/index.html" method="post" id="login">
          <div class="loginform">
            <div class="form-label"><label for="username">Nom d'utilisateur</label></div>
            <div class="form-input">
              <input type="text" name="username" id="username" size="15" value=""/>
            </div>
            <div class="clearer"><!-- --></div>
            <div class="form-label"><label for="password">Mot de passe</label></div>
            <div class="form-input">
              <input type="password" name="password" id="password" size="15" value=""/>
              <input type="submit" value="Connexion"/>
              <input type="hidden" name="testcookies" value="1"/>
            </div>
            <div class="clearer"><!-- --></div>
          </div>
        </form>
      </div>
      <div class="subcontent guestsub">
        <div class="desc">
          Les visiteurs anonymes peuvent accéder à<br/>certains cours        </div>
        <form action="../../moodle/login/index.html" method="post" id="guestlogin">
          <div class="guestform">
            <input type="hidden" name="username" value="guest"/>
            <input type="hidden" name="password" value="guest"/>
            <input type="hidden" name="testcookies" value="1"/>
            <input type="submit" value="Connexion anonyme"/>
          </div>
        </form>
      </div>
      <div class="subcontent forgotsub">
        <div class="desc">
          Vous avez oublié votre nom d'utilisateur et/ou votre mot de passe ?        </div>
        <form action="../../moodle/login/forgot_password.html" method="post" id="changepassword">
          <div class="forgotform">
            <input type="hidden" name="sesskey" value="UCuov3BJ1i"/>
            <input type="submit" value="Récupération de votre mot de passe"/>
          </div>
        </form>
      </div>
     </div>
    <div class="signuppanel">
      <h2>Première visite sur ce site ?</h2>
      <div class="subcontent">
<p>Bonjour. Pour un accès complet aux cours, prenez une minute pour vous créer un compte personnel sur ce site.<br/>
Chaque cours peut également nécessiter une clef d'inscription à usage unique dont vous n'avez pas besoin pour l'instant.</p>
<p>Voici les étapes à suivre :</p>
   <ol>
   <li>Remplir le formulaire <a href="../../moodle/login/signup.html">nouveau compte</a>.</li>
   <li>Un message vous sera immédiatement adressé par courriel.</li>
   <li>Récupérer ce message et cliquer sur le lien Web qu'il contient.</li>
   <li>Votre inscription sera alors confirmée et vous serez connecté.</li>
   <li>Vous pourrez ensuite choisir le cours auquel vous souhaitez vous inscrire.</li>
   <li>Si une clef d'inscription vous est demandée, utilisez celle que votre enseignant vous aura communiquée.</li>
   <li>Vous aurez alors un accès au cours complet. À votre prochaine visite il vous suffira d'entrer votre nom d'utilisateur et mot de passe (dans le formulaire ci-contre) afin de vous connecter et d'accéder à tous les cours auxquels vous serez inscrit.</li>
   </ol>                 <div class="signupform">
                   <form action="../../moodle/login/signup.html" method="get" id="signup">
                   <div><input type="submit" value="Créer un compte"/></div>
                   </form>
                 </div>
      </div>
    </div>
</div>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">Non connecté. (<a href="../../moodle/login/index.html">Connexion</a>)</div><div class="homelink"><a href="../../moodle.html">Accueil</a></div></div>
</div>
</body>
</html>