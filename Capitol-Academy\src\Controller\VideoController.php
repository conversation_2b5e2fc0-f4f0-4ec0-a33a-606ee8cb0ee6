<?php

namespace App\Controller;

use App\Entity\Video;
use App\Entity\User;
use App\Repository\VideoRepository;
use App\Service\AccessControlService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/videos')]
class VideoController extends AbstractController
{
    #[Route('/', name: 'app_videos_list')]
    #[Route('/', name: 'app_videos')]
    public function list(VideoRepository $videoRepository): Response
    {
        $videos = $videoRepository->findActiveVideos();

        return $this->render('video/list.html.twig', [
            'videos' => $videos,
        ]);
    }

    #[Route('/{id}', name: 'app_video_show', requirements: ['id' => '\d+'])]
    public function show(int $id, VideoRepository $videoRepository, EntityManagerInterface $entityManager, AccessControlService $accessControlService): Response
    {
        $video = $videoRepository->find($id);

        if (!$video) {
            throw $this->createNotFoundException('Video not found');
        }

        // Check if user is logged in
        $user = $this->getUser();
        
        // For premium videos, require login
        if (!$video->isFree() && !$user) {
            // Redirect to login page for premium content
            $this->addFlash('info', 'Please log in to access premium video content.');
            return $this->redirectToRoute('app_login');
        }

        // Check access for premium videos
        $hasAccess = false;
        if ($video->isFree()) {
            $hasAccess = true;
        } elseif ($user instanceof User) {
            $hasAccess = $accessControlService->hasVideoAccess($user, $video);
        }

        // Get related videos
        $relatedVideos = [];
        if ($video->getCategory()) {
            $relatedVideos = $videoRepository->findByCategory($video->getCategory(), 4);
            $relatedVideos = array_filter($relatedVideos, fn($v) => $v->getId() !== $video->getId());
        }

        return $this->render('video/show.html.twig', [
            'video' => $video,
            'has_access' => $hasAccess,
            'related_videos' => $relatedVideos,
        ]);
    }
}
