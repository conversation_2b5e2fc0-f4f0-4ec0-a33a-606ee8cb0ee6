{#
    Capitol Academy Admin Table Component
    Standardized table with consistent styling, pagination, and empty states
    
    Parameters:
    - headers: Array of header objects with {text, style} properties
    - rows: Array of row objects with {cells, attributes} properties
    - row_class: CSS class for table rows (for search targeting)
    - table_id: Optional table ID
    - table_attributes: Optional table attributes
    - empty_message: Message to show when no data
    - empty_icon: FontAwesome icon for empty state
    - empty_description: Description for empty state
    - empty_action: Object with {url, text, icon} for empty state action
    - search_config: Object with {fields} array for search targeting
    - pagination: Object with pagination data
#}

<!-- Professional Table -->
<div class="table-responsive">
    <table class="table table-hover border-0 shadow-sm admin-table" 
           {% if table_id is defined %}id="{{ table_id }}"{% endif %}
           {% if table_attributes is defined %}{{ table_attributes|raw }}{% endif %}
           style="background: white; border-radius: 12px; overflow: hidden;">
        
        <!-- Table Header -->
        <thead style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;">
            <tr>
                {% for header in headers %}
                <th style="border: none; padding: 1rem; font-weight: 600; text-transform: uppercase; font-size: 0.85rem; letter-spacing: 0.5px; {{ header.style|default('') }}">
                    {{ header.text }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        
        <!-- Table Body -->
        <tbody>
            {% if rows|length > 0 %}
                {% for row in rows %}
                <tr class="{{ row_class|default('table-row') }}" 
                    style="transition: all 0.3s ease; border-bottom: 1px solid rgba(0,0,0,0.05);"
                    {% if row.attributes is defined %}{{ row.attributes|raw }}{% endif %}>
                    {% for cell in row.cells %}
                    <td style="padding: 1rem; vertical-align: middle; border: none; {{ cell.style|default('') }}">
                        {{ cell.content|raw }}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            {% else %}
                <!-- Empty State -->
                <tr>
                    <td colspan="{{ headers|length }}" class="text-center py-5">
                        <div class="empty-state">
                            <div class="mb-4">
                                <i class="{{ empty_icon|default('fas fa-inbox') }} fa-4x text-muted"></i>
                            </div>
                            <h4 class="text-muted mb-3">{{ empty_message|default('No data found') }}</h4>
                            {% if empty_description %}
                                <p class="text-muted mb-4">{{ empty_description }}</p>
                            {% endif %}
                            {% if empty_action is defined %}
                                <a href="{{ empty_action.url }}" class="btn btn-primary btn-lg">
                                    <i class="{{ empty_action.icon|default('fas fa-plus') }} me-2"></i>{{ empty_action.text }}
                                </a>
                            {% endif %}
                        </div>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Pagination -->
{% if pagination is defined and pagination.total_pages > 1 %}
<div class="row mt-4">
    <div class="col-sm-12 col-md-5">
        <div class="dataTables_info">
            Showing {{ ((pagination.current_page - 1) * pagination.limit) + 1 }} to 
            {{ ((pagination.current_page - 1) * pagination.limit) + rows|length }} of 
            {{ pagination.total_items }} entries
            {% if pagination.search %}
                (filtered from total entries)
            {% endif %}
        </div>
    </div>
    <div class="col-sm-12 col-md-7">
        <div class="dataTables_paginate paging_simple_numbers">
            <ul class="pagination justify-content-end">
                <!-- Previous Button -->
                {% if pagination.current_page > 1 %}
                    <li class="paginate_button page-item previous">
                        <a href="{{ pagination.base_url }}?page={{ pagination.current_page - 1 }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}" 
                           class="page-link">Previous</a>
                    </li>
                {% else %}
                    <li class="paginate_button page-item previous disabled">
                        <span class="page-link">Previous</span>
                    </li>
                {% endif %}
                
                <!-- Page Numbers -->
                {% for page in 1..pagination.total_pages %}
                    {% if page == pagination.current_page %}
                        <li class="paginate_button page-item active">
                            <span class="page-link">{{ page }}</span>
                        </li>
                    {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                        <li class="paginate_button page-item">
                            <a href="{{ pagination.base_url }}?page={{ page }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}" 
                               class="page-link">{{ page }}</a>
                        </li>
                    {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                        <li class="paginate_button page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                <!-- Next Button -->
                {% if pagination.current_page < pagination.total_pages %}
                    <li class="paginate_button page-item next">
                        <a href="{{ pagination.base_url }}?page={{ pagination.current_page + 1 }}{% if pagination.search %}&search={{ pagination.search }}{% endif %}" 
                           class="page-link">Next</a>
                    </li>
                {% else %}
                    <li class="paginate_button page-item next disabled">
                        <span class="page-link">Next</span>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</div>
{% endif %}

<!-- Table Styling -->
<style>
/* Table hover effects */
.admin-table tbody tr:hover {
    background-color: rgba(1, 26, 45, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Empty state styling */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.6;
}

.empty-state h4 {
    font-weight: 600;
}

/* Pagination styling */
.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #011a2d;
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #011a2d;
    border-color: #011a2d;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
    border-color: #011a2d;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Responsive table */
@media (max-width: 768px) {
    .admin-table {
        font-size: 0.875rem;
    }
    
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.75rem 0.5rem !important;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .empty-state i {
        font-size: 3rem !important;
    }
    
    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .dataTables_info {
        text-align: center;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.5rem 0.25rem !important;
        font-size: 0.8rem;
    }
    
    .pagination {
        justify-content: center !important;
    }
    
    .pagination .page-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Loading state */
.admin-table.loading {
    opacity: 0.6;
    pointer-events: none;
}

.admin-table.loading tbody::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #011a2d;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
</style>
