<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Re-add tags column to video table
 */
final class Version20250705000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Re-add tags JSON column to video table to support video tagging functionality';
    }

    public function up(Schema $schema): void
    {
        // Add tags column back to video table
        $this->addSql('ALTER TABLE video ADD tags JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove tags column from video table
        $this->addSql('ALTER TABLE video DROP tags');
    }
}
