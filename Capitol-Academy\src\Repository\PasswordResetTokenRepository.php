<?php

namespace App\Repository;

use App\Entity\PasswordResetToken;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PasswordResetToken>
 */
class PasswordResetTokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PasswordResetToken::class);
    }

    /**
     * Find a valid token for a user
     */
    public function findValidTokenForUser(User $user, string $token): ?PasswordResetToken
    {
        return $this->createQueryBuilder('prt')
            ->andWhere('prt.user = :user')
            ->andWhere('prt.token = :token')
            ->andWhere('prt.isUsed = false')
            ->andWhere('prt.expiresAt > :now')
            ->setParameter('user', $user)
            ->setParameter('token', $token)
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find the latest valid token for a user
     */
    public function findLatestValidTokenForUser(User $user): ?PasswordResetToken
    {
        return $this->createQueryBuilder('prt')
            ->andWhere('prt.user = :user')
            ->andWhere('prt.isUsed = false')
            ->andWhere('prt.expiresAt > :now')
            ->setParameter('user', $user)
            ->setParameter('now', new \DateTimeImmutable())
            ->orderBy('prt.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Invalidate all existing tokens for a user
     */
    public function invalidateAllTokensForUser(User $user): void
    {
        $this->createQueryBuilder('prt')
            ->update()
            ->set('prt.isUsed', 'true')
            ->andWhere('prt.user = :user')
            ->andWhere('prt.isUsed = false')
            ->setParameter('user', $user)
            ->getQuery()
            ->execute();
    }

    /**
     * Clean up expired tokens (for maintenance)
     */
    public function cleanupExpiredTokens(): int
    {
        return $this->createQueryBuilder('prt')
            ->delete()
            ->andWhere('prt.expiresAt < :now')
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->execute();
    }

    /**
     * Count active tokens for a user (rate limiting)
     */
    public function countActiveTokensForUser(User $user): int
    {
        return $this->createQueryBuilder('prt')
            ->select('COUNT(prt.id)')
            ->andWhere('prt.user = :user')
            ->andWhere('prt.isUsed = false')
            ->andWhere('prt.expiresAt > :now')
            ->setParameter('user', $user)
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->getSingleScalarResult();
    }
}
