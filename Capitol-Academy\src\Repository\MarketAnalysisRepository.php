<?php

namespace App\Repository;

use App\Entity\MarketAnalysis;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MarketAnalysis>
 *
 * @method MarketAnalysis|null find($id, $lockMode = null, $lockVersion = null)
 * @method MarketAnalysis|null findOneBy(array $criteria, array $orderBy = null)
 * @method MarketAnalysis[]    findAll()
 * @method MarketAnalysis[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MarketAnalysisRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MarketAnalysis::class);
    }

    /**
     * Find published articles with optional filtering
     */
    public function findPublished(?string $assetType = null, int $limit = 10, int $offset = 0): array
    {
        $qb = $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('ma.publishDate', 'DESC');

        if ($assetType) {
            $qb->andWhere('ma.assetType = :assetType')
               ->setParameter('assetType', $assetType);
        }

        return $qb->setFirstResult($offset)
                  ->setMaxResults($limit)
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Find published articles with filtering and sorting
     */
    public function findPublishedWithSorting(?string $assetType = null, string $sortBy = 'date', string $sortOrder = 'desc', int $limit = 10, int $offset = 0): array
    {
        $qb = $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true);

        if ($assetType) {
            $qb->andWhere('ma.assetType = :assetType')
               ->setParameter('assetType', $assetType);
        }

        // Add sorting
        $sortField = match($sortBy) {
            'title' => 'ma.title',
            'category' => 'ma.assetType',
            'author' => 'ma.author',
            'views' => 'ma.views',
            'date' => 'ma.publishDate',
            default => 'ma.publishDate'
        };

        $sortDirection = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';
        $qb->orderBy($sortField, $sortDirection);

        // Add secondary sort by date if not already sorting by date
        if ($sortBy !== 'date') {
            $qb->addOrderBy('ma.publishDate', 'DESC');
        }

        return $qb->setFirstResult($offset)
                  ->setMaxResults($limit)
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Count published articles with optional filtering
     */
    public function countPublished(?string $assetType = null): int
    {
        $qb = $this->createQueryBuilder('ma')
            ->select('COUNT(ma.id)')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true);

        if ($assetType) {
            $qb->andWhere('ma.assetType = :assetType')
               ->setParameter('assetType', $assetType);
        }

        return $qb->getQuery()->getSingleScalarResult();
    }



    /**
     * Find articles with search functionality for admin
     */
    public function findWithSearch(string $search = ''): array
    {
        $qb = $this->createQueryBuilder('ma')
            ->orderBy('ma.createdAt', 'DESC');

        if (!empty($search)) {
            $qb->where('ma.title LIKE :search')
               ->orWhere('ma.excerpt LIKE :search')
               ->orWhere('ma.author LIKE :search')
               ->orWhere('ma.assetType LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Search articles by title for public search
     */
    public function searchByTitle(string $query, int $limit = 10): array
    {
        return $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->andWhere('ma.title LIKE :query OR ma.excerpt LIKE :query')
            ->setParameter('isActive', true)
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('ma.publishDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get statistics for admin dashboard
     */
    public function getMarketAnalysisStats(): array
    {
        $total = $this->count([]);
        $published = $this->count(['isActive' => true]);
        $draft = $this->count(['isActive' => false]);

        // Recent articles (last 30 days)
        $recent = $this->createQueryBuilder('ma')
            ->select('COUNT(ma.id)')
            ->where('ma.createdAt >= :date')
            ->setParameter('date', new \DateTimeImmutable('-30 days'))
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total' => $total,
            'published' => $published,
            'draft' => $draft,
            'recent' => $recent,
            'archived' => 0 // No archived status anymore
        ];
    }

    /**
     * Get articles by asset type for statistics
     */
    public function getByAssetTypeStats(): array
    {
        $result = $this->createQueryBuilder('ma')
            ->select('ma.assetType, COUNT(ma.id) as count')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true)
            ->groupBy('ma.assetType')
            ->getQuery()
            ->getResult();

        $stats = [];
        foreach ($result as $row) {
            $stats[$row['assetType']] = $row['count'];
        }

        return $stats;
    }

    /**
     * Find recent articles for homepage or widgets
     */
    public function findRecent(int $limit = 5): array
    {
        return $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('ma.publishDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find featured articles (returns empty array since featured functionality is removed)
     */
    public function findFeatured(int $limit = 5): array
    {
        // Return empty array since featured functionality is removed
        return [];
    }

    /**
     * Find articles by multiple asset types
     */
    public function findByAssetTypes(array $assetTypes, int $limit = 10, int $offset = 0): array
    {
        return $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->andWhere('ma.assetType IN (:assetTypes)')
            ->setParameter('isActive', true)
            ->setParameter('assetTypes', $assetTypes)
            ->orderBy('ma.publishDate', 'DESC')
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find related articles based on asset type
     */
    public function findRelated(MarketAnalysis $article, int $limit = 3): array
    {
        return $this->createQueryBuilder('ma')
            ->where('ma.isActive = :isActive')
            ->andWhere('ma.assetType = :assetType')
            ->andWhere('ma.id != :currentId')
            ->setParameter('isActive', true)
            ->setParameter('assetType', $article->getAssetType())
            ->setParameter('currentId', $article->getId())
            ->orderBy('ma.publishDate', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get available asset types with article counts
     */
    public function getAssetTypesWithCounts(): array
    {
        $result = $this->createQueryBuilder('ma')
            ->select('ma.assetType, COUNT(ma.id) as articleCount')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true)
            ->groupBy('ma.assetType')
            ->orderBy('articleCount', 'DESC')
            ->getQuery()
            ->getResult();

        $assetTypes = [];
        foreach ($result as $row) {
            $assetTypes[$row['assetType']] = [
                'label' => $this->getAssetTypeLabel($row['assetType']),
                'count' => $row['articleCount']
            ];
        }

        return $assetTypes;
    }

    /**
     * Helper method to get asset type labels
     */
    private function getAssetTypeLabel(string $assetType): string
    {
        return match($assetType) {
            'stocks' => 'Stocks',
            'forex' => 'Forex',
            'crypto' => 'Crypto',
            'crude_oil' => 'Crude Oil',
            'gold' => 'Gold',
            'commodities' => 'Commodities',
            default => 'Unknown'
        };
    }

    /**
     * Find articles for sitemap generation
     */
    public function findForSitemap(): array
    {
        return $this->createQueryBuilder('ma')
            ->select('ma.id, ma.title, ma.updatedAt')
            ->where('ma.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('ma.updatedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find article by slug (title-slug-author-slug format)
     */
    public function findBySlug(string $slug): ?MarketAnalysis
    {
        // Split the slug to get title and author parts
        $parts = explode('-', $slug);
        if (count($parts) < 2) {
            return null;
        }

        // Try to find by matching the generated slug
        $articles = $this->findAll();
        foreach ($articles as $article) {
            if ($article->getSlug() === $slug) {
                return $article;
            }
        }

        return null;
    }
}
