<?php

namespace App\Form;

use App\Entity\Contact;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class ContactType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('fullName', TextType::class, [
                'label' => 'Full Name',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter your full name'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your full name']),
                    new Length(['max' => 200])
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => 'Email',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '<EMAIL>'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Please enter your email address']),
                    new Email(['message' => 'Please enter a valid email address'])
                ]
            ])
            ->add('country', ChoiceType::class, [
                'label' => 'Country',
                'placeholder' => 'Select your country',
                'attr' => [
                    'class' => 'form-control'
                ],
                'choices' => $this->getCountries(),
                'required' => false
            ])
            ->add('subject', TextType::class, [
                'label' => 'Subject',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Subject of your inquiry',
                    'readonly' => false // Will be set to true in templates for specific forms
                ],
                'constraints' => [
                    new Length(['max' => 255])
                ]
            ])
            ->add('message', TextareaType::class, [
                'label' => 'Message',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 5,
                    'placeholder' => 'Your message or inquiry'
                ]
            ])
            ->add('submit', SubmitType::class, [
                'label' => '<i class="fas fa-paper-plane me-2"></i>Send Message',
                'label_html' => true,
                'attr' => [
                    'class' => 'btn btn-primary btn-lg btn-enhanced'
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Contact::class,
        ]);
    }

    private function getCountries(): array
    {
        return [
            'Afghanistan' => 'Afghanistan',
            'Albania' => 'Albania',
            'Algeria' => 'Algeria',
            'Argentina' => 'Argentina',
            'Australia' => 'Australia',
            'Austria' => 'Austria',
            'Bahrain' => 'Bahrain',
            'Bangladesh' => 'Bangladesh',
            'Belgium' => 'Belgium',
            'Brazil' => 'Brazil',
            'Canada' => 'Canada',
            'China' => 'China',
            'Denmark' => 'Denmark',
            'Egypt' => 'Egypt',
            'Finland' => 'Finland',
            'France' => 'France',
            'Germany' => 'Germany',
            'Greece' => 'Greece',
            'India' => 'India',
            'Indonesia' => 'Indonesia',
            'Iraq' => 'Iraq',
            'Ireland' => 'Ireland',
            'Italy' => 'Italy',
            'Japan' => 'Japan',
            'Jordan' => 'Jordan',
            'Kuwait' => 'Kuwait',
            'Lebanon' => 'Lebanon',
            'Libya' => 'Libya',
            'Malaysia' => 'Malaysia',
            'Morocco' => 'Morocco',
            'Netherlands' => 'Netherlands',
            'Norway' => 'Norway',
            'Pakistan' => 'Pakistan',
            'Poland' => 'Poland',
            'Qatar' => 'Qatar',
            'Russia' => 'Russia',
            'Saudi Arabia' => 'Saudi Arabia',
            'Singapore' => 'Singapore',
            'South Africa' => 'South Africa',
            'Spain' => 'Spain',
            'Sweden' => 'Sweden',
            'Switzerland' => 'Switzerland',
            'Tunisia' => 'Tunisia',
            'Turkey' => 'Turkey',
            'Ukraine' => 'Ukraine',
            'United Arab Emirates' => 'United Arab Emirates',
            'United Kingdom' => 'United Kingdom',
            'United States' => 'United States',
            'Yemen' => 'Yemen'
        ];
    }
}
