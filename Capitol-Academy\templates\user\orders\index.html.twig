{% extends 'base.html.twig' %}

{% block title %}My Orders - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .orders-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .page-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }

        .stats-cards {
            margin-bottom: 40px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 1.5rem;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .orders-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .orders-header {
            background: #f8f9fa;
            padding: 25px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .orders-content {
            padding: 30px;
        }

        .order-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .order-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-color: #011a2d;
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .order-number {
            font-size: 1.1rem;
            font-weight: 600;
            color: #011a2d;
        }

        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .order-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .order-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .order-items {
            margin-bottom: 15px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 3px;
            font-size: 0.95rem;
        }

        .item-type {
            background: #e9ecef;
            color: #6c757d;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            text-transform: uppercase;
        }

        .item-price {
            font-weight: 600;
            color: #495057;
        }

        .order-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 6px 15px;
            font-size: 0.85rem;
        }

        .empty-orders {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-orders i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        @media (max-width: 768px) {
            .orders-section {
                padding: 40px 0;
            }
            
            .page-header {
                padding: 30px 0;
            }
            
            .orders-content {
                padding: 20px;
            }
            
            .order-card {
                padding: 20px;
            }
            
            .order-header,
            .order-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .stats-cards .col-md-3 {
                margin-bottom: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="mb-3">
                        <i class="fas fa-shopping-bag me-3"></i>My Orders
                    </h1>
                    <p class="lead mb-0">Track your purchases and access your content</p>
                </div>
                <div class="col-lg-4 text-end">
                    <a href="{{ path('app_premium_videos') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>Browse More Videos
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="orders-section">
        <div class="container">
            <!-- Statistics Cards -->
            <div class="stats-cards">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stats-number">{{ orders|length }}</div>
                            <div class="stats-label">Total Orders</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-video"></i>
                            </div>
                            <div class="stats-number">{{ access_stats.total_videos_accessed }}</div>
                            <div class="stats-label">Videos Owned</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stats-number">{{ access_stats.total_video_views }}</div>
                            <div class="stats-label">Total Views</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <div class="stats-number">{{ access_stats.purchased_courses }}</div>
                            <div class="stats-label">Courses Enrolled</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders List -->
            <div class="orders-container">
                <div class="orders-header">
                    <h4 class="mb-0">
                        <i class="fas fa-list me-2"></i>Order History
                    </h4>
                </div>

                <div class="orders-content">
                    {% if orders|length > 0 %}
                        {% for order in orders %}
                            <div class="order-card">
                                <div class="order-header">
                                    <div class="order-number">
                                        Order #{{ order.orderNumber }}
                                    </div>
                                    <div class="order-status status-{{ order.paymentStatus }}">
                                        {{ order.statusLabel }}
                                    </div>
                                </div>

                                <div class="order-meta">
                                    <div class="order-date">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ order.createdAt|date('M d, Y \\a\\t g:i A') }}
                                    </div>
                                    <div class="order-total">
                                        {{ order.formattedTotalPrice }}
                                    </div>
                                </div>

                                <div class="order-items">
                                    {% for item in order.items %}
                                        <div class="order-item">
                                            <div class="item-info">
                                                <h6>{{ item.title }}</h6>
                                                <span class="item-type">{{ item.type|replace({'_': ' '})|title }}</span>
                                                {% if item.quantity > 1 %}
                                                    <small class="text-muted ms-2">Qty: {{ item.quantity }}</small>
                                                {% endif %}
                                            </div>
                                            <div class="item-price">
                                                ${{ item.subtotal|number_format(2) }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <div class="order-actions">
                                    <a href="{{ path('app_user_order_show', {orderNumber: order.orderNumber}) }}" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    
                                    {% if order.isCompleted %}
                                        <a href="{{ path('app_premium_videos') }}" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-play me-1"></i>Access Content
                                        </a>
                                    {% endif %}
                                    
                                    {% if order.paymentGateway == 'paypal' and order.paypalTransactionId %}
                                        <small class="text-muted align-self-center">
                                            PayPal ID: {{ order.paypalTransactionId }}
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-orders">
                            <i class="fas fa-shopping-bag"></i>
                            <h3>No Orders Yet</h3>
                            <p class="mb-4">You haven't made any purchases yet. Start exploring our premium content!</p>
                            <a href="{{ path('app_premium_videos') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-video me-2"></i>Browse Premium Videos
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Add hover effects to order cards
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
{% endblock %}
