<?php

namespace App\Controller;

use App\Entity\Order;
use App\Entity\User;
use App\Repository\OrderRepository;
use App\Service\CartService;
use App\Service\PayPalService;
use App\Service\AccessControlService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Psr\Log\LoggerInterface;

#[Route('/checkout')]
#[IsGranted('ROLE_USER')]
class CheckoutController extends AbstractController
{
    private CartService $cartService;
    private PayPalService $payPalService;
    private AccessControlService $accessControlService;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    public function __construct(
        CartService $cartService,
        PayPalService $payPalService,
        AccessControlService $accessControlService,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->cartService = $cartService;
        $this->payPalService = $payPalService;
        $this->accessControlService = $accessControlService;
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    #[Route('', name: 'app_checkout', methods: ['GET'])]
    public function index(): Response
    {
        if ($this->cartService->isEmpty()) {
            $this->addFlash('warning', 'Your cart is empty. Please add items before checkout.');
            return $this->redirectToRoute('app_cart');
        }

        // Validate cart items
        $removedItems = $this->cartService->validateCart();
        if (!empty($removedItems)) {
            $this->addFlash('warning', count($removedItems) . ' item(s) were removed from your cart because they are no longer available.');
        }

        $cart = $this->cartService->getCart();
        $user = $this->getUser();

        return $this->render('checkout/index.html.twig', [
            'cart' => $cart,
            'user' => $user,
            'removed_items' => $removedItems,
        ]);
    }

    #[Route('/create-order', name: 'app_checkout_create_order', methods: ['POST'])]
    public function createOrder(): JsonResponse
    {
        try {
            if ($this->cartService->isEmpty()) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Cart is empty'
                ], 400);
            }

            $user = $this->getUser();
            if (!$user instanceof User) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Create order entity
            $order = new Order();
            $order->setUser($user);
            $order->setItems($this->cartService->getOrderItems());
            $order->setTotalPrice((string)$this->cartService->getTotal());
            $order->setPaymentGateway(Order::GATEWAY_PAYPAL);

            $this->entityManager->persist($order);
            $this->entityManager->flush();

            // Create PayPal order
            $paypalOrderData = $this->payPalService->createOrder($order);

            if (!$paypalOrderData) {
                $this->logger->error('Failed to create PayPal order', [
                    'order_id' => $order->getId(),
                    'user_id' => $user->getId()
                ]);

                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to create PayPal order. Please try again.'
                ], 500);
            }

            // Store PayPal order ID
            $order->setPaymentDetails([
                'paypal_order_id' => $paypalOrderData['id'],
                'paypal_status' => $paypalOrderData['status'] ?? 'CREATED'
            ]);

            $this->entityManager->flush();

            $this->logger->info('Order created successfully', [
                'order_id' => $order->getId(),
                'paypal_order_id' => $paypalOrderData['id'],
                'user_id' => $user->getId()
            ]);

            return new JsonResponse([
                'success' => true,
                'order_id' => $paypalOrderData['id'],
                'order_number' => $order->getOrderNumber()
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Error creating order', [
                'error' => $e->getMessage(),
                'user_id' => $this->getUser()?->getId()
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while creating your order. Please try again.'
            ], 500);
        }
    }

    #[Route('/capture-order', name: 'app_checkout_capture_order', methods: ['POST'])]
    public function captureOrder(Request $request, OrderRepository $orderRepository): JsonResponse
    {
        try {
            $paypalOrderId = $request->request->get('order_id');

            if (!$paypalOrderId) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'PayPal order ID is required'
                ], 400);
            }

            // Find our order by PayPal order ID
            $order = $orderRepository->createQueryBuilder('o')
                ->where('JSON_EXTRACT(o.paymentDetails, \'$.paypal_order_id\') = :paypal_order_id')
                ->setParameter('paypal_order_id', $paypalOrderId)
                ->getQuery()
                ->getOneOrNullResult();

            if (!$order) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            // Capture PayPal payment
            $captureData = $this->payPalService->captureOrder($paypalOrderId);

            if (!$captureData) {
                $this->logger->error('Failed to capture PayPal payment', [
                    'paypal_order_id' => $paypalOrderId,
                    'order_id' => $order->getId()
                ]);

                return new JsonResponse([
                    'success' => false,
                    'message' => 'Failed to process payment. Please try again.'
                ], 500);
            }

            // Update order status
            $order->setPaymentStatus(Order::STATUS_COMPLETED);
            $order->setPaypalTransactionId($captureData['purchase_units'][0]['payments']['captures'][0]['id'] ?? null);
            $order->setPaymentDetails(array_merge($order->getPaymentDetails() ?? [], [
                'capture_data' => $captureData,
                'captured_at' => time()
            ]));

            $this->entityManager->flush();

            // Process order completion (grant access)
            $this->accessControlService->processOrderCompletion($order);

            // Clear cart
            $this->cartService->clear();

            $this->logger->info('Order completed successfully', [
                'order_id' => $order->getId(),
                'paypal_order_id' => $paypalOrderId,
                'transaction_id' => $order->getPaypalTransactionId()
            ]);

            return new JsonResponse([
                'success' => true,
                'order_number' => $order->getOrderNumber(),
                'redirect_url' => $this->generateUrl('app_checkout_success', ['orderNumber' => $order->getOrderNumber()])
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Error capturing order', [
                'error' => $e->getMessage(),
                'paypal_order_id' => $paypalOrderId ?? null
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'An error occurred while processing your payment. Please contact support.'
            ], 500);
        }
    }

    #[Route('/success/{orderNumber}', name: 'app_checkout_success', methods: ['GET'])]
    public function success(string $orderNumber, OrderRepository $orderRepository): Response
    {
        $order = $orderRepository->findByOrderNumber($orderNumber);

        if (!$order || $order->getUser() !== $this->getUser()) {
            throw $this->createNotFoundException('Order not found');
        }

        return $this->render('checkout/success.html.twig', [
            'order' => $order,
        ]);
    }

    #[Route('/cancel', name: 'app_checkout_cancel', methods: ['GET'])]
    public function cancel(): Response
    {
        $this->addFlash('warning', 'Payment was cancelled. Your items are still in your cart.');
        
        return $this->redirectToRoute('app_cart');
    }

    #[Route('/webhook/paypal', name: 'app_checkout_paypal_webhook', methods: ['POST'])]
    public function paypalWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $headers = $request->headers->all();

            // Verify webhook signature (implement in production)
            if (!$this->payPalService->verifyWebhookSignature($payload, $headers)) {
                $this->logger->warning('Invalid PayPal webhook signature');
                return new JsonResponse(['status' => 'invalid_signature'], 400);
            }

            $eventData = json_decode($payload, true);
            
            if (!$eventData) {
                return new JsonResponse(['status' => 'invalid_payload'], 400);
            }

            $processed = $this->payPalService->processWebhookEvent($eventData);

            return new JsonResponse([
                'status' => $processed ? 'processed' : 'ignored'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('PayPal webhook error', [
                'error' => $e->getMessage()
            ]);

            return new JsonResponse(['status' => 'error'], 500);
        }
    }
}
