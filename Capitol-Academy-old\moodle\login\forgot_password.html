<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Forgotten password "/>
    <title>Forgotten password</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/login/forgot_password.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
addonload(function() { if(el = document.getElementById('id_email')) el.focus(); });
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="login course-1 notloggedin dir-ltr lang-en_utf8" id="login-forgot_password">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">Forgotten password</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">You are here</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../../moodle.html">Learn Financial Markets</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> <a onclick="this.target='_top'" href="../../moodle/login/index.html">Login</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> Forgotten password</li></ul></div>
        <div class="navbutton">&nbsp;</div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix"><div class="generalbox boxwidthnormal boxaligncenter box">Your details must first be found in the user database. Please enter <strong>either</strong> your username or your registered email address in the appropriate box. There is no need to enter both.</div>
<form autocomplete="off" action="../../moodle/login/forgot_password.html" method="post" accept-charset="utf-8" id="mform1" class="mform">
	<div style="display: none;"><input name="MAX_FILE_SIZE" type="hidden" value="8388608"/>
<input name="sesskey" type="hidden" value="0DPee9mWig"/>
<input name="_qf__login_forgot_password_form" type="hidden" value="1"/>
</div>
	<fieldset class="clearfix">
		<legend class="ftoggler">Forgotten password</legend>
		<div class="advancedbutton"></div><div class="fcontainer clearfix">
		<div class="fitem"><div class="fitemtitle"><label for="id_username">Username </label></div><div class="felement ftext"><input name="username" type="text" id="id_username"/></div></div>
		<div class="fitem"><div class="fitemtitle"><label for="id_email">Email address </label></div><div class="felement ftext"><input name="email" type="text" id="id_email"/></div></div>
		</div></fieldset>
	<fieldset class="hidden"><div>
		<div class="fitem"><div class="fitemtitle"><div class="fgrouplabel"><label> </label></div></div><fieldset class="felement fgroup"><input name="submitbutton" value="OK" type="submit" id="id_submitbutton"/> <input name="cancel" value="Cancel" type="submit" onclick="skipClientValidation = true; return true;" id="id_cancel"/></fieldset></div>
		</div></fieldset>
</form>
<script type="text/javascript">
//<![CDATA[
var mform1items = Array();
lockoptionsallsetup('mform1');
//]]>
</script>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div><div class="homelink"><a href="../../moodle.html">Home</a></div></div>
</div>
</body>
</html>