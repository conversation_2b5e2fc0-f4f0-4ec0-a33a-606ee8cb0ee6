{% extends 'admin/base.html.twig' %}

{% block title %}Add New Admin - Capitol Academy Admin{% endblock %}

{% block page_title %}Add New Admin{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_profile') }}">Profile</a></li>
<li class="breadcrumb-item active">Add New Admin</li>
{% endblock %}

{% block content %}
<!-- Standardized Header Card -->
<div class="card border-0 shadow-lg mb-4">
    <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                    <i class="fas fa-user-shield mr-3" style="font-size: 2rem;"></i>
                    Create New Administrator
                </h2>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end align-items-center flex-wrap">
                    <!-- Back to Profile Button -->
                    <a href="{{ path('admin_profile') }}"
                       class="btn mb-2 mb-md-0"
                       style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                       onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                       onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <!-- Flash Messages -->
            {% for message in app.flashes('success') %}
                <div class="alert alert-success alert-dismissible fade show mx-3 mt-3">
                    <h6><i class="fas fa-check-circle me-2"></i>Success!</h6>
                    <p class="mb-0">{{ message }}</p>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}

            {% if errors is defined and errors|length > 0 %}
                <div class="alert alert-danger alert-dismissible fade show mx-3 mt-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
                    <ul class="mb-0">
                        {% for error in errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endif %}

            <form method="post" action="{{ path('admin_add_admin') }}" class="needs-validation" novalidate>
                <input type="hidden" name="_token" value="{{ csrf_token('admin_creation') }}">
                <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Admin Credentials Row -->
                            <div class="row">
                                <!-- Username -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="username" class="form-label">
                                            <i class="fas fa-user text-primary mr-1"></i>
                                            Username <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="username"
                                               name="username"
                                               placeholder="Enter username"
                                               required
                                               minlength="3"
                                               maxlength="50"
                                               pattern="^[a-zA-Z0-9_]+$"
                                               value="{{ formData.username ?? '' }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Username must be 3-50 characters, alphanumeric and underscore only.
                                        </div>
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope text-primary mr-1"></i>
                                            Email Address <span class="text-danger">*</span>
                                        </label>
                                        <input type="email"
                                               class="form-control enhanced-field"
                                               id="email"
                                               name="email"
                                               placeholder="Enter email address"
                                               required
                                               value="{{ formData.email ?? '' }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please provide a valid email address.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Name Information Row -->
                            <div class="row">
                                <!-- First Name -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="firstName" class="form-label">
                                            <i class="fas fa-user text-primary mr-1"></i>
                                            First Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="firstName"
                                               name="firstName"
                                               placeholder="Enter first name"
                                               required
                                               minlength="2"
                                               maxlength="50"
                                               value="{{ formData.firstName ?? '' }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            First name must be 2-50 characters.
                                        </div>
                                    </div>
                                </div>

                                <!-- Last Name -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="lastName" class="form-label">
                                            <i class="fas fa-user text-primary mr-1"></i>
                                            Last Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="lastName"
                                               name="lastName"
                                               placeholder="Enter last name"
                                               required
                                               minlength="2"
                                               maxlength="50"
                                               value="{{ formData.lastName ?? '' }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Last name must be 2-50 characters.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Password Information Row -->
                            <div class="row">
                                <!-- Password -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock text-primary mr-1"></i>
                                            Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password"
                                               class="form-control enhanced-field"
                                               id="password"
                                               name="password"
                                               placeholder="Enter password"
                                               required
                                               minlength="8"
                                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Password must be at least 8 characters with mixed case, numbers, and special characters.
                                        </div>
                                        <div id="password-strength" class="mt-2"></div>
                                    </div>
                                </div>

                                <!-- Confirm Password -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="confirmPassword" class="form-label">
                                            <i class="fas fa-lock text-primary mr-1"></i>
                                            Confirm Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password"
                                               class="form-control enhanced-field"
                                               id="confirmPassword"
                                               name="confirmPassword"
                                               placeholder="Confirm password"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Passwords must match.
                                        </div>
                                    </div>
                                </div>
                                </div>

                            <!-- Permissions Section -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-shield-alt text-primary mr-1"></i>
                                    Administrator Permissions & Access Control
                                </label>
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Select the permissions this administrator will have in the system
                                </small>

                                <div class="card" style="border-left: 4px solid #1e3c72; background: #f8f9fa; margin-top: 1rem;">
                                    <div class="card-body">
                                        <div class="row">
                                            <!-- User Management Permissions -->
                                            <div class="col-md-3">
                                                <div class="permission-group p-3 border rounded" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 2px solid #1e3c72 !important;">
                                                    <h6 class="text-primary mb-3 fw-bold">
                                                        <i class="fas fa-users me-2"></i>User Management
                                                    </h6>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="users_view" name="permissions[]" value="users.view"
                                                               {% if formData.permissions is defined and 'users.view' in formData.permissions %}checked{% endif %}>
                                                        <label class="form-check-label" for="users_view">View Users</label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="users_create" name="permissions[]" value="users.create">
                                                        <label class="form-check-label" for="users_create">Create Users</label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="users_edit" name="permissions[]" value="users.edit">
                                                        <label class="form-check-label" for="users_edit">Edit Users</label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="users_delete" name="permissions[]" value="users.delete">
                                                        <label class="form-check-label" for="users_delete">Delete Users</label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="checkbox" id="users_block" name="permissions[]" value="users.block">
                                                        <label class="form-check-label" for="users_block">Block/Unblock Users</label>
                                                    </div>
                                                </div>
                                            </div>

                                    <!-- Contact Management Permissions -->
                                    <div class="col-md-3">
                                        <div class="permission-group p-3 border rounded" style="background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%); border: 2px solid #ffc107 !important;">
                                            <h6 class="text-warning mb-3 fw-bold">
                                                <i class="fas fa-envelope me-2"></i>Contact Management
                                            </h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="contacts_view" name="permissions[]" value="contacts.view">
                                                <label class="form-check-label" for="contacts_view">View Contacts</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="contacts_process" name="permissions[]" value="contacts.process">
                                                <label class="form-check-label" for="contacts_process">Process Contacts</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="contacts_delete" name="permissions[]" value="contacts.delete">
                                                <label class="form-check-label" for="contacts_delete">Delete Contacts</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="contacts_export" name="permissions[]" value="contacts.export">
                                                <label class="form-check-label" for="contacts_export">Export Contact Data</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Course Management Permissions -->
                                    <div class="col-md-3">
                                        <div class="permission-group p-3 border rounded" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); border: 2px solid #28a745 !important;">
                                            <h6 class="text-success mb-3 fw-bold">
                                                <i class="fas fa-graduation-cap me-2"></i>Course Management
                                            </h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="courses_view" name="permissions[]" value="courses.view">
                                                <label class="form-check-label" for="courses_view">View Courses</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="courses_create" name="permissions[]" value="courses.create">
                                                <label class="form-check-label" for="courses_create">Create Courses</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="courses_edit" name="permissions[]" value="courses.edit">
                                                <label class="form-check-label" for="courses_edit">Edit Courses</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="courses_delete" name="permissions[]" value="courses.delete">
                                                <label class="form-check-label" for="courses_delete">Delete Courses</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="courses_modules" name="permissions[]" value="courses.modules">
                                                <label class="form-check-label" for="courses_modules">Manage Course Modules</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Plan Management Permissions -->
                                    <div class="col-md-3">
                                        <div class="permission-group p-3 border rounded" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 2px solid #2196f3 !important;">
                                            <h6 class="text-primary mb-3 fw-bold">
                                                <i class="fas fa-layer-group me-2"></i>Plan Management
                                            </h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="plans_view" name="permissions[]" value="plans.view">
                                                <label class="form-check-label" for="plans_view">View Plans</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="plans_create" name="permissions[]" value="plans.create">
                                                <label class="form-check-label" for="plans_create">Create Plans</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="plans_edit" name="permissions[]" value="plans.edit">
                                                <label class="form-check-label" for="plans_edit">Edit Plans</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="plans_delete" name="permissions[]" value="plans.delete">
                                                <label class="form-check-label" for="plans_delete">Delete Plans</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Admin Management Permissions -->
                                    <div class="col-md-3">
                                        <div class="permission-group p-3 border rounded" style="background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%); border: 2px solid #dc3545 !important;">
                                            <h6 class="text-danger mb-3 fw-bold">
                                                <i class="fas fa-users-cog me-2"></i>Admin Management
                                            </h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="admin_view" name="permissions[]" value="admin.view">
                                                <label class="form-check-label" for="admin_view">View Admins</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="admin_create" name="permissions[]" value="admin.create">
                                                <label class="form-check-label" for="admin_create">Create Admins</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="admin_edit" name="permissions[]" value="admin.edit">
                                                <label class="form-check-label" for="admin_edit">Edit Admins</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="admin_delete" name="permissions[]" value="admin.delete">
                                                <label class="form-check-label" for="admin_delete">Delete Admins</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="admin_permissions" name="permissions[]" value="admin.permissions">
                                                <label class="form-check-label" for="admin_permissions">Manage Admin Permissions</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Permission Sets -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card border-0" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px;">
                                            <div class="card-body p-3">
                                                <h6 class="mb-3 text-dark fw-bold">
                                                    <i class="fas fa-magic me-2 text-primary"></i>Quick Permission Templates
                                                </h6>
                                                <div class="d-flex flex-wrap gap-2">
                                                    <button type="button" class="btn btn-sm"
                                                            style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border: none; border-radius: 6px;"
                                                            onclick="selectAllPermissions()">
                                                        <i class="fas fa-check-double me-1"></i>Select All Permissions
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllPermissions()">
                                                        <i class="fas fa-times me-1"></i>Clear All
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="selectBasicPermissions()">
                                                        <i class="fas fa-user me-1"></i>Basic Admin
                                                    </button>
                                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectContentPermissions()">
                                                        <i class="fas fa-graduation-cap me-1"></i>Content Manager
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;">
                                <i class="fas fa-user-plus mr-2"></i>
                                Create Administrator
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_profile') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-user-plus mr-2"></i>Create Administrator';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Administrator...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Restore permission selections if form data exists
    {% if formData.permissions is defined %}
        {% for permission in formData.permissions %}
            $('input[value="{{ permission }}"]').prop('checked', true);
        {% endfor %}
    {% endif %}

    // Form enhancement animations
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });
            console.log('Form validation failed');
            showAdminNotification('Please fill in all required fields correctly.', 'error');
            return false;
        }

        // Check password confirmation
        const password = $('#password').val();
        const confirmPassword = $('#confirmPassword').val();
        if (password !== confirmPassword) {
            e.preventDefault();
            showAdminNotification('Passwords do not match.', 'error');
            return false;
        }

        console.log('Form validation passed, submitting...');

        // Show loading state but don't prevent form submission
        showLoadingState();

        // Allow form to submit naturally
        return true;
    });

    // Loading state functions
    function showLoadingState() {
        $('#submitBtn').prop('disabled', true);
        $('#submitBtnText').hide();
        $('#submitBtnLoading').show();
        // Don't disable form inputs to allow form submission

        // Set timeout to reset loading state if no response
        setTimeout(function() {
            if ($('#submitBtn').prop('disabled')) {
                hideLoadingState();
                showAdminNotification('Request timeout. Please try again.', 'error');
            }
        }, 30000);
    }

    function hideLoadingState() {
        $('#submitBtn').prop('disabled', false);
        $('#submitBtnText').show();
        $('#submitBtnLoading').hide();
        $('input, select, textarea').prop('disabled', false);
    }

    // Password confirmation validation
    $('#confirmPassword').on('input', function() {
        var password = $('#password').val();
        var confirmPassword = $(this).val();

        if (password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Password strength indicator
    $('#password').on('input', function() {
        const password = $(this).val();
        const strength = getPasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });

    function getPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }

    function updatePasswordStrengthIndicator(strength) {
        const colors = ['danger', 'danger', 'warning', 'info', 'success'];
        const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const widths = [20, 40, 60, 80, 100];

        $('#password-strength').html(`
            <div class="progress" style="height: 6px;">
                <div class="progress-bar bg-${colors[strength]}" role="progressbar"
                     style="width: ${widths[strength]}%" aria-valuenow="${widths[strength]}"
                     aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <small class="text-${colors[strength]}">Password Strength: ${texts[strength]}</small>
        `);
    }

    // Real-time validation feedback
    $('input[required]').on('blur', function() {
        if (this.checkValidity()) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).removeClass('is-valid').addClass('is-invalid');
        }
    });
});

// Permission management functions
function selectAllPermissions() {
    $('input[name="permissions[]"]').prop('checked', true);
    showAdminNotification('All permissions selected', 'success');
}

function clearAllPermissions() {
    $('input[name="permissions[]"]').prop('checked', false);
    showAdminNotification('All permissions cleared', 'info');
}

function selectBasicPermissions() {
    clearAllPermissions();
    $('#users_view, #contacts_view, #courses_view').prop('checked', true);
    showAdminNotification('Basic admin permissions selected', 'success');
}

function selectContentPermissions() {
    clearAllPermissions();
    $('#courses_view, #courses_create, #courses_edit, #courses_modules').prop('checked', true);
    showAdminNotification('Content manager permissions selected', 'success');
}

// Using showAdminNotification from base template
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
}

.enhanced-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

/* Permission group styling */
.permission-group {
    transition: all 0.3s ease;
}

.permission-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}
</style>
{% endblock %}
