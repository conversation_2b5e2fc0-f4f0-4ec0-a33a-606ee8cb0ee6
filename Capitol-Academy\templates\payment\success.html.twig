{% extends 'base.html.twig' %}

{% block title %}Payment Successful - Capitol Academy{% endblock %}

{% block meta_description %}Your payment has been processed successfully. Welcome to {{ course.title }} at Capitol Academy.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-success: #28a745;
    --ca-white: #ffffff;
}

.success-hero {
    background: linear-gradient(135deg, var(--ca-success) 0%, #20c997 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.success-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.success-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.payment-details {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--ca-primary);
}

.detail-value {
    color: #495057;
}

.course-card {
    background: var(--ca-light-gray);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    border: 2px solid #e9ecef;
}

.course-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 1rem;
}

.course-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-primary-custom {
    background: var(--ca-primary);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-primary-custom:hover {
    background: var(--ca-accent);
    border-color: var(--ca-accent);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-secondary-custom {
    background: white;
    color: var(--ca-primary);
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-secondary-custom:hover {
    background: var(--ca-primary);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.next-steps {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.next-steps h4 {
    color: #155724;
    margin-bottom: 1rem;
}

.next-steps ul {
    color: #155724;
    margin: 0;
}

.next-steps li {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block body %}
<!-- Success Hero Section -->
<section class="success-hero">
    <div class="container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h1 class="success-title">Payment Successful!</h1>
        <p class="success-subtitle">Welcome to your new course at Capitol Academy</p>
    </div>
</section>

<!-- Payment Details Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Payment Details -->
                <div class="payment-details">
                    <h3 class="text-center mb-4" style="color: var(--ca-primary);">
                        <i class="fas fa-receipt mr-2"></i>
                        Payment Details
                    </h3>
                    
                    <div class="detail-row">
                        <span class="detail-label">Course:</span>
                        <span class="detail-value">{{ course.title }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">Course Code:</span>
                        <span class="detail-value">{{ course.code }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">Amount Paid:</span>
                        <span class="detail-value">${{ payment.amount|number_format(2) }} {{ payment.currency|upper }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">Payment ID:</span>
                        <span class="detail-value">{{ payment.stripePaymentId }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">{{ payment.createdAt|date('F j, Y g:i A') }}</span>
                    </div>
                </div>

                <!-- Course Information -->
                <div class="course-card">
                    <h3 class="course-title">{{ course.title }}</h3>
                    {% if course.description %}
                        <p class="course-description">{{ course.description|slice(0, 200) }}...</p>
                    {% endif %}
                    
                    <div class="next-steps">
                        <h4><i class="fas fa-list-check mr-2"></i>What's Next?</h4>
                        <ul>
                            <li>You now have full access to all course materials</li>
                            <li>Start with the first module and progress at your own pace</li>
                            <li>Access your course anytime from your dashboard</li>
                            <li>Contact support if you need any assistance</li>
                        </ul>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{{ path('course_show', {'code': course.code}) }}" class="btn-primary-custom">
                        <i class="fas fa-play mr-2"></i>
                        Start Learning
                    </a>
                    
                    <a href="{{ path('app_home') }}" class="btn-secondary-custom">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
