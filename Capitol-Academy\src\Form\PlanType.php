<?php

namespace App\Form;

use App\Entity\Plan;
use App\Entity\Video;
use App\Repository\VideoRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class PlanType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Plan Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter plan title'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Enter plan description'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])

            ->add('price', MoneyType::class, [
                'label' => 'Price',
                'currency' => 'USD',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Price is required']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ]
            ])
            ->add('duration', TextType::class, [
                'label' => 'Access Duration (Days)',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., 30, 60, 90 (leave empty for lifetime access)',
                    'type' => 'number',
                    'min' => '1'
                ],
                'help' => 'Number of days after payment when plan access expires. Leave empty for lifetime access.'
            ])
            ->add('videos', EntityType::class, [
                'class' => Video::class,
                'choice_label' => function (Video $video) {
                    return $video->getTitle();
                },
                'multiple' => true,
                'expanded' => true,
                'attr' => [
                    'class' => 'video-selection'
                ],
                'query_builder' => function (VideoRepository $repository) {
                    return $repository->createQueryBuilder('v')
                        ->where('v.isActive = :active')
                        ->setParameter('active', true)
                        ->orderBy('v.category', 'ASC')
                        ->addOrderBy('v.title', 'ASC');
                },
                'constraints' => [
                    new NotBlank(['message' => 'At least one video must be selected'])
                ],
                'help' => 'Select videos to include in this plan. Duration will be calculated automatically.'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Plan::class,
        ]);
    }


}
