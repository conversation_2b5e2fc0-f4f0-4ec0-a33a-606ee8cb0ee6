<?php

namespace App\Repository;

use App\Entity\Plan;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Plan>
 */
class PlanRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Plan::class);
    }

    /**
     * Find all active plans
     */
    public function findActivePlans(): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('p.category', 'ASC')
            ->addOrderBy('p.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find plan by code
     */
    public function findByCode(string $code): ?Plan
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.code = :code')
            ->andWhere('p.is_active = :active')
            ->setParameter('code', $code)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find plans by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.category = :category')
            ->andWhere('p.is_active = :active')
            ->setParameter('category', $category)
            ->setParameter('active', true)
            ->orderBy('p.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find plans by level
     */
    public function findByLevel(string $level): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.level = :level')
            ->andWhere('p.is_active = :active')
            ->setParameter('level', $level)
            ->setParameter('active', true)
            ->orderBy('p.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search plans by title or description
     */
    public function searchPlans(string $query): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.title LIKE :query OR p.description LIKE :query')
            ->andWhere('p.is_active = :active')
            ->setParameter('query', '%' . $query . '%')
            ->setParameter('active', true)
            ->orderBy('p.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find plans containing a specific video
     */
    public function findPlansContainingVideo(int $videoId): array
    {
        return $this->createQueryBuilder('p')
            ->join('p.videos', 'v')
            ->andWhere('v.id = :videoId')
            ->andWhere('p.is_active = :active')
            ->setParameter('videoId', $videoId)
            ->setParameter('active', true)
            ->orderBy('p.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find featured plans (highest rated)
     */
    public function findFeaturedPlans(int $limit = 6): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('p.average_rating', 'DESC')
            ->addOrderBy('p.total_reviews', 'DESC')
            ->addOrderBy('p.enrolled_count', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find popular plans (most enrolled)
     */
    public function findPopularPlans(int $limit = 6): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('p.enrolled_count', 'DESC')
            ->addOrderBy('p.view_count', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find recent plans
     */
    public function findRecentPlans(int $limit = 6): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('p.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get plan statistics
     */
    public function getPlanStatistics(): array
    {
        $qb = $this->createQueryBuilder('p');
        
        return [
            'total_plans' => $qb->select('COUNT(p.id)')
                ->andWhere('p.is_active = :active')
                ->setParameter('active', true)
                ->getQuery()
                ->getSingleScalarResult(),
            
            'total_enrollments' => $qb->select('SUM(p.enrolled_count)')
                ->resetDQLPart('select')
                ->andWhere('p.is_active = :active')
                ->setParameter('active', true)
                ->getQuery()
                ->getSingleScalarResult() ?? 0,
                
            'average_rating' => $qb->select('AVG(p.average_rating)')
                ->resetDQLPart('select')
                ->andWhere('p.is_active = :active')
                ->andWhere('p.total_reviews > 0')
                ->setParameter('active', true)
                ->getQuery()
                ->getSingleScalarResult() ?? 0,
        ];
    }

    /**
     * Find plans with pagination
     */
    public function findPlansWithPagination(int $page = 1, int $limit = 10, ?string $search = null, ?string $category = null): array
    {
        $qb = $this->createQueryBuilder('p')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true);

        if ($search) {
            $qb->andWhere('p.title LIKE :search OR p.description LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        if ($category) {
            $qb->andWhere('p.category = :category')
               ->setParameter('category', $category);
        }

        $qb->orderBy('p.created_at', 'DESC')
           ->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        return $qb->getQuery()->getResult();
    }

    /**
     * Count total plans for pagination
     */
    public function countPlans(?string $search = null, ?string $category = null): int
    {
        $qb = $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->andWhere('p.is_active = :active')
            ->setParameter('active', true);

        if ($search) {
            $qb->andWhere('p.title LIKE :search OR p.description LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        if ($category) {
            $qb->andWhere('p.category = :category')
               ->setParameter('category', $category);
        }

        return $qb->getQuery()->getSingleScalarResult();
    }
}
