<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Course Enhancement Migration - Remove slug/content fields and add enrollment/rating fields
 */
final class Version20241202000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove slug and content fields from course and course_module tables, add enrollment and rating fields to course table, create course_review table';
    }

    public function up(Schema $schema): void
    {
        // Create course_review table
        $this->addSql('CREATE TABLE course_review (
            id INT AUTO_INCREMENT NOT NULL, 
            course_id INT NOT NULL, 
            user_id INT NOT NULL, 
            rating SMALLINT NOT NULL, 
            comment LONGTEXT DEFAULT NULL, 
            is_certified TINYINT(1) NOT NULL, 
            is_approved TINYINT(1) NOT NULL, 
            is_featured TINYINT(1) NOT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            INDEX IDX_D77B408B591CC992 (course_id), 
            INDEX IDX_D77B408BA76ED395 (user_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints for course_review
        $this->addSql('ALTER TABLE course_review ADD CONSTRAINT FK_D77B408B591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE course_review ADD CONSTRAINT FK_D77B408BA76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)');

        // Add new fields to course table
        $this->addSql('ALTER TABLE course ADD enrolled_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE course ADD active_enrollments INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE course ADD completed_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE course ADD certified_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE course ADD average_rating NUMERIC(3, 2) NOT NULL DEFAULT 0.00');
        $this->addSql('ALTER TABLE course ADD total_reviews INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE course ADD view_count INT NOT NULL DEFAULT 0');

        // Remove slug and content fields from course table
        $this->addSql('ALTER TABLE course DROP COLUMN content');
        $this->addSql('ALTER TABLE course DROP COLUMN slug');

        // Remove slug and content fields from course_module table
        $this->addSql('ALTER TABLE course_module DROP COLUMN content');
        $this->addSql('ALTER TABLE course_module DROP COLUMN slug');
    }

    public function down(Schema $schema): void
    {
        // Drop course_review table
        $this->addSql('DROP TABLE course_review');

        // Remove new fields from course table
        $this->addSql('ALTER TABLE course DROP COLUMN enrolled_count');
        $this->addSql('ALTER TABLE course DROP COLUMN active_enrollments');
        $this->addSql('ALTER TABLE course DROP COLUMN completed_count');
        $this->addSql('ALTER TABLE course DROP COLUMN certified_count');
        $this->addSql('ALTER TABLE course DROP COLUMN average_rating');
        $this->addSql('ALTER TABLE course DROP COLUMN total_reviews');
        $this->addSql('ALTER TABLE course DROP COLUMN view_count');

        // Add back slug and content fields to course table
        $this->addSql('ALTER TABLE course ADD content LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE course ADD slug VARCHAR(255) DEFAULT NULL');

        // Add back slug and content fields to course_module table
        $this->addSql('ALTER TABLE course_module ADD content LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE course_module ADD slug VARCHAR(255) DEFAULT NULL');
    }
}
