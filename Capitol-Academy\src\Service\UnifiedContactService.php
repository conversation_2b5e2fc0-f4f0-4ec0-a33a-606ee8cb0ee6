<?php

namespace App\Service;

use App\Entity\Contact;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;

class UnifiedContactService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private IpAddressService $ipAddressService
    ) {}

    /**
     * Process a unified contact form submission
     * 
     * @param array $formData Array containing form data
     * @param string $sourcePage Source page identifier
     * @param Request $request HTTP request object
     * @return Contact The created contact entity
     */
    public function processContactForm(array $formData, string $sourcePage, Request $request): Contact
    {
        $contact = new Contact();

        // Handle name field - use fullName
        if (isset($formData['name'])) {
            $contact->setFullName(trim($formData['name']));
        } elseif (isset($formData['fullName'])) {
            $contact->setFullName($formData['fullName']);
        } elseif (isset($formData['firstName']) && isset($formData['lastName'])) {
            // Handle separate first/last name fields by combining them
            $fullName = trim($formData['firstName'] . ' ' . $formData['lastName']);
            $contact->setFullName($fullName);
        } elseif (isset($formData['firstName'])) {
            // Handle only first name
            $contact->setFullName($formData['firstName']);
        }

        // Set email
        if (isset($formData['email'])) {
            $contact->setEmail($formData['email']);
        }

        // Set message
        if (isset($formData['message'])) {
            $contact->setMessage($formData['message']);
        }

        // Phone field is no longer supported in the Contact entity

        // Set country using IP geolocation or provided value
        if (isset($formData['country'])) {
            $contact->setCountry($formData['country']);
        } else {
            $country = $this->getCountryFromIp($request);
            $contact->setCountry($country);
        }

        // Set subject based on source page
        $subject = $this->generateSubjectFromSource($sourcePage);
        $contact->setSubject($subject);

        // Set source page
        $contact->setSourcePage($sourcePage);

        // Set IP address
        $ipAddress = $this->ipAddressService->getClientIpAddress($request);
        $contact->setIpAddress($ipAddress);

        // Save to database
        $this->entityManager->persist($contact);
        $this->entityManager->flush();

        return $contact;
    }

    /**
     * Get country from IP address with fallback
     */
    private function getCountryFromIp(Request $request): string
    {
        try {
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            
            // Simple IP geolocation using a free service
            // In production, you might want to use a more reliable service
            $response = @file_get_contents("http://ip-api.com/json/{$ipAddress}");
            
            if ($response) {
                $data = json_decode($response, true);
                if (isset($data['country']) && $data['status'] === 'success') {
                    return $data['country'];
                }
            }
        } catch (\Exception $e) {
            // Log error if needed
        }

        return 'Unknown';
    }

    /**
     * Generate subject based on source page
     */
    private function generateSubjectFromSource(string $sourcePage): string
    {
        return match ($sourcePage) {
            'contact' => 'Contact Us Inquiry - Capitol Academy',
            'about' => 'About Us Inquiry - Capitol Academy',
            'partnership' => 'Partnership Inquiry - Capitol Academy',
            'instructor' => 'Instructor Application - Capitol Academy',
            'registration' => 'Student Registration Application - Capitol Academy',
            default => 'General Inquiry - Capitol Academy'
        };
    }

    /**
     * Validate form data
     */
    public function validateFormData(array $formData): array
    {
        $errors = [];

        // Check required fields
        if (empty($formData['name']) && (empty($formData['firstName']) || empty($formData['lastName']))) {
            $errors[] = 'Name is required';
        }

        if (empty($formData['email'])) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }

        if (empty($formData['message'])) {
            $errors[] = 'Message is required';
        }

        return $errors;
    }
}
