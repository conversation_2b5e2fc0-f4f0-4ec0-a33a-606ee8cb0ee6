<?php

namespace App\Controller;

use App\Entity\MarketAnalysis;
use App\Repository\MarketAnalysisRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MarketAnalysisController extends AbstractController
{
    public function __construct(
        private MarketAnalysisRepository $marketAnalysisRepository
    ) {}

    #[Route('/trading-tools/market-analysis', name: 'app_market_analysis', methods: ['GET'])]
    public function index(Request $request): Response
    {
        $assetType = $request->query->get('asset_type');
        $sortBy = $request->query->get('sort_by', 'date');
        $sortOrder = $request->query->get('sort_order', 'desc');
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = 6; // Articles per page
        $offset = ($page - 1) * $limit;

        // Get published articles with filtering and sorting
        $articles = $this->marketAnalysisRepository->findPublishedWithSorting($assetType, $sortBy, $sortOrder, $limit, $offset);
        $totalArticles = $this->marketAnalysisRepository->countPublished($assetType);
        $totalPages = ceil($totalArticles / $limit);

        // Get featured articles for sidebar
        $featuredArticles = $this->marketAnalysisRepository->findFeatured(3);

        // Get asset types with counts for filter bar
        $assetTypesWithCounts = $this->marketAnalysisRepository->getAssetTypesWithCounts();

        // If this is an AJAX request, return JSON
        if ($request->isXmlHttpRequest()) {
            return $this->json([
                'success' => true,
                'articles' => array_map(function(MarketAnalysis $article) {
                    return [
                        'id' => $article->getId(),
                        'title' => $article->getTitle(),
                        'excerpt' => $article->getShortExcerpt(120),
                        'assetType' => $article->getAssetType(),
                        'assetTypeLabel' => $article->getAssetTypeLabel(),
                        'thumbnailUrl' => $article->getThumbnailUrl(),
                        'publishDate' => $article->getFormattedPublishDate(),
                        'author' => $article->getAuthor()
                    ];
                }, $articles),
                'pagination' => [
                    'currentPage' => $page,
                    'totalPages' => $totalPages,
                    'totalArticles' => $totalArticles,
                    'hasNext' => $page < $totalPages,
                    'hasPrev' => $page > 1
                ]
            ]);
        }

        return $this->render('market_analysis/index.html.twig', [
            'articles' => $articles,
            'featuredArticles' => $featuredArticles,
            'assetTypesWithCounts' => $assetTypesWithCounts,
            'currentAssetType' => $assetType,
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => $totalPages,
                'totalArticles' => $totalArticles,
                'hasNext' => $page < $totalPages,
                'hasPrev' => $page > 1
            ]
        ]);
    }

    #[Route('/trading-tools/market-analysis/{slug}', name: 'app_market_analysis_show_seo', methods: ['GET'])]
    public function showBySeoSlug(string $slug, EntityManagerInterface $entityManager): Response
    {
        // Check if user is logged in for "Read Analysis" functionality
        if (!$this->getUser()) {
            $this->addFlash('info', 'Please log in to read the full market analysis.');
            return $this->redirectToRoute('app_login');
        }

        // Find article by matching the slug
        $articles = $this->marketAnalysisRepository->findBy(['isActive' => true]);
        $article = null;

        foreach ($articles as $candidateArticle) {
            if ($candidateArticle->getSlug() === $slug) {
                $article = $candidateArticle;
                break;
            }
        }

        if (!$article) {
            throw $this->createNotFoundException('Article not found.');
        }

        // Increment view count
        $article->incrementViews();
        $entityManager->flush();

        // Get related articles
        $relatedArticles = $this->marketAnalysisRepository->findRelated($article, 3);

        return $this->render('market_analysis/show.html.twig', [
            'article' => $article,
            'relatedArticles' => $relatedArticles
        ]);
    }

    #[Route('/api/market-analysis/filter', name: 'api_market_analysis_filter', methods: ['GET'])]
    public function filter(Request $request): JsonResponse
    {
        $assetType = $request->query->get('asset_type');
        $sortBy = $request->query->get('sort_by', 'date');
        $sortOrder = $request->query->get('sort_order', 'desc');
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = 6;
        $offset = ($page - 1) * $limit;

        try {
            $articles = $this->marketAnalysisRepository->findPublishedWithSorting($assetType, $sortBy, $sortOrder, $limit, $offset);
            $totalArticles = $this->marketAnalysisRepository->countPublished($assetType);
            $totalPages = ceil($totalArticles / $limit);

            $articlesData = array_map(function(MarketAnalysis $article) {
                return [
                    'id' => $article->getId(),
                    'title' => $article->getTitle(),
                    'excerpt' => $article->getShortExcerpt(120),
                    'assetType' => $article->getAssetType(),
                    'assetTypeLabel' => $article->getAssetTypeLabel(),
                    'thumbnailUrl' => $article->getThumbnailUrl(),
                    'featuredImageUrl' => $article->getFeaturedImageUrl(),
                    'publishDate' => $article->getFormattedPublishDate(),
                    'author' => $article->getAuthor(),
                    'detailUrl' => $this->generateUrl('app_market_analysis_show_seo', ['slug' => $article->getSlug()])
                ];
            }, $articles);

            return $this->json([
                'success' => true,
                'articles' => $articlesData,
                'pagination' => [
                    'currentPage' => $page,
                    'totalPages' => $totalPages,
                    'totalArticles' => $totalArticles,
                    'hasNext' => $page < $totalPages,
                    'hasPrev' => $page > 1
                ],
                'filter' => [
                    'assetType' => $assetType,
                    'assetTypeLabel' => $assetType ? $this->getAssetTypeLabel($assetType) : 'All Assets'
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'An error occurred while filtering articles.'
            ], 500);
        }
    }

    #[Route('/api/market-analysis/featured', name: 'api_market_analysis_featured', methods: ['GET'])]
    public function getFeatured(): JsonResponse
    {
        try {
            $featuredArticles = $this->marketAnalysisRepository->findFeatured(5);
            
            $articlesData = array_map(function(MarketAnalysis $article) {
                return [
                    'id' => $article->getId(),
                    'title' => $article->getTitle(),
                    'excerpt' => $article->getShortExcerpt(80),
                    'assetType' => $article->getAssetType(),
                    'assetTypeLabel' => $article->getAssetTypeLabel(),
                    'thumbnailUrl' => $article->getThumbnailUrl(),
                    'featuredImageUrl' => $article->getFeaturedImageUrl(),
                    'publishDate' => $article->getFormattedPublishDate(),
                    'detailUrl' => $this->generateUrl('app_market_analysis_show_seo', ['slug' => $article->getSlug()])
                ];
            }, $featuredArticles);

            return $this->json([
                'success' => true,
                'articles' => $articlesData
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'An error occurred while fetching featured articles.'
            ], 500);
        }
    }

    #[Route('/api/market-analysis/recent', name: 'api_market_analysis_recent', methods: ['GET'])]
    public function getRecent(Request $request): JsonResponse
    {
        try {
            $limit = min(20, max(1, (int) $request->query->get('limit', 5)));
            $recentArticles = $this->marketAnalysisRepository->findRecent($limit);
            
            $articlesData = array_map(function(MarketAnalysis $article) {
                return [
                    'id' => $article->getId(),
                    'title' => $article->getTitle(),
                    'excerpt' => $article->getShortExcerpt(100),
                    'assetType' => $article->getAssetType(),
                    'assetTypeLabel' => $article->getAssetTypeLabel(),
                    'thumbnailUrl' => $article->getThumbnailUrl(),
                    'featuredImageUrl' => $article->getFeaturedImageUrl(),
                    'publishDate' => $article->getFormattedPublishDate(),
                    'detailUrl' => $this->generateUrl('app_market_analysis_show_seo', ['slug' => $article->getSlug()])
                ];
            }, $recentArticles);

            return $this->json([
                'success' => true,
                'articles' => $articlesData
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'An error occurred while fetching recent articles.'
            ], 500);
        }
    }

    #[Route('/api/market-analysis/stats', name: 'api_market_analysis_stats', methods: ['GET'])]
    public function getStats(): JsonResponse
    {
        try {
            $assetTypesWithCounts = $this->marketAnalysisRepository->getAssetTypesWithCounts();
            $totalPublished = $this->marketAnalysisRepository->countPublished();
            
            return $this->json([
                'success' => true,
                'stats' => [
                    'totalPublished' => $totalPublished,
                    'assetTypes' => $assetTypesWithCounts
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'An error occurred while fetching statistics.'
            ], 500);
        }
    }

    /**
     * Helper method to get asset type labels
     */
    private function getAssetTypeLabel(string $assetType): string
    {
        return match($assetType) {
            'stocks' => 'Stocks',
            'forex' => 'Forex',
            'crypto' => 'Crypto',
            'crude_oil' => 'Crude Oil',
            'gold' => 'Gold',
            'commodities' => 'Commodities',
            default => 'Unknown'
        };
    }
}
