<?php

namespace App\Service;

use App\Entity\User;
use App\Entity\Video;
use App\Entity\Course;
use App\Entity\Order;
use App\Entity\UserVideoAccess;
use App\Repository\UserVideoAccessRepository;
use App\Repository\OrderRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for managing user access control to videos, courses, and video plans
 */
class AccessControlService
{
    private EntityManagerInterface $entityManager;
    private UserVideoAccessRepository $videoAccessRepository;
    private OrderRepository $orderRepository;
    private VdoCipherService $vdoCipherService;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserVideoAccessRepository $videoAccessRepository,
        OrderRepository $orderRepository,
        VdoCipherService $vdoCipherService,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->videoAccessRepository = $videoAccessRepository;
        $this->orderRepository = $orderRepository;
        $this->vdoCipherService = $vdoCipherService;
        $this->logger = $logger;
    }

    /**
     * Check if user has access to a video
     */
    public function hasVideoAccess(User $user, Video $video): bool
    {
        // Free videos are accessible to everyone
        if ($video->isFree()) {
            return true;
        }

        // Check if user has specific access granted
        return $this->videoAccessRepository->hasUserAccess($user, $video);
    }

    /**
     * Grant video access to user
     */
    public function grantVideoAccess(
        User $user, 
        Video $video, 
        ?\DateTimeImmutable $expiresAt = null,
        ?string $accessSource = null,
        ?int $orderId = null,
        ?int $videoPlanId = null
    ): UserVideoAccess {
        $access = $this->videoAccessRepository->grantAccess(
            $user, 
            $video, 
            $expiresAt, 
            $accessSource, 
            $orderId, 
            $videoPlanId
        );

        $this->entityManager->flush();

        $this->logger->info('Video access granted', [
            'user_id' => $user->getId(),
            'video_id' => $video->getId(),
            'expires_at' => $expiresAt?->format('Y-m-d H:i:s'),
            'source' => $accessSource
        ]);

        return $access;
    }



    /**
     * Process order completion and grant appropriate access
     */
    public function processOrderCompletion(Order $order): bool
    {
        if (!$order->isCompleted()) {
            $this->logger->warning('Attempted to process incomplete order', [
                'order_id' => $order->getId(),
                'status' => $order->getPaymentStatus()
            ]);
            return false;
        }

        $user = $order->getUser();
        $accessGranted = [];

        try {
            foreach ($order->getItems() as $item) {
                switch ($item['type']) {
                    case 'video':
                        $video = $this->entityManager->getRepository(Video::class)->find($item['id']);
                        if ($video) {
                            $access = $this->grantVideoAccess($user, $video, null, 'purchase', $order->getId());
                            $accessGranted[] = ['type' => 'video', 'id' => $video->getId(), 'title' => $video->getTitle()];
                        }
                        break;



                    case 'course':
                        // Course enrollment is handled by CourseEnrollmentService in CheckoutController
                        // to avoid circular dependencies. This just logs the course purchase.
                        $course = $this->entityManager->getRepository(Course::class)->find($item['id']);
                        if ($course) {
                            $accessGranted[] = ['type' => 'course', 'id' => $course->getId(), 'title' => $course->getTitle(), 'mode' => $course->getMode()];
                        }
                        break;
                }
            }

            $this->logger->info('Order access processing completed', [
                'order_id' => $order->getId(),
                'user_id' => $user->getId(),
                'access_granted' => $accessGranted
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error processing order access', [
                'order_id' => $order->getId(),
                'user_id' => $user->getId(),
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get user's accessible videos
     */
    public function getUserAccessibleVideos(User $user, ?string $category = null): array
    {
        $accessRecords = $this->videoAccessRepository->getUserAccessibleVideos($user, true);
        $videos = [];

        foreach ($accessRecords as $access) {
            $video = $access->getVideo();
            
            if ($category === null || $video->getCategory() === $category) {
                $videos[] = [
                    'video' => $video,
                    'access' => $access
                ];
            }
        }

        return $videos;
    }

    /**
     * Generate secure video playback token
     */
    public function generateVideoPlaybackToken(User $user, Video $video): ?array
    {
        // Check access first
        if (!$this->hasVideoAccess($user, $video)) {
            $this->logger->warning('Unauthorized video access attempt', [
                'user_id' => $user->getId(),
                'video_id' => $video->getId()
            ]);
            return null;
        }

        // For free videos, no OTP needed
        if ($video->isFree()) {
            return [
                'type' => 'free',
                'embed_code' => $this->vdoCipherService->generateEmbedCode($video->getVdocipherVideoId())
            ];
        }

        // Generate OTP for premium videos
        $otpData = $this->vdoCipherService->generateOTP($video->getVdocipherVideoId(), $user);
        
        if ($otpData) {
            // Record video access
            $this->videoAccessRepository->recordVideoAccess($user, $video);
            $this->entityManager->flush();

            return [
                'type' => 'premium',
                'otp' => $otpData['otp'],
                'playback_info' => $otpData['playbackInfo'] ?? $video->getVdocipherVideoId(),
                'embed_code' => $this->vdoCipherService->generateEmbedCode(
                    $video->getVdocipherVideoId(), 
                    $otpData['otp']
                )
            ];
        }

        return null;
    }

    /**
     * Revoke user access to a video
     */
    public function revokeVideoAccess(User $user, Video $video): bool
    {
        $revoked = $this->videoAccessRepository->revokeAccess($user, $video);
        
        if ($revoked) {
            $this->entityManager->flush();
            
            $this->logger->info('Video access revoked', [
                'user_id' => $user->getId(),
                'video_id' => $video->getId()
            ]);
        }

        return $revoked;
    }

    /**
     * Get user's access statistics
     */
    public function getUserAccessStatistics(User $user): array
    {
        $stats = $this->videoAccessRepository->getUserAccessStatistics($user);
        $purchaseHistory = $this->orderRepository->getUserPurchasedItems($user);

        return array_merge($stats, [
            'purchased_videos' => count($purchaseHistory['videos']),
            'purchased_courses' => count($purchaseHistory['courses'])
        ]);
    }

    /**
     * Clean up expired access records
     */
    public function cleanupExpiredAccess(): int
    {
        return $this->videoAccessRepository->cleanupExpiredAccess();
    }

    /**
     * Get access records expiring soon
     */
    public function getAccessExpiringSoon(int $days = 7): array
    {
        return $this->videoAccessRepository->findAccessExpiringSoon($days);
    }

    /**
     * Extend user's video access
     */
    public function extendVideoAccess(User $user, Video $video, int $days): bool
    {
        $access = $this->videoAccessRepository->getUserVideoAccess($user, $video);
        
        if ($access) {
            $access->extendAccess($days);
            $this->entityManager->flush();

            $this->logger->info('Video access extended', [
                'user_id' => $user->getId(),
                'video_id' => $video->getId(),
                'extended_days' => $days,
                'new_expiry' => $access->getExpiresAt()?->format('Y-m-d H:i:s')
            ]);

            return true;
        }

        return false;
    }

    /**
     * Grant lifetime access to a video
     */
    public function grantLifetimeVideoAccess(User $user, Video $video, ?string $accessSource = null): UserVideoAccess
    {
        return $this->grantVideoAccess($user, $video, null, $accessSource);
    }
}
