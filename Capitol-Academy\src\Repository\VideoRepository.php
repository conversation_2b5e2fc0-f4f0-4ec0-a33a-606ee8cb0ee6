<?php

namespace App\Repository;

use App\Entity\Video;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Video>
 *
 * @method Video|null find($id, $lockMode = null, $lockVersion = null)
 * @method Video|null findOneBy(array $criteria, array $orderBy = null)
 * @method Video[]    findAll()
 * @method Video[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VideoRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Video::class);
    }

    /**
     * Find all active videos
     */
    public function findActiveVideos(): array
    {
        return $this->createQueryBuilder('v')
            ->andWhere('v.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('v.category', 'ASC')
            ->addOrderBy('v.title', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all free videos
     */
    public function findFreeVideos(int $limit = null): array
    {
        $qb = $this->createQueryBuilder('v')
            ->where('v.isFree = :isFree')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('isFree', true)
            ->setParameter('isActive', true)
            ->orderBy('v.createdAt', 'DESC');

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find all premium videos
     */
    public function findPremiumVideos(int $limit = null): array
    {
        $qb = $this->createQueryBuilder('v')
            ->where('v.isFree = :isFree')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('isFree', false)
            ->setParameter('isActive', true)
            ->orderBy('v.createdAt', 'DESC');

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find videos by category
     */
    public function findByCategory(string $category, bool $activeOnly = true): array
    {
        $qb = $this->createQueryBuilder('v')
            ->where('v.category = :category')
            ->setParameter('category', $category)
            ->orderBy('v.createdAt', 'DESC');

        if ($activeOnly) {
            $qb->andWhere('v.isActive = :isActive')
               ->setParameter('isActive', true);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Search videos by title or description
     */
    public function searchVideos(string $query, bool $freeOnly = false): array
    {
        $qb = $this->createQueryBuilder('v')
            ->where('v.title LIKE :query OR v.description LIKE :query')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('query', '%' . $query . '%')
            ->setParameter('isActive', true)
            ->orderBy('v.createdAt', 'DESC');

        if ($freeOnly) {
            $qb->andWhere('v.isFree = :isFree')
               ->setParameter('isFree', true);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Search videos by title for public search
     */
    public function searchByTitle(string $query, int $limit = 10): array
    {
        return $this->createQueryBuilder('v')
            ->where('v.isActive = :isActive')
            ->andWhere('v.title LIKE :query OR v.description LIKE :query')
            ->setParameter('isActive', true)
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('v.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find videos by IDs (for video plans)
     */
    public function findByIds(array $ids): array
    {
        if (empty($ids)) {
            return [];
        }

        return $this->createQueryBuilder('v')
            ->where('v.id IN (:ids)')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('ids', $ids)
            ->setParameter('isActive', true)
            ->orderBy('v.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get all unique categories
     */
    public function getCategories(): array
    {
        $result = $this->createQueryBuilder('v')
            ->select('DISTINCT v.category')
            ->where('v.category IS NOT NULL')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('v.category', 'ASC')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'category');
    }

    /**
     * Find popular videos (most recently created for now)
     */
    public function findPopularVideos(int $limit = 6): array
    {
        return $this->createQueryBuilder('v')
            ->where('v.isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('v.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find video by slug
     */
    public function findBySlug(string $slug): ?Video
    {
        return $this->createQueryBuilder('v')
            ->where('v.slug = :slug')
            ->andWhere('v.isActive = :isActive')
            ->setParameter('slug', $slug)
            ->setParameter('isActive', true)
            ->getQuery()
            ->getOneOrNullResult();
    }



    /**
     * Count total videos
     */
    public function countVideos(bool $freeOnly = false): int
    {
        $qb = $this->createQueryBuilder('v')
            ->select('COUNT(v.id)')
            ->where('v.isActive = :isActive')
            ->setParameter('isActive', true);

        if ($freeOnly) {
            $qb->andWhere('v.isFree = :isFree')
               ->setParameter('isFree', true);
        }

        return $qb->getQuery()->getSingleScalarResult();
    }

    public function save(Video $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Video $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
