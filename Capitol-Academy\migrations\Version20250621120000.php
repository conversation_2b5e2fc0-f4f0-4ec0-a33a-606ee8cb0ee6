<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250621120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create countries table and import country data';
    }

    public function up(Schema $schema): void
    {
        // Create countries table
        $this->addSql('CREATE TABLE countries (
            id INT AUTO_INCREMENT NOT NULL,
            country_name VARCHAR(100) NOT NULL,
            phone_prefix VARCHAR(10) NOT NULL,
            phone_number_length INT NOT NULL,
            PRIMARY KEY(id),
            UNIQUE INDEX UNIQ_5D66EBAD5373C966 (country_name)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Insert country data
        $this->addSql("INSERT INTO countries (country_name, phone_prefix, phone_number_length) VALUES
            ('Afghanistan', '+93', 9),
            ('Albania', '+355', 9),
            ('Algeria', '+213', 9),
            ('Andorra', '+376', 6),
            ('Angola', '+244', 9),
            ('Antigua and Barbuda', '******', 10),
            ('Argentina', '+54', 10),
            ('Armenia', '+374', 8),
            ('Australia', '+61', 9),
            ('Austria', '+43', 10),
            ('Azerbaijan', '+994', 9),
            ('Bahamas', '******', 10),
            ('Bahrain', '+973', 8),
            ('Bangladesh', '+880', 10),
            ('Barbados', '******', 10),
            ('Belarus', '+375', 9),
            ('Belgium', '+32', 9),
            ('Belize', '+501', 7),
            ('Benin', '+229', 8),
            ('Bhutan', '+975', 8),
            ('Bolivia', '+591', 8),
            ('Bosnia and Herzegovina', '+387', 8),
            ('Botswana', '+267', 7),
            ('Brazil', '+55', 11),
            ('Brunei', '+673', 7),
            ('Bulgaria', '+359', 9),
            ('Burkina Faso', '+226', 8),
            ('Burundi', '+257', 8),
            ('Cambodia', '+855', 9),
            ('Cameroon', '+237', 9),
            ('Canada', '+1', 10),
            ('Cape Verde', '+238', 7),
            ('Central African Republic', '+236', 8),
            ('Chad', '+235', 8),
            ('Chile', '+56', 9),
            ('China', '+86', 11),
            ('Colombia', '+57', 10),
            ('Comoros', '+269', 7),
            ('Republic of Congo', '+242', 9),
            ('Democratic Republic of the Congo', '+243', 9),
            ('Cook Islands', '+682', 5),
            ('Costa Rica', '+506', 8),
            ('Côte d\\'Ivoire', '+225', 8),
            ('Croatia', '+385', 9),
            ('Cuba', '+53', 8),
            ('Cyprus', '+357', 8),
            ('Czech Republic', '+420', 9),
            ('Denmark', '+45', 8),
            ('Djibouti', '+253', 8),
            ('Dominica', '+1-767', 10),
            ('Dominican Republic', '+1-809', 10),
            ('Ecuador', '+593', 9),
            ('Egypt', '+20', 10),
            ('El Salvador', '+503', 8),
            ('Equatorial Guinea', '+240', 9),
            ('Eritrea', '+291', 7),
            ('Estonia', '+372', 7),
            ('Eswatini', '+268', 8),
            ('Ethiopia', '+251', 9),
            ('Fiji', '+679', 7),
            ('Finland', '+358', 9),
            ('France', '+33', 9),
            ('Gabon', '+241', 8),
            ('Gambia', '+220', 7),
            ('Georgia', '+995', 9),
            ('Germany', '+49', 10),
            ('Ghana', '+233', 9),
            ('Greece', '+30', 10),
            ('Grenada', '+1-473', 10),
            ('Guatemala', '+502', 8),
            ('Guinea', '+224', 9),
            ('Guinea-Bissau', '+245', 7),
            ('Guyana', '+592', 7),
            ('Haiti', '+509', 8),
            ('Honduras', '+504', 8),
            ('Hungary', '+36', 9),
            ('Iceland', '+354', 7),
            ('India', '+91', 10),
            ('Indonesia', '+62', 10),
            ('Iran', '+98', 10),
            ('Iraq', '+964', 10),
            ('Ireland', '+353', 9),
            ('Israel', '+972', 9),
            ('Italy', '+39', 10),
            ('Jamaica', '+1-876', 10),
            ('Jordan', '+962', 9),
            ('Kazakhstan', '+7', 10),
            ('Kenya', '+254', 10),
            ('Kiribati', '+686', 5),
            ('Republic of Korea', '+82', 10),
            ('Kosovo', '+383', 9),
            ('Kuwait', '+965', 8),
            ('Kyrgyz Republic', '+996', 9),
            ('Laos', '+856', 10),
            ('Latvia', '+371', 8),
            ('Lebanon', '+961', 8),
            ('Lesotho', '+266', 8),
            ('Liberia', '+231', 7),
            ('Libya', '+218', 10),
            ('Liechtenstein', '+423', 7),
            ('Lithuania', '+370', 8),
            ('Luxembourg', '+352', 9),
            ('Madagascar', '+261', 9),
            ('Malawi', '+265', 9),
            ('Malaysia', '+60', 10),
            ('Maldives', '+960', 7),
            ('Mali', '+223', 8),
            ('Malta', '+356', 8),
            ('Marshall Islands', '+692', 7),
            ('Mauritania', '+222', 8),
            ('Mauritius', '+230', 8),
            ('Mexico', '+52', 10),
            ('Micronesia', '+691', 7),
            ('Moldova', '+373', 8),
            ('Monaco', '+377', 8),
            ('Mongolia', '+976', 8),
            ('Montenegro', '+382', 8),
            ('Morocco', '+212', 9),
            ('Mozambique', '+258', 9),
            ('Myanmar', '+95', 9),
            ('Namibia', '+264', 8),
            ('Nauru', '+674', 7),
            ('Nepal', '+977', 10),
            ('Netherlands', '+31', 9),
            ('New Zealand', '+64', 9),
            ('Nicaragua', '+505', 8),
            ('Niger', '+227', 8),
            ('Nigeria', '+234', 10),
            ('Niue', '+683', 4),
            ('North Macedonia', '+389', 8),
            ('Norway', '+47', 8),
            ('Oman', '+968', 8),
            ('Pakistan', '+92', 10),
            ('Palau', '+680', 7),
            ('Panama', '+507', 8),
            ('Papua New Guinea', '+675', 7),
            ('Paraguay', '+595', 9),
            ('Peru', '+51', 9),
            ('Philippines', '+63', 10),
            ('Poland', '+48', 9),
            ('Portugal', '+351', 9),
            ('Qatar', '+974', 8),
            ('Romania', '+40', 10),
            ('Russia', '+7', 10),
            ('Rwanda', '+250', 9),
            ('Saint Kitts and Nevis', '+1-869', 10),
            ('Saint Lucia', '+1-758', 10),
            ('Saint Vincent', '+1-784', 10),
            ('Samoa', '+685', 7),
            ('San Marino', '+378', 8),
            ('Sao Tome and Principe', '+239', 7),
            ('Saudi Arabia', '+966', 9),
            ('Senegal', '+221', 9),
            ('Serbia', '+381', 9),
            ('Seychelles', '+248', 7),
            ('Sierra Leone', '+232', 8),
            ('Singapore', '+65', 8),
            ('Slovakia', '+421', 9),
            ('Slovenia', '+386', 8),
            ('Solomon Islands', '+677', 7),
            ('Somalia', '+252', 8),
            ('South Africa', '+27', 9),
            ('South Sudan', '+211', 9),
            ('Spain', '+34', 9),
            ('Sri Lanka', '+94', 9),
            ('Sudan', '+249', 9),
            ('Suriname', '+597', 7),
            ('Sweden', '+46', 9),
            ('Switzerland', '+41', 9),
            ('Syria', '+963', 9),
            ('Tajikistan', '+992', 9),
            ('Tanzania', '+255', 9),
            ('Thailand', '+66', 9),
            ('Togo', '+228', 8),
            ('Tonga', '+676', 5),
            ('Tunisia', '+216', 8),
            ('Türkiye', '+90', 10),
            ('Turkmenistan', '+993', 8),
            ('Tuvalu', '+688', 5),
            ('Uganda', '+256', 9),
            ('Ukraine', '+380', 9),
            ('United Arab Emirates', '+971', 9),
            ('United Kingdom', '+44', 10),
            ('United States of America', '+1', 10),
            ('Uruguay', '+598', 9),
            ('Uzbekistan', '+998', 9),
            ('Vanuatu', '+678', 7),
            ('Vatican', '+379', 6),
            ('Venezuela', '+58', 10),
            ('Vietnam', '+84', 9),
            ('Yemen', '+967', 9),
            ('Zambia', '+260', 9),
            ('Zimbabwe', '+263', 9)");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE countries');
    }
}
