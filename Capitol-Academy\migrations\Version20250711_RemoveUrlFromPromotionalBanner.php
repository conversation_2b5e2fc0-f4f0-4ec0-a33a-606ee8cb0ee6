<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove URL field from promotional_banner table
 */
final class Version20250711_RemoveUrlFromPromotionalBanner extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove url column from promotional_banner table';
    }

    public function up(Schema $schema): void
    {
        // Check if column exists before dropping it
        $this->addSql('ALTER TABLE promotional_banner DROP COLUMN IF EXISTS url');
    }

    public function down(Schema $schema): void
    {
        // Re-add the column if needed for rollback
        $this->addSql('ALTER TABLE promotional_banner ADD COLUMN url VARCHAR(100) NULL');
    }
}
