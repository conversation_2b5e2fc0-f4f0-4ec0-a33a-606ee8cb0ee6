{% extends 'admin/base.html.twig' %}

{% block title %}Edit Partner - Capitol Academy Admin{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for type, messages in app.flashes %}
        {% for message in messages %}
            <div class="alert alert-{{ type == 'error' ? 'danger' : type }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{{ type == 'success' ? 'check-circle' : (type == 'error' ? 'exclamation-triangle' : 'info-circle') }} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endfor %}

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit me-2 text-primary"></i>Edit Partner
                    </h1>
                    <p class="text-muted mb-0">Update partner information for {{ partner.name }}</p>
                </div>
                <a href="{{ path('admin_partners') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Partners
                </a>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Partner Information</h6>
                </div>
                <div class="card-body">
                    {{ form_start(form, {'attr': {'enctype': 'multipart/form-data', 'novalidate': 'novalidate'}}) }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form_label(form.name, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            {{ form_widget(form.name, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.name) }}
                            {% if form.name.vars.help %}
                                <div class="form-text">{{ form.name.vars.help }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form_label(form.displayOrder, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                            {{ form_widget(form.displayOrder, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.displayOrder) }}
                            {% if form.displayOrder.vars.help %}
                                <div class="form-text">{{ form.displayOrder.vars.help }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form_label(form.logoFile, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                        {{ form_widget(form.logoFile, {'attr': {'class': 'form-control'}}) }}
                        {{ form_errors(form.logoFile) }}
                        {% if form.logoFile.vars.help %}
                            <div class="form-text">{{ form.logoFile.vars.help }}</div>
                        {% endif %}
                        {% if partner.logoPath %}
                            <div class="form-text">
                                <strong>Current logo:</strong> {{ partner.logoPath }}
                                <br><small class="text-muted">Leave empty to keep current logo</small>
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            {{ form_widget(form.isActive, {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(form.isActive, null, {'label_attr': {'class': 'form-check-label'}}) }}
                        </div>
                        {{ form_errors(form.isActive) }}
                        {% if form.isActive.vars.help %}
                            <div class="form-text">{{ form.isActive.vars.help }}</div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ path('admin_partners') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Partner
                        </button>
                    </div>

                    {{ form_end(form) }}
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Logo</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="partner-preview mb-3" style="min-height: 100px; border: 2px solid #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                            <img src="{{ partner.logoUrl }}" alt="{{ partner.name }}" 
                                 class="img-fluid" style="max-height: 80px;"
                                 onerror="this.src='/images/placeholders/image-placeholder.png'">
                        </div>
                        <div class="h6 text-dark">{{ partner.name }}</div>
                        <small class="text-muted">Display Order: {{ partner.displayOrder }}</small>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-3">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">New Logo Preview</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="partner-preview mb-3" style="min-height: 100px; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                            <div id="logoPreview" class="text-muted">
                                <i class="fas fa-image fa-2x mb-2"></i>
                                <p class="mb-0">New logo preview will appear here</p>
                            </div>
                        </div>
                        <div id="namePreview" class="h6 text-dark">{{ partner.name }}</div>
                    </div>
                    
                    <div class="mt-4">
                        <h6 class="text-primary">Tips:</h6>
                        <ul class="small text-muted">
                            <li>Use PNG or SVG format for best quality</li>
                            <li>Recommended size: 200x100px</li>
                            <li>Keep file size under 2MB</li>
                            <li>Use transparent background if possible</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('partner_name');
    const logoInput = document.getElementById('partner_logoFile');
    const namePreview = document.getElementById('namePreview');
    const logoPreview = document.getElementById('logoPreview');

    // Name preview
    nameInput.addEventListener('input', function() {
        const name = this.value.trim();
        namePreview.textContent = name || '{{ partner.name }}';
    });

    // Logo preview
    logoInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="img-fluid" style="max-height: 80px;">`;
            };
            reader.readAsDataURL(file);
        } else {
            logoPreview.innerHTML = `
                <i class="fas fa-image fa-2x mb-2"></i>
                <p class="mb-0">New logo preview will appear here</p>
            `;
        }
    });

    // Enhanced form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Validate required fields
        const requiredFields = [
            { id: 'partner_name', name: 'Partner Name' },
            { id: 'partner_displayOrder', name: 'Display Order' }
        ];

        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element && !element.value.trim()) {
                isValid = false;
                element.classList.add('is-invalid');
                errorMessages.push(`${field.name} is required`);
            } else if (element) {
                element.classList.remove('is-invalid');
            }
        });

        // Validate display order is a positive number
        const displayOrderField = document.getElementById('partner_displayOrder');
        if (displayOrderField && displayOrderField.value.trim()) {
            const displayOrder = parseInt(displayOrderField.value);
            if (isNaN(displayOrder) || displayOrder < 0) {
                isValid = false;
                displayOrderField.classList.add('is-invalid');
                errorMessages.push('Display Order must be a positive number');
            }
        }

        // Validate logo file if selected
        const logoField = document.getElementById('partner_logoFile');
        if (logoField && logoField.files.length > 0) {
            const file = logoField.files[0];
            const maxSize = 2 * 1024 * 1024; // 2MB
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

            if (file.size > maxSize) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo file must be smaller than 2MB');
            }

            if (!allowedTypes.includes(file.type)) {
                isValid = false;
                logoField.classList.add('is-invalid');
                errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
            }
        }

        if (!isValid) {
            e.preventDefault();
            showValidationErrors(errorMessages);
        } else {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating Partner...';
        }
    });

    // Function to show validation errors
    function showValidationErrors(errors) {
        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Remove existing error alerts
        document.querySelectorAll('.alert-danger').forEach(alert => alert.remove());

        // Add new error alert at the top of the form
        const formCard = document.querySelector('.card');
        formCard.insertAdjacentHTML('beforebegin', errorHtml);

        // Scroll to top to show errors
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
});
</script>
{% endblock %}
