{% extends 'base.html.twig' %}

{% block title %}Instructor Registration - Capitol Academy{% endblock %}

{% block meta_description %}Join Capitol Academy as a professional instructor and share your expertise in financial markets education with students worldwide.{% endblock %}

{% block body %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ path('app_home') }}">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Instructor Registration</li>
        </ol>
    </div>
</nav>

<!-- Hero Section -->
<section class="bg-success text-white py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2">Instructor Registration</h1>
                <p class="lead mb-0">
                    Join our team of professional instructors and share your expertise with aspiring traders worldwide.
                </p>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-chalkboard-teacher fa-4x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-5">
                    <h2 class="mb-4">Instructor Application Form</h2>
                    
                    <p class="lead mb-4">
                        Be one of our Capitol Academy Professional Instructors and help shape the next generation of successful traders.
                    </p>
                    
                    <p class="mb-4">
                        Please fill out this application form and our academic team will review your qualifications and contact you about joining our instructor network.
                    </p>
                    
                    {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            {{ form_row(form.fname, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class="col-md-6">
                            {{ form_row(form.lname, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            {{ form_row(form.country, {
                                'attr': {'class': 'form-select'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class="col-md-6">
                            {{ form_row(form.email, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            {{ form_row(form.phone, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class="col-md-6">
                            {{ form_row(form.subject, {
                                'attr': {'class': 'form-control', 'readonly': true},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        {{ form_row(form.message, {
                            'attr': {'class': 'form-control', 'rows': 6, 'placeholder': 'Please provide details about your trading experience, educational background, areas of expertise, teaching experience, and why you want to join Capitol Academy as an instructor...'},
                            'label': 'Professional Background & Teaching Interest',
                            'label_attr': {'class': 'form-label fw-bold'}
                        }) }}
                    </div>
                    
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <button type="submit" class="btn btn-success btn-lg px-5 btn-enhanced">
                            <i class="fas fa-chalkboard-teacher me-2"></i>Submit Application
                        </button>
                        <button type="reset" class="btn btn-outline-secondary btn-lg px-5 btn-enhanced" onclick="return confirm('Are you sure you want to reset all fields?')">
                            <i class="fas fa-undo me-2"></i>Reset Form
                        </button>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Requirements -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">Instructor Requirements</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Minimum 3 years trading experience</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Proven track record of profitability</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Strong communication skills</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Teaching or mentoring experience preferred</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Professional certifications (preferred)</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Fluency in English</li>
                    </ul>
                </div>
            </div>
            
            <!-- Benefits -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Instructor Benefits</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Competitive compensation</li>
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Flexible teaching schedule</li>
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Professional development opportunities</li>
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Access to teaching resources</li>
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Global student network</li>
                        <li class="mb-2"><i class="fas fa-star text-warning me-2"></i>Capitol Academy certification</li>
                    </ul>
                </div>
            </div>
            
            <!-- Teaching Areas -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Teaching Opportunities</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-12">
                            <span class="badge bg-primary w-100 p-2">Financial Markets Analysis</span>
                        </div>
                        <div class="col-12">
                            <span class="badge bg-success w-100 p-2">Technical Analysis</span>
                        </div>
                        <div class="col-12">
                            <span class="badge bg-info w-100 p-2">Trading Strategies</span>
                        </div>
                        <div class="col-12">
                            <span class="badge bg-warning w-100 p-2">Fundamental Analysis</span>
                        </div>
                        <div class="col-12">
                            <span class="badge bg-danger w-100 p-2">Trading Psychology</span>
                        </div>
                        <div class="col-12">
                            <span class="badge bg-dark w-100 p-2">Risk & Capital Management</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Questions?</h5>
                </div>
                <div class="card-body">
                    <p class="card-text mb-3">
                        Have questions about becoming an instructor? Contact our academic team.
                    </p>
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-envelope text-primary me-2"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-phone text-primary me-2"></i>
                        <span>+44-************</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
