{% extends 'admin/base.html.twig' %}

{% block title %}Edit Administrator - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Administrator{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_admins') }}">Administrators</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_admin_view', {'id': admin.id}) }}">{{ admin.fullName }}</a></li>
<li class="breadcrumb-item active">Edit</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-0" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
            <div class="card-body py-4">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center">
                            {% if admin.profileImage %}
                                <img src="{{ asset('uploads/profiles/' ~ admin.profileImage) }}" 
                                     alt="{{ admin.fullName }}" 
                                     class="rounded-circle me-4"
                                     style="width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);">
                            {% else %}
                                <div class="rounded-circle me-4 d-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);">
                                    <span class="fs-2 fw-bold">{{ admin.fullName|slice(0,1)|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <h1 class="h3 mb-2 fw-bold">
                                    <i class="fas fa-edit me-3"></i>Edit Administrator
                                </h1>
                                <p class="mb-0 opacity-90">
                                    Modify settings and permissions for {{ admin.fullName }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <div class="d-flex flex-wrap gap-2 justify-content-lg-end">
                            <a href="{{ path('admin_admin_view', {'id': admin.id}) }}" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                            <a href="{{ path('admin_admins') }}" class="btn btn-light btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Form -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-cog me-2 text-primary"></i>Administrator Settings
                </h5>
            </div>
            <div class="card-body">
                {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-lg-6">
                        <div class="card border-0 bg-light mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-user me-2"></i>Basic Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        {{ form_label(form.firstName, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.firstName, {'attr': {'class': 'form-control'}}) }}
                                        {{ form_errors(form.firstName) }}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        {{ form_label(form.lastName, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.lastName, {'attr': {'class': 'form-control'}}) }}
                                        {{ form_errors(form.lastName) }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form_label(form.username, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                    {{ form_widget(form.username) }}
                                    {{ form_errors(form.username) }}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form_label(form.email, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                    {{ form_widget(form.email) }}
                                    {{ form_errors(form.email) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Settings -->
                    <div class="col-lg-6">
                        <div class="card border-0 bg-light mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-cog me-2"></i>Account Settings
                                </h6>
                            </div>
                            <div class="card-body">
                                {% if form.profileImageFile is defined %}
                                    <div class="mb-3">
                                        {{ form_label(form.profileImageFile, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.profileImageFile) }}
                                        {{ form_errors(form.profileImageFile) }}
                                        {% if admin.profileImage %}
                                            <div class="mt-2">
                                                <small class="text-muted">Current image:</small>
                                                <img src="{{ asset('uploads/profiles/' ~ admin.profileImage) }}" 
                                                     alt="Current profile" 
                                                     class="img-thumbnail ms-2" 
                                                     style="max-height: 50px;">
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}
                                
                                {% if form.isActive is defined %}
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            {{ form_widget(form.isActive) }}
                                            {{ form_label(form.isActive, null, {'label_attr': {'class': 'form-check-label fw-bold'}}) }}
                                        </div>
                                        {{ form_errors(form.isActive) }}
                                    </div>
                                {% endif %}
                                
                                {% if form.plainPassword is defined %}
                                    <div class="mb-3">
                                        {{ form_label(form.plainPassword, 'New Password (leave blank to keep current)', {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                        {{ form_widget(form.plainPassword) }}
                                        {{ form_errors(form.plainPassword) }}
                                        <div class="form-text">Leave blank to keep the current password</div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Permissions -->
                {% if form.permissions is defined %}
                    <div class="mb-4">
                        <div class="card border-0 bg-light">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-shield-alt me-2"></i>Permissions
                                </h6>
                            </div>
                            <div class="card-body">
                                {{ form_label(form.permissions, null, {'label_attr': {'class': 'form-label fw-bold'}}) }}
                                {{ form_widget(form.permissions) }}
                                {{ form_errors(form.permissions) }}
                                <div class="form-text">Select the permissions this administrator should have</div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <!-- Action Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="{{ path('admin_admin_view', {'id': admin.id}) }}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <div>
                        <button type="submit" class="btn btn-primary btn-lg me-2">
                            <i class="fas fa-save me-2"></i>Update Administrator
                        </button>
                        <button type="submit" name="save_and_continue" value="1" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>Save & Continue Editing
                        </button>
                    </div>
                </div>
                
                {{ form_end(form) }}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>
{% endblock %}
