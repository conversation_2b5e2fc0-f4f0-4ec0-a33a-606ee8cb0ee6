{% extends 'admin/base.html.twig' %}

{% block title %}Course Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Course Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_courses') }}">Courses</a></li>
<li class="breadcrumb-item active">{{ course.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Course Details: {{ course.code }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Course Button (Icon Only) -->
                        <a href="{{ path('admin_course_edit', {'code': course.code}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Course">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Course Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Course Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Courses Button -->
                        <a href="{{ path('admin_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">

                        <!-- Course Code and Title Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Course Code -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-hashtag text-primary mr-1"></i>
                                        Course Code
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ course.code }}
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-graduation-cap text-primary mr-1"></i>
                                        Course Title
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ course.title }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Description -->
                        <div class="row print-full-width clearfix">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-align-left text-primary mr-1"></i>
                                        Course Description
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;">
                                        {{ course.description|nl2br }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category and Level Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Category -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tags text-primary mr-1"></i>
                                        Category
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {% if course.category %}
                                            {{ course.category }}
                                        {% else %}
                                            No category assigned
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Level -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-signal text-primary mr-1"></i>
                                        Level
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {% if course.level %}
                                            {{ course.level|title }}
                                        {% else %}
                                            Not specified
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Details Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Course Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ course.isActive ? 'Active' : 'Inactive' }}
                                    </div>
                                </div>
                            </div>

                            <!-- View Count -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-eye text-primary mr-1"></i>
                                        View Count
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ course.viewCount|default(0) }} views
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Learning Outcomes -->
                        {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                        <div class="row print-full-width clearfix">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-bullseye text-primary mr-1"></i>
                                        Learning Outcomes
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        <ul class="mb-0">
                                            {% for outcome in course.learningOutcomes %}
                                                <li>{{ outcome }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Features -->
                        {% if course.features and course.features|length > 0 %}
                        <div class="row print-full-width clearfix">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-star text-primary mr-1"></i>
                                        Course Features
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        <ul class="mb-0">
                                            {% for feature in course.features %}
                                                <li>{{ feature }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Modules -->
                        {% if course.modules and course.modules|length > 0 %}
                        <div class="row print-full-width clearfix">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-list text-primary mr-1"></i>
                                        Course Modules ({{ course.modules|length }})
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;">
                                        {% for module in course.modules %}
                                        <div class="module-item mb-3 p-3" style="background: white; border: 1px solid #dee2e6; border-radius: 8px;">
                                            <!-- Module Header -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6 class="mb-1" style="color: #011a2d; font-weight: 600;">
                                                        <i class="fas fa-play-circle text-primary mr-2"></i>
                                                        {{ module.title }}
                                                    </h6>
                                                    <small class="text-muted">Code: {{ module.code }}</small>
                                                </div>
                                                <div class="col-md-6 text-md-right">
                                                    <span class="badge bg-info">Module {{ loop.index }}</span>
                                                    <span class="badge bg-success">{{ module.isActive ? 'Active' : 'Inactive' }}</span>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            {% if module.description %}
                                            <div class="mt-3">
                                                <h6 class="mb-2" style="color: #011a2d; font-weight: 600; font-size: 0.9rem;">
                                                    <i class="fas fa-align-left text-primary mr-2"></i>
                                                    Description
                                                </h6>
                                                <p class="mb-0 text-muted" style="font-size: 0.9rem; line-height: 1.5;">{{ module.description }}</p>
                                            </div>
                                            {% endif %}

                                            <!-- Module Learning Outcomes -->
                                            {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                            <div class="mt-3">
                                                <h6 class="mb-2" style="color: #011a2d; font-weight: 600; font-size: 0.9rem;">
                                                    <i class="fas fa-graduation-cap text-primary mr-2"></i>
                                                    Learning Outcomes
                                                </h6>
                                                <ul class="mb-0" style="font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;">
                                                    {% for outcome in module.learningOutcomes %}
                                                    <li class="text-muted mb-1">{{ outcome }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            {% endif %}

                                            <!-- Module Features -->
                                            {% if module.features and module.features|length > 0 %}
                                            <div class="mt-3">
                                                <h6 class="mb-2" style="color: #011a2d; font-weight: 600; font-size: 0.9rem;">
                                                    <i class="fas fa-star text-primary mr-2"></i>
                                                    Features
                                                </h6>
                                                <ul class="mb-0" style="font-size: 0.9rem; line-height: 1.5; padding-left: 1.2rem;">
                                                    {% for feature in module.features %}
                                                    <li class="text-muted mb-1">{{ feature }}</li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Course Images -->
                        <div class="row print-two-column clearfix">
                            <!-- Thumbnail Image -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-image text-primary mr-1"></i>
                                        Thumbnail Image
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem;">
                                        {% if course.thumbnailImage %}
                                            <img src="{{ course_image_url(course.thumbnailImage, 'thumbnail') }}"
                                                 alt="Course Thumbnail"
                                                 class="img-fluid rounded"
                                                 style="max-height: 200px; width: auto; object-fit: cover;">
                                        {% else %}
                                            <div class="text-center text-muted py-3">
                                                <i class="fas fa-image fa-3x mb-2"></i>
                                                <p class="mb-0">No thumbnail image</p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Course Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Course Status
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {% if course.isActive %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Status and Dates -->
                        <div class="row print-two-column clearfix">
                            <!-- Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {% if course.isActive %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                        Created Date
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ course.createdAt|date('F j, Y g:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>

<style>
/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }

    .print-two-column {
        page-break-inside: avoid;
    }

    .print-full-width {
        page-break-inside: avoid;
    }

    .module-item {
        page-break-inside: avoid;
        background: white !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}

/* Enhanced Display Field Styling */
.enhanced-display-field {
    font-weight: 500;
    line-height: 1.5;
}

.enhanced-display-field ul {
    padding-left: 1.5rem;
}

.enhanced-display-field ul li {
    margin-bottom: 0.25rem;
}

.module-item {
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
{% endblock %}
