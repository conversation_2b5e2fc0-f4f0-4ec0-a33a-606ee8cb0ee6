<?php

namespace App\Service;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Service for comprehensive validation and error handling
 */
class ValidationService
{
    private ValidatorInterface $validator;

    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    /**
     * Validate email format and uniqueness
     */
    public function validateEmail(string $email, ?object $existingEntity = null): array
    {
        $errors = [];
        
        // Basic email validation
        $emailConstraint = new Assert\Email(['message' => 'Please enter a valid email address.']);
        $violations = $this->validator->validate($email, $emailConstraint);
        
        if (count($violations) > 0) {
            foreach ($violations as $violation) {
                $errors[] = $violation->getMessage();
            }
        }
        
        // Length validation
        if (strlen($email) > 255) {
            $errors[] = 'Email address cannot be longer than 255 characters.';
        }
        
        return $errors;
    }

    /**
     * Validate password strength
     */
    public function validatePassword(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }
        
        if (strlen($password) > 128) {
            $errors[] = 'Password cannot be longer than 128 characters.';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter.';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter.';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number.';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character.';
        }
        
        // Check for common weak passwords
        $weakPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ];
        
        if (in_array(strtolower($password), $weakPasswords)) {
            $errors[] = 'Password is too common. Please choose a stronger password.';
        }
        
        return $errors;
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload(UploadedFile $file, array $options = []): array
    {
        $errors = [];
        
        // Default options
        $maxSize = $options['maxSize'] ?? 10 * 1024 * 1024; // 10MB
        $minSize = $options['minSize'] ?? 1024; // 1KB
        $allowedMimeTypes = $options['allowedMimeTypes'] ?? ['image/jpeg', 'image/png', 'image/webp'];
        $allowedExtensions = $options['allowedExtensions'] ?? ['jpg', 'jpeg', 'png', 'webp'];
        
        // Check if file is valid
        if (!$file->isValid()) {
            $errors[] = 'File upload failed. Please try again.';
            return $errors;
        }
        
        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = sprintf('File size too large. Maximum size is %s.', $this->formatFileSize($maxSize));
        }
        
        if ($file->getSize() < $minSize) {
            $errors[] = sprintf('File size too small. Minimum size is %s.', $this->formatFileSize($minSize));
        }
        
        // Check MIME type
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            $errors[] = 'Invalid file type. Allowed types: ' . implode(', ', $allowedMimeTypes);
        }
        
        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'Invalid file extension. Allowed extensions: ' . implode(', ', $allowedExtensions);
        }
        
        // Check for potential security issues
        $filename = $file->getClientOriginalName();
        if (preg_match('/[<>:"/\\|?*]/', $filename)) {
            $errors[] = 'Filename contains invalid characters.';
        }
        
        // Check for executable file extensions
        $dangerousExtensions = ['php', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'];
        if (in_array($extension, $dangerousExtensions)) {
            $errors[] = 'File type not allowed for security reasons.';
        }
        
        return $errors;
    }

    /**
     * Validate text input for XSS and other security issues
     */
    public function validateTextInput(string $input, array $options = []): array
    {
        $errors = [];
        
        $maxLength = $options['maxLength'] ?? 1000;
        $minLength = $options['minLength'] ?? 0;
        $allowHtml = $options['allowHtml'] ?? false;
        
        // Length validation
        if (strlen($input) > $maxLength) {
            $errors[] = sprintf('Text cannot be longer than %d characters.', $maxLength);
        }
        
        if (strlen($input) < $minLength) {
            $errors[] = sprintf('Text must be at least %d characters long.', $minLength);
        }
        
        // XSS protection
        if (!$allowHtml) {
            if (preg_match('/<[^>]*>/', $input)) {
                $errors[] = 'HTML tags are not allowed.';
            }
            
            if (preg_match('/javascript:/i', $input)) {
                $errors[] = 'JavaScript code is not allowed.';
            }
            
            if (preg_match('/on\w+\s*=/i', $input)) {
                $errors[] = 'Event handlers are not allowed.';
            }
        }
        
        // SQL injection protection
        $sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'UNION', 'EXEC'];
        foreach ($sqlKeywords as $keyword) {
            if (stripos($input, $keyword) !== false) {
                $errors[] = 'Potentially dangerous content detected.';
                break;
            }
        }
        
        return $errors;
    }

    /**
     * Validate phone number
     */
    public function validatePhoneNumber(string $phone): array
    {
        $errors = [];
        
        // Remove all non-digit characters for validation
        $cleanPhone = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($cleanPhone) < 7) {
            $errors[] = 'Phone number is too short.';
        }
        
        if (strlen($cleanPhone) > 15) {
            $errors[] = 'Phone number is too long.';
        }
        
        // Basic format validation
        if (!preg_match('/^[\+]?[0-9\s\-\(\)\.]{7,20}$/', $phone)) {
            $errors[] = 'Invalid phone number format.';
        }
        
        return $errors;
    }

    /**
     * Validate course code format
     */
    public function validateCourseCode(string $code): array
    {
        $errors = [];
        
        if (strlen($code) < 3) {
            $errors[] = 'Course code must be at least 3 characters long.';
        }
        
        if (strlen($code) > 10) {
            $errors[] = 'Course code cannot be longer than 10 characters.';
        }
        
        if (!preg_match('/^[A-Z0-9]+$/', $code)) {
            $errors[] = 'Course code can only contain uppercase letters and numbers.';
        }
        
        return $errors;
    }

    /**
     * Format file size for human reading
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $bytes;
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Sanitize input for safe display
     */
    public function sanitizeInput(string $input): string
    {
        // Remove potential XSS
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        return $input;
    }

    /**
     * Validate entity using Symfony validator
     */
    public function validateEntity(object $entity): array
    {
        $violations = $this->validator->validate($entity);
        $errors = [];
        
        foreach ($violations as $violation) {
            $errors[$violation->getPropertyPath()] = $violation->getMessage();
        }
        
        return $errors;
    }
}
