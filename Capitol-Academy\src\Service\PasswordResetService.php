<?php

namespace App\Service;

use App\Entity\PasswordResetToken;
use App\Entity\User;
use App\Repository\PasswordResetTokenRepository;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;

class PasswordResetService
{
    private const MAX_TOKENS_PER_USER = 3;
    private const TOKEN_LENGTH = 6;

    public function __construct(
        private EntityManagerInterface $entityManager,
        private PasswordResetTokenRepository $tokenRepository,
        private UserRepository $userRepository,
        private MailerInterface $mailer,
        private ParameterBagInterface $params,
        private LoggerInterface $logger,
        private IpAddressService $ipAddressService
    ) {}

    /**
     * Generate and send password reset code
     */
    public function sendPasswordResetCode(string $email, string $ipAddress): bool
    {
        try {
            // Find user by email
            $user = $this->userRepository->findOneBy(['email' => $email]);
            if (!$user) {
                throw new UserNotFoundException('User not found');
            }

            // Check rate limiting
            $activeTokens = $this->tokenRepository->countActiveTokensForUser($user);
            if ($activeTokens >= self::MAX_TOKENS_PER_USER) {
                $this->logger->warning('Rate limit exceeded for password reset', [
                    'user_id' => $user->getId(),
                    'email' => $email,
                    'ip_address' => $ipAddress
                ]);
                return false;
            }

            // Generate secure 6-digit code
            $token = $this->generateSecureToken();

            // Create password reset token
            $resetToken = new PasswordResetToken();
            $resetToken->setUser($user);
            $resetToken->setToken($token);
            $resetToken->setIpAddress($ipAddress);

            $this->entityManager->persist($resetToken);
            $this->entityManager->flush();

            // Send email
            $this->sendResetEmail($user, $token);

            $this->logger->info('Password reset code sent', [
                'user_id' => $user->getId(),
                'email' => $email,
                'ip_address' => $ipAddress
            ]);

            return true;

        } catch (UserNotFoundException $e) {
            // Don't log user not found for security reasons
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Failed to send password reset code', [
                'email' => $email,
                'error' => $e->getMessage(),
                'ip_address' => $ipAddress
            ]);
            return false;
        }
    }

    /**
     * Verify reset code and return user if valid
     */
    public function verifyResetCode(string $email, string $code): ?User
    {
        try {
            $user = $this->userRepository->findOneBy(['email' => $email]);
            if (!$user) {
                return null;
            }

            $token = $this->tokenRepository->findValidTokenForUser($user, $code);
            if (!$token || !$token->isValid()) {
                return null;
            }

            return $user;

        } catch (\Exception $e) {
            $this->logger->error('Failed to verify reset code', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Reset password using valid code
     */
    public function resetPassword(string $email, string $code, string $newPassword, string $ipAddress): bool
    {
        try {
            $user = $this->userRepository->findOneBy(['email' => $email]);
            if (!$user) {
                return false;
            }

            $token = $this->tokenRepository->findValidTokenForUser($user, $code);
            if (!$token || !$token->isValid()) {
                return false;
            }

            // Mark token as used
            $token->setIsUsed(true);

            // Invalidate all other tokens for this user
            $this->tokenRepository->invalidateAllTokensForUser($user);

            // Update user password (will be hashed by the controller)
            $user->setPassword($newPassword);
            $user->setUpdatedAt(new \DateTimeImmutable());

            $this->entityManager->flush();

            $this->logger->info('Password reset successful', [
                'user_id' => $user->getId(),
                'email' => $email,
                'ip_address' => $ipAddress
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Failed to reset password', [
                'email' => $email,
                'error' => $e->getMessage(),
                'ip_address' => $ipAddress
            ]);
            return false;
        }
    }

    /**
     * Generate secure 6-digit token
     */
    private function generateSecureToken(): string
    {
        return str_pad((string) random_int(100000, 999999), self::TOKEN_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * Send password reset email
     */
    private function sendResetEmail(User $user, string $token): void
    {
        $email = (new TemplatedEmail())
            ->from($this->params->get('app.email_from'))
            ->to($user->getEmail())
            ->subject('Capitol Academy - Password Reset Code')
            ->htmlTemplate('emails/password_reset.html.twig')
            ->context([
                'user' => $user,
                'token' => $token,
                'expires_in_minutes' => 15
            ]);

        $this->mailer->send($email);
    }

    /**
     * Clean up expired tokens (for maintenance)
     */
    public function cleanupExpiredTokens(): int
    {
        return $this->tokenRepository->cleanupExpiredTokens();
    }
}
