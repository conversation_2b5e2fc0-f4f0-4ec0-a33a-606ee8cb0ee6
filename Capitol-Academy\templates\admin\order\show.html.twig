{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Order',
    'entity_title': order.orderNumber,
    'entity_code': order.orderNumber,
    'entity_icon': 'fas fa-shopping-cart',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_order_index', 'title': 'Orders'},
        {'title': order.orderNumber, 'active': true}
    ],
    'edit_path': path('admin_order_edit', {'id': order.id}),
    'back_path': path('admin_order_index'),
    'print_function': 'printOrderDetails'
} %}

{% block preview_content %}

                        <!-- Order Information Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Order Number -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-hashtag text-primary mr-1"></i>
                                        Order Number
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        <code style="background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 6px; font-size: 1.1rem;">{{ order.orderNumber }}</code>
                                    </div>
                                </div>
                            </div>

                            <!-- Total Price -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                        Total Amount
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1.2rem;">
                                            ${{ order.totalPrice }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Customer Name -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user text-primary mr-1"></i>
                                        Customer Name
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        {{ order.user.fullName }}
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Email -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope text-primary mr-1"></i>
                                        Customer Email
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        <a href="mailto:{{ order.user.email }}" class="text-decoration-none" style="color: #011a2d;">{{ order.user.email }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Payment Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-credit-card text-primary mr-1"></i>
                                        Payment Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        {% set status_colors = {
                                            'completed': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
                                            'pending': 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',
                                            'failed': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                                            'refunded': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)',
                                            'cancelled': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
                                        } %}
                                        {% set status_icons = {
                                            'completed': 'fas fa-check-circle',
                                            'pending': 'fas fa-clock',
                                            'failed': 'fas fa-times-circle',
                                            'refunded': 'fas fa-undo',
                                            'cancelled': 'fas fa-ban'
                                        } %}
                                        <span class="badge px-3 py-2" style="background: {{ status_colors[order.paymentStatus] }}; color: white;">
                                            <i class="{{ status_icons[order.paymentStatus] }} mr-1"></i>{{ order.paymentStatus|title }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Gateway -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-credit-card text-primary mr-1"></i>
                                        Payment Gateway
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        {% if order.paymentGateway %}
                                            <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white;">
                                                {% if order.paymentGateway == 'paypal' %}
                                                    <i class="fab fa-paypal mr-1"></i>PayPal
                                                {% elseif order.paymentGateway == 'stripe' %}
                                                    <i class="fab fa-stripe mr-1"></i>Stripe
                                                {% else %}
                                                    <i class="fas fa-hand-holding-usd mr-1"></i>{{ order.paymentGateway|title }}
                                                {% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-shopping-bag text-primary mr-1"></i>
                                Order Items ({{ order.items|length }})
                            </label>
                            <div class="items-container" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;">
                                {% if order.items|length > 0 %}
                                    {% for item in order.items %}
                                    <div class="item-card" style="background: white; border: 2px solid #011a2d; border-radius: 8px; padding: 1.25rem; margin-bottom: {{ loop.last ? '0' : '1rem' }}; box-shadow: 0 2px 8px rgba(1, 26, 45, 0.1);">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-1" style="color: #011a2d; font-weight: 600;">{{ item.name }}</h6>
                                                <small class="text-muted">{{ item.type|title }}</small>
                                            </div>
                                            <div class="col-md-3 text-center">
                                                <span class="badge px-2 py-1" style="background: #e9ecef; color: #495057;">
                                                    Qty: {{ item.quantity }}
                                                </span>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <span class="badge px-3 py-2" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; font-size: 1rem;">
                                                    ${{ item.subtotal }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                {% else %}
                                    <div class="no-items" style="text-align: center; color: #666; font-style: italic;">No items in this order</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Transaction IDs -->
                        {% if order.paypalTransactionId or order.stripePaymentIntentId %}
                        <div class="row print-two-column clearfix">
                            {% if order.paypalTransactionId %}
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fab fa-paypal text-primary mr-1"></i>
                                        PayPal Transaction ID
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        <code style="background: #0070ba; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">{{ order.paypalTransactionId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}

                            {% if order.stripePaymentIntentId %}
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fab fa-stripe text-primary mr-1"></i>
                                        Stripe Payment Intent ID
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        <code style="background: #635bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px;">{{ order.stripePaymentIntentId }}</code>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- Notes -->
                        {% if order.notes %}
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-sticky-note text-primary mr-1"></i>
                                Order Notes
                            </label>
                            <div class="notes-container" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 1.5rem;">
                                <div class="notes-text" style="background: white; border: 2px solid #ffc107; border-radius: 8px; padding: 1.25rem; box-shadow: 0 2px 8px rgba(255, 193, 7, 0.1); white-space: pre-wrap; line-height: 1.6;">{{ order.notes }}</div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Timestamps Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Order Date -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                        Order Date
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        {{ order.createdAt|date('F j, Y \\a\\t g:i A') }}
                                    </div>
                                </div>
                            </div>

                            <!-- Completion Date -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-check text-primary mr-1"></i>
                                        Completion Date
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field">
                                        {% if order.completedAt %}
                                            {{ order.completedAt|date('F j, Y \\a\\t g:i A') }}
                                        {% else %}
                                            <span class="text-muted">Not completed</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
{% endblock %}

{% block print_body %}
    <!-- Order Information Section -->
    <div class="print-section">
        <div class="print-section-title">Order Information</div>
        <div class="print-info-grid">
            <div class="print-info-row">
                <div class="print-info-label">Order Number:</div>
                <div class="print-info-value">{{ order.orderNumber }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Customer Name:</div>
                <div class="print-info-value">{{ order.user.fullName }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Customer Email:</div>
                <div class="print-info-value">{{ order.user.email }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Total Amount:</div>
                <div class="print-info-value">${{ order.totalPrice }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Payment Status:</div>
                <div class="print-info-value">{{ order.paymentStatus|title }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Payment Gateway:</div>
                <div class="print-info-value">{{ order.paymentGateway ? order.paymentGateway|title : 'Not specified' }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Order Date:</div>
                <div class="print-info-value">{{ order.createdAt|date('F j, Y \\a\\t g:i A') }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Completion Date:</div>
                <div class="print-info-value">{{ order.completedAt ? order.completedAt|date('F j, Y \\a\\t g:i A') : 'Not completed' }}</div>
            </div>
        </div>
    </div>

    <!-- Order Items Section -->
    <div class="print-section">
        <div class="print-section-title">Order Items</div>
        <div class="print-info-grid">
            {% for item in order.items %}
            <div class="print-info-row">
                <div class="print-info-label">{{ item.name }}:</div>
                <div class="print-info-value">Qty: {{ item.quantity }} - ${{ item.subtotal }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    {% if order.notes %}
    <!-- Notes Section -->
    <div class="print-section">
        <div class="print-section-title">Order Notes</div>
        <div class="print-message-content" style="padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;">{{ order.notes }}</div>
    </div>
    {% endif %}
{% endblock %}
{% endembed %}

<script>
function printOrderDetails() {
    window.print();
}
</script>
