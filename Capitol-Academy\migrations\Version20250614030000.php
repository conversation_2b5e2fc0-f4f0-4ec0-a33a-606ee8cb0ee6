<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add price, level, and duration fields to Course entity
 */
final class Version20250614030000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add price, level, and duration fields to Course entity';
    }

    public function up(Schema $schema): void
    {
        // Add new columns to course table
        $this->addSql('ALTER TABLE course ADD price NUMERIC(10, 2) DEFAULT NULL');
        $this->addSql('ALTER TABLE course ADD level VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE course ADD duration INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove added columns from course table
        $this->addSql('ALTER TABLE course DROP COLUMN price');
        $this->addSql('ALTER TABLE course DROP COLUMN level');
        $this->addSql('ALTER TABLE course DROP COLUMN duration');
    }
}
