{% extends 'base.html.twig' %}

{% block title %}{{ course.title }} - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid px-0">
    <!-- Course Header -->
    <section class="course-header py-5" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge bg-light text-dark me-3 fs-6">{{ course.code }}</span>
                        <span class="badge bg-warning text-dark">{{ course.level|default('Beginner') }}</span>
                    </div>
                    <h1 class="display-5 fw-bold text-white mb-3">{{ course.title }}</h1>
                    <p class="lead text-white-50 mb-4">{{ course.description }}</p>
                    <div class="d-flex align-items-center text-white-50">
                        <i class="fas fa-tag me-2"></i>
                        <span>{{ course.category }}</span>
                    </div>
                </div>
                <div class="col-lg-4">
                    {% if course.thumbnailImage %}
                        <img src="{{ course.thumbnailUrl }}" class="img-fluid rounded shadow" alt="{{ course.title }}">
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- Learning Outcomes -->
                    {% if course.learningOutcomes|length > 0 %}
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body">
                                <h3 class="fw-bold text-dark mb-4">
                                    <i class="fas fa-lightbulb text-warning me-2"></i>What You'll Learn
                                </h3>
                                <div class="row">
                                    {% for outcome in course.learningOutcomes %}
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-start">
                                                <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                                                <span>{{ outcome }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Features -->
                    {% if course.features|length > 0 %}
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body">
                                <h3 class="fw-bold text-dark mb-4">
                                    <i class="fas fa-star text-warning me-2"></i>Course Features
                                </h3>
                                <div class="row">
                                    {% for feature in course.features %}
                                        <div class="col-md-6 mb-3">
                                            <div class="d-flex align-items-start">
                                                <i class="fas fa-gem text-primary me-3 mt-1"></i>
                                                <span>{{ feature }}</span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Course Modules -->
                    {% if course.modules|length > 0 %}
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-body">
                                <h3 class="fw-bold text-dark mb-4">
                                    <i class="fas fa-list text-info me-2"></i>Course Modules
                                </h3>
                                {% for module in course.modules %}
                                    <div class="module-item border-bottom pb-3 mb-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h5 class="fw-bold text-dark mb-2">
                                                    {{ module.code }} - {{ module.title }}
                                                </h5>
                                                <p class="text-muted mb-2">{{ module.description }}</p>
                                                
                                                {% if module.learningOutcomes|length > 0 %}
                                                    <div class="mb-2">
                                                        <h6 class="fw-bold text-dark mb-2">Module Outcomes:</h6>
                                                        <ul class="list-unstyled">
                                                            {% for outcome in module.learningOutcomes %}
                                                                <li class="mb-1">
                                                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                                                    <small>{{ outcome }}</small>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <span class="badge bg-secondary ms-3">Module {{ loop.index }}</span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Contact Form Sidebar -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm sticky-top" style="top: 2rem;">
                        <div class="card-body">
                            <h4 class="fw-bold text-dark mb-4">
                                <i class="fas fa-envelope text-primary me-2"></i>Interested in this course?
                            </h4>
                            <p class="text-muted mb-4">Contact us to learn more about enrollment and get started with your trading education.</p>
                            
                            <form method="POST" action="{{ path('public_course_contact', {code: course.code}) }}">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="4" placeholder="Tell us about your trading experience and goals..."></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.course-header {
    position: relative;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.course-header > .container {
    position: relative;
    z-index: 2;
}

.module-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
{% endblock %}
