(function(e){var n;e.fn.undoChanges=function(){n.undoChanges();return this};e.fn.updateChanges=function(){n.updateChanges();return this};e.fn.setTransition=function(G){n.setTransition(G);return this};e.fn.setEasing=function(G){n.setEasing(G);return this};e.fn.setTextEffect=function(G){n.setTextEffect(G);return this};e.fn.setTooltipType=function(G){n.setTooltipType(G);return this};e.fn.setCpanelAlign=function(G){n.setCpanelAlign(G);return this};e.fn.setCpanelPos=function(G){n.setCpanelPos(G);return this};e.fn.setThumbs=function(G){n.setThumbs(G);return this};e.fn.setDButtons=function(G){n.setDButtons(G);return this};e.fn.setPlayButton=function(G){n.setPlayButton(G);return this};e.fn.setTooltip=function(G){n.setTooltip(G);return this};e.fn.setTimerBar=function(G){n.setTimerBar(G);return this};e.fn.setSideButtons=function(G){n.setSideButtons(G);return this};e.fn.setThumbImg=function(G){n.setThumbImg(G);return this};e.fn.setMouseoverPause=function(G){n.setMouseoverPause(G);return this};e.fn.setMouseoverCPanel=function(G){n.setMouseoverCPanel(G);return this};e.fn.setMouseoverText=function(G){n.setMouseoverText(G);return this};C.prototype.undoChanges=function(){this.resetTimer();if(this.vStripeEffect){this.vStripes.clear()}if(this.hStripeEffect){this.hStripes.clear()}if(this.blockEffect){this.blocks.clear()}e("#rotator-tooltip").remove();this._$obj.empty();this._$obj.append(this._$cloneDom)};C.prototype.updateChanges=function(){this.init()};C.prototype.setMouseoverPause=function(G){this._pauseMouseover=window.Touch?false:G};C.prototype.setMouseoverText=function(G){this._textMousover=window.Touch?false:G};C.prototype.setMouseoverCPanel=function(G){this._cpMouseover=window.Touch?false:G};C.prototype.setTransition=function(G){this._globalEffect=G};C.prototype.setEasing=function(G){this._easing=G};C.prototype.setTextEffect=function(G){this._textEffect=G};C.prototype.setThumbs=function(G){this._displayThumbs=G};C.prototype.setDButtons=function(G){this._displayDBtns=G};C.prototype.setPlayButton=function(G){this._displayPlayBtn=G};C.prototype.setTimerBar=function(G){this._displayTimer=G};C.prototype.setSideButtons=function(G){this._displaySideBtns=G};C.prototype.setThumbImg=function(G){this._displayThumbImg=G;if(this._displayThumbImg){this._thumbWidth=this._thumbHeight=45;this._displayNumbers=false}else{this._thumbWidth=this._thumbHeight=24;this._displayNumbers=true}};C.prototype.setCpanelAlign=function(G){this._cpAlign=G};C.prototype.setCpanelPos=function(G){this._cpPos=G};C.prototype.setTooltipType=function(G){this._tipType=G};var m="inside";var t="outside";var E=0;var F=1;var l={TL:0,TC:1,TR:2,BL:3,BC:4,BR:5,LT:6,LC:7,LB:8,RT:9,RC:10,RB:11};var d=0;var k={"block.top":d++,"block.right":d++,"block.bottom":d++,"block.left":d++,"block.drop":d++,"diag.fade":d++,"diag.exp":d++,"rev.diag.fade":d++,"rev.diag.exp":d++,"block.fade":d++,"block.exp":d++,"block.top.zz":d++,"block.bottom.zz":d++,"block.left.zz":d++,"block.right.zz":d++,"spiral.in":d++,"spiral.out":d++,"vert.tl":d++,"vert.tr":d++,"vert.bl":d++,"vert.br":d++,"fade.left":d++,"fade.right":d++,"alt.left":d++,"alt.right":d++,"blinds.left":d++,"blinds.right":d++,"vert.random.fade":d++,"horz.tl":d++,"horz.tr":d++,"horz.bl":d++,"horz.br":d++,"fade.top":d++,"fade.bottom":d++,"alt.top":d++,"alt.bottom":d++,"blinds.top":d++,"blinds.bottom":d++,"horz.random.fade":d++,none:d++,fade:d++,"h.slide":d++,"v.slide":d++,random:d++};var B={fade:0,down:1,right:2,up:3,left:4,none:5};var r=250;var g=75;var a=50;var s=5000;var y=800;var x=500;var q=600;var z=4;var h=50;var f="updatetext";var w="updatelist";var A=(jQuery.browser.msie&&parseInt(jQuery.browser.version)<=7);function b(G){this._$stripes;this._arr;this._total;this._intervalId=null;this._rotator=G;this._areaWidth=G._screenWidth;this._areaHeight=G._screenHeight;this._size=G._vertSize;this._delay=G._vertDelay;this.init()}b.prototype.init=function(){this._total=Math.ceil(this._areaWidth/this._size);if(this._total>r){this._size=Math.ceil(this._areaWidth/r);this._total=Math.ceil(this._areaWidth/this._size)}var H="";for(var G=0;G<this._total;G++){H+="<div class='vpiece' id='"+G+"' style='left:"+(G*this._size)+"px; height:"+this._areaHeight+"px'></div>"}this._rotator.addToScreen(H);this._$stripes=this._rotator._$obj.find("div.vpiece");this._arr=this._$stripes.toArray()};b.prototype.clear=function(){clearInterval(this._intervalId);this._$stripes.stop(true).css({"z-index":2,opacity:0})};b.prototype.displayContent=function(G,H){this.setPieces(G,H);if(H==k["vert.random.fade"]){this.animateRandom(G)}else{this.animate(G,H)}};b.prototype.setPieces=function(G,H){switch(H){case k["vert.tl"]:case k["vert.tr"]:this.setVertPieces(G,-this._areaHeight,1,this._size,false);break;case k["vert.bl"]:case k["vert.br"]:this.setVertPieces(G,this._areaHeight,1,this._size,false);break;case k["alt.left"]:case k["alt.right"]:this.setVertPieces(G,0,1,this._size,true);break;case k["blinds.left"]:case k["blinds.right"]:this.setVertPieces(G,0,1,0,false);break;default:this.setVertPieces(G,0,0,this._size,false)}};b.prototype.setVertPieces=function(G,N,O,J,M){var Q=G.attr("src");var P=0;var I=0;if(this._rotator._autoCenter){P=(this._areaHeight-G.height())/2;I=(this._areaWidth-G.width())/2}for(var K=0;K<this._total;K++){var H=this._$stripes.eq(K);var L=((-K*this._size)+I);if(M){N=(K%2)==0?-this._areaHeight:this._areaHeight}H.css({background:"url('"+Q+"') no-repeat",backgroundPosition:L+"px "+P+"px",opacity:O,top:N,width:J,"z-index":3})}};b.prototype.animate=function(I,J){var K=this;var M,H,L,G;switch(J){case k["vert.tl"]:case k["vert.bl"]:case k["fade.left"]:case k["blinds.left"]:case k["alt.left"]:M=0;H=this._total-1;L=1;break;default:M=this._total-1;H=0;L=-1}this._intervalId=setInterval(function(){K._$stripes.eq(M).animate({top:0,opacity:1,width:K._size},K._rotator._duration,K._rotator._easing,function(){if(e(this).attr("id")==H){K._rotator.showContent(I)}});if(M==H){clearInterval(K._intervalId)}M+=L},this._delay)};b.prototype.animateRandom=function(H){var J=this;u(this._arr);var G=0;var I=0;this._intervalId=setInterval(function(){e(J._arr[G++]).animate({opacity:1},J._rotator._duration,J._rotator._easing,function(){if(++I==J._total){J._rotator.showContent(H)}});if(G==J._total){clearInterval(J._intervalId)}},this._delay)};function v(G){this._$stripes;this._arr;this._total;this._intervalId=null;this._rotator=G;this._areaWidth=G._screenWidth;this._areaHeight=G._screenHeight;this._size=G._horzSize;this._delay=G._horzDelay;this.init()}v.prototype.init=function(){this._total=Math.ceil(this._areaHeight/this._size);if(this._total>r){this._size=Math.ceil(this._areaHeight/r);this._total=Math.ceil(this._areaHeight/this._size)}var H="";for(var G=0;G<this._total;G++){H+="<div class='hpiece' id='"+G+"' style='top:"+(G*this._size)+"px; width:"+this._areaWidth+"px'><!-- --></div>"}this._rotator.addToScreen(H);this._$stripes=this._rotator._$obj.find("div.hpiece");this._arr=this._$stripes.toArray()};v.prototype.clear=function(){clearInterval(this._intervalId);this._$stripes.stop(true).css({"z-index":2,opacity:0})};v.prototype.displayContent=function(G,H){this.setPieces(G,H);if(H==k["horz.random.fade"]){this.animateRandom(G)}else{this.animate(G,H)}};v.prototype.setPieces=function(G,H){switch(H){case k["horz.tr"]:case k["horz.br"]:this.setHorzPieces(G,this._areaWidth,1,this._size,false);break;case k["horz.tl"]:case k["horz.bl"]:this.setHorzPieces(G,-this._areaWidth,1,this._size,false);break;case k["alt.top"]:case k["alt.bottom"]:this.setHorzPieces(G,0,1,this._size,true);break;case k["blinds.top"]:case k["blinds.bottom"]:this.setHorzPieces(G,0,1,0,false);break;default:this.setHorzPieces(G,0,0,this._size,false)}};v.prototype.setHorzPieces=function(G,K,N,P,M){var Q=G.attr("src");var O=0;var I=0;if(this._rotator._autoCenter){O=(this._areaHeight-G.height())/2;I=(this._areaWidth-G.width())/2}for(var L=0;L<this._total;L++){var H=this._$stripes.eq(L);var J=((-L*this._size)+O);if(M){K=(L%2)==0?-this._areaWidth:this._areaWidth}H.css({background:"url('"+Q+"') no-repeat",backgroundPosition:I+"px "+J+"px",opacity:N,left:K,height:P,"z-index":3})}};v.prototype.animate=function(H,I){var J=this;var L,G,K;switch(I){case k["horz.tl"]:case k["horz.tr"]:case k["fade.top"]:case k["blinds.top"]:case k["alt.top"]:L=0;G=this._total-1;K=1;break;default:L=this._total-1;G=0;K=-1}this._intervalId=setInterval(function(){J._$stripes.eq(L).animate({left:0,opacity:1,height:J._size},J._rotator._duration,J._rotator._easing,function(){if(e(this).attr("id")==G){J._rotator.showContent(H)}});if(L==G){clearInterval(J._intervalId)}L+=K},this._delay)};v.prototype.animateRandom=function(H){var J=this;u(this._arr);var G=0;var I=0;this._intervalId=setInterval(function(){e(J._arr[G++]).animate({opacity:1},J._rotator._duration,J._rotator._easing,function(){if(++I==J._total){J._rotator.showContent(H)}});if(G==J._total){clearInterval(J._intervalId)}},this._delay)};function o(G){this._$blockArr;this._$blocks;this._arr;this._numRows;this._numCols;this._total;this._intervalId;this._rotator=G;this._areaWidth=G._screenWidth;this._areaHeight=G._screenHeight;this._size=G._blockSize;this._delay=G._blockDelay;this.init()}o.prototype.init=function(){this._numRows=Math.ceil(this._areaHeight/this._size);this._numCols=Math.ceil(this._areaWidth/this._size);this._total=this._numRows*this._numCols;if(this._total>r){this._size=Math.ceil(Math.sqrt((this._areaHeight*this._areaWidth)/r));this._numRows=Math.ceil(this._areaHeight/this._size);this._numCols=Math.ceil(this._areaWidth/this._size);this._total=this._numRows*this._numCols}var J="";for(var I=0;I<this._numRows;I++){for(var H=0;H<this._numCols;H++){J+="<div class='block' id='"+I+"-"+H+"'></div>"}}this._rotator.addToScreen(J);this._$blocks=this._rotator._$obj.find("div.block");this._$blocks.data({tlId:"0-0",trId:"0-"+(this._numCols-1),blId:(this._numRows-1)+"-0",brId:(this._numRows-1)+"-"+(this._numCols-1)});var G=0;this._arr=this._$blocks.toArray();this._$blockArr=new Array(this._numRows);for(var I=0;I<this._numRows;I++){this._$blockArr[I]=new Array(this._numCols);for(var H=0;H<this._numCols;H++){this._$blockArr[I][H]=this._$blocks.filter("#"+(I+"-"+H)).data("top",I*this._size)}}};o.prototype.clear=function(){clearInterval(this._intervalId);this._$blocks.stop(true).css({"z-index":2,opacity:0})};o.prototype.displayContent=function(G,H){switch(H){case k["diag.fade"]:this.setBlocks(G,0,this._size,0);this.diagAnimate(G,{opacity:1},false);break;case k["diag.exp"]:this.setBlocks(G,0,0,0);this.diagAnimate(G,{opacity:1,width:this._size,height:this._size},false);break;case k["rev.diag.fade"]:this.setBlocks(G,0,this._size,0);this.diagAnimate(G,{opacity:1},true);break;case k["rev.diag.exp"]:this.setBlocks(G,0,0,0);this.diagAnimate(G,{opacity:1,width:this._size,height:this._size},true);break;case k["block.fade"]:this.setBlocks(G,0,this._size,0);this.randomAnimate(G);break;case k["block.exp"]:this.setBlocks(G,1,0,0);this.randomAnimate(G);break;case k["block.drop"]:this.setBlocks(G,1,this._size,-(this._numRows*this._size));this.randomAnimate(G);break;case k["block.top.zz"]:case k["block.bottom.zz"]:case k["block.left.zz"]:case k["block.right.zz"]:this.setBlocks(G,0,this._size,0);this.zigZag(G,H);break;case k["spiral.in"]:this.setBlocks(G,0,this._size,0);this.spiral(G,false);break;case k["spiral.out"]:this.setBlocks(G,0,this._size,0);this.spiral(G,true);break;default:this.setBlocks(G,1,0,0);this.dirAnimate(G,H)}};o.prototype.setBlocks=function(G,M,Q,L){var N=0;var I=0;if(this._rotator._autoCenter){N=(this._areaHeight-G.height())/2;I=(this._areaWidth-G.width())/2}var O=G.attr("src");for(var K=0;K<this._numRows;K++){for(var J=0;J<this._numCols;J++){var P=((-K*this._size)+N);var H=((-J*this._size)+I);this._$blockArr[K][J].css({background:"url('"+O+"') no-repeat",backgroundPosition:H+"px "+P+"px",opacity:M,top:(K*this._size)+L,left:(J*this._size),width:Q,height:Q,"z-index":3})}}};o.prototype.diagAnimate=function(G,P,L){var O=new Array(this._total);var H,K,I,J;var Q=(this._numRows-1)+(this._numCols-1);if(L){H=Q;K=-1;I=-1;J=this._$blocks.data("tlId")}else{H=0;K=Q+1;I=1;J=this._$blocks.data("brId")}var N=0;while(H!=K){i=Math.min(this._numRows-1,H);while(i>=0){j=Math.abs(i-H);if(j>=this._numCols){break}O[N++]=this._$blockArr[i][j];i--}H+=I}N=0;var M=this;this._intervalId=setInterval(function(){O[N++].animate(P,M._rotator._duration,M._rotator._easing,function(){if(e(this).attr("id")==J){M._rotator.showContent(G)}});if(N==M._total){clearInterval(M._intervalId)}},this._delay)};o.prototype.zigZag=function(H,O){var N=this;var L=true;var M=0,K=0,I,J,G;if(O==k["block.left.zz"]){J=(this._numCols%2==0)?this._$blocks.data("trId"):this._$blocks.data("brId");K=0;I=1;G=false}else{if(O==k["block.right.zz"]){J=(this._numCols%2==0)?this._$blocks.data("tlId"):this._$blocks.data("blId");K=this._numCols-1;I=-1;G=false}else{if(O==k["block.top.zz"]){J=(this._numRows%2==0)?this._$blocks.data("blId"):this._$blocks.data("brId");M=0;I=1;G=true}else{J=(this._numRows%2==0)?this._$blocks.data("tlId"):this._$blocks.data("trId");M=this._numRows-1;I=-1;G=true}}}this._intervalId=setInterval(function(){N._$blockArr[M][K].animate({opacity:1},N._duration,N._rotator._easing,function(){if(e(this).attr("id")==J){N._rotator.showContent(H)}});if(N._$blockArr[M][K].attr("id")==J){clearInterval(N._intervalId)}if(G){(L?K++:K--);if(K==N._numCols||K<0){L=!L;K=(L?0:N._numCols-1);M+=I}}else{(L?M++:M--);if(M==N._numRows||M<0){L=!L;M=(L?0:N._numRows-1);K+=I}}},this._delay)};o.prototype.dirAnimate=function(I,J){var M=new Array(this._total);var N;var L=0;switch(J){case k["block.left"]:N=this._$blocks.data("brId");for(var G=0;G<this._numCols;G++){for(var H=0;H<this._numRows;H++){M[L++]=this._$blockArr[H][G]}}break;case k["block.right"]:N=this._$blocks.data("blId");for(var G=this._numCols-1;G>=0;G--){for(var H=0;H<this._numRows;H++){M[L++]=this._$blockArr[H][G]}}break;case k["block.top"]:N=this._$blocks.data("brId");for(var H=0;H<this._numRows;H++){for(var G=0;G<this._numCols;G++){M[L++]=this._$blockArr[H][G]}}break;default:N=this._$blocks.data("trId");for(var H=this._numRows-1;H>=0;H--){for(var G=0;G<this._numCols;G++){M[L++]=this._$blockArr[H][G]}}}L=0;var K=this;this._intervalId=setInterval(function(){M[L++].animate({width:K._size,height:K._size},K._rotator._duration,K._rotator._easing,function(){if(e(this).attr("id")==N){K._rotator.showContent(I)}});if(L==K._total){clearInterval(K._intervalId)}},this._delay)};o.prototype.randomAnimate=function(H){u(this._arr);var G=0;count=0;var I=this;this._intervalId=setInterval(function(){e(I._arr[G]).animate({top:e(I._arr[G]).data("top"),width:I._size,height:I._size,opacity:1},I._rotator._duration,I._rotator._easing,function(){if(++count==I._total){I._rotator.showContent(H)}});G++;if(G==I._total){clearInterval(I._intervalId)}},this._delay)};o.prototype.spiral=function(G,O){var N=0,M=0;var S=this._numRows-1;var T=this._numCols-1;var I=0;var L=T;var R=new Array();while(S>=0&&T>=0){var Q=0;while(true){R[R.length]=this._$blockArr[N][M];if((++Q)>L){break}switch(I){case 0:M++;break;case 1:N++;break;case 2:M--;break;case 3:N--}}switch(I){case 0:I=1;L=(--S);N++;break;case 1:I=2;L=(--T);M--;break;case 2:I=3;L=(--S);N--;break;case 3:I=0;L=(--T);M++}}if(R.length>0){if(O){R.reverse()}var K=R.length-1;var H=R[K].attr("id");var J=0;var P=this;this._intervalId=setInterval(function(){R[J].animate({opacity:1},P._rotator._duration,P._rotator._easing,function(){if(e(this).attr("id")==H){P._rotator.showContent(G)}});if(J==K){clearInterval(P._intervalId)}J++},this._delay)}};function C(H,G){this._screenWidth=c(G.width,825);this._screenHeight=c(G.height,300);this._margin=D(G.button_margin,4);this._globalEffect=G.transition.toLowerCase();this._duration=c(G.transition_speed,y);this._globalDelay=c(G.delay,s);this._rotate=G.auto_start;this._cpPos=G.cpanel_position.toLowerCase();this._cpAlign=G.cpanel_align.toUpperCase();this._thumbWidth=c(G.thumb_width,24);this._thumbHeight=c(G.thumb_height,24);this._buttonWidth=c(G.button_width,24);this._buttonHeight=c(G.button_height,24);this._displayThumbImg=G.display_thumbimg;this._displayThumbs=G.display_thumbs;this._displaySideBtns=G.display_side_buttons;this._displayDBtns=G.display_dbuttons;this._displayPlayBtn=G.display_playbutton;this._displayNumbers=G.display_numbers;this._displayTimer=G.display_timer;this._cpMouseover=window.Touch?false:G.cpanel_mouseover;this._textMousover=window.Touch?false:G.text_mouseover;this._pauseMouseover=window.Touch?false:G.mouseover_pause;this._mouseoverSelect=window.Touch?false:G.mouseover_select;this._tipType=G.tooltip_type.toLowerCase();this._textEffect=G.text_effect.toLowerCase();this._textSync=G.text_sync;this._playOnce=G.play_once;this._autoCenter=G.auto_center;this._easing=G.easing;this._timerAlign=G.timer_align.toLowerCase();this._shuffle=G.shuffle;this._vertSize=c(G.vert_size,a);this._horzSize=c(G.horz_size,a);this._blockSize=c(G.block_size,g);this._vertDelay=c(G.vstripe_delay,75);this._horzDelay=c(G.hstripe_delay,75);this._blockDelay=c(G.block_delay,25);this._numItems;this._currIndex;this._prevIndex;this._delay;this._vStripes;this._hStripes;this._blocks;this._timerId;this._blockEffect;this._hStripeEffect;this._vStripeEffect;this._dir;this._cpVertical;this._slideCoord;this._$rotator;this._$screen;this._$strip;this._$mainLink;this._$textBox;this._$preloader;this._$cpWrapper;this._$cpanel;this._$thumbPanel;this._$list;this._$thumbs;this._$buttonPanel;this._$playBtn;this._$sPrev;this._$sNext;this._$timer;this._$tooltip;this._$items;this._$innerText;this._$obj=H;this._$cloneDom;this.init()}C.prototype.init=function(){this._$cloneDom=this._$obj.find(">div:first").clone();this._$rotator=this._$obj.find(".wt-rotator");this._$screen=this._$rotator.find("div.screen");this._$cpanel=this._$rotator.find("div.c-panel");this._$buttonPanel=this._$cpanel.find("div.buttons");this._$thumbPanel=this._$cpanel.find("div.thumbnails");this._$list=this._$thumbPanel.find(">ul");this._$thumbs=this._$list.find(">li");this._timerId=null;this._currIndex=0;this._prevIndex=-1;this._numItems=this._$thumbs.size();this._$items=new Array(this._numItems);this._blockEffect=this._hStripeEffect=this._vStripeEffect=false;this.checkEffect(k[this._globalEffect]);this._cpVertical=l[this._cpAlign]>=l.LT?true:false;if(this._displaySideBtns){this._displayDBtns=false}if(this._displayThumbImg){this._displayNumbers=false;if(this._tipType=="image"){this._tipType="none"}}if(this._shuffle){this.shuffleItems()}this._$rotator.css({width:this._screenWidth,height:this._screenHeight});this.initScreen();this.initButtons();this.initItems();this.initCPanel();this.initTimerBar();if(this._textMousover){this._$rotator.bind("mouseenter",{elem:this},this.displayText).bind("mouseleave",{elem:this},this.hideText)}else{this._$rotator.bind(f,{elem:this},this.updateText)}if(this._vStripeEffect){this._vStripes=new b(this)}if(this._hStripeEffect){this._hStripes=new v(this)}if(this._blockEffect){this._blocks=new o(this)}if(window.Touch){this._slideCoord={start:-1,end:-1};if(this._globalEffect=="v.slide"){this._$rotator.bind("touchstart",{elem:this},this.touchVStart).bind("touchmove",{elem:this},this.touchVMove)}else{this._$rotator.bind("touchstart",{elem:this},this.touchStart).bind("touchmove",{elem:this},this.touchMove)}this._$rotator.bind("touchend",{elem:this},this.touchEnd)}else{try{this._$rotator.bind("mousewheel",{elem:this},this.mouseScrollContent).bind("DOMMouseScroll",{elem:this},this.mouseScrollContent)}catch(G){}}this.loadImg(0);this.loadContent(this._currIndex)};C.prototype.touchStart=function(G){G.data.elem._slideCoord.start=G.originalEvent.touches[0].pageX};C.prototype.touchMove=function(G){G.preventDefault();G.data.elem._slideCoord.end=G.originalEvent.touches[0].pageX};C.prototype.touchVStart=function(G){G.data.elem._slideCoord.start=G.originalEvent.touches[0].pageY};C.prototype.touchVMove=function(G){G.preventDefault();G.data.elem._slideCoord.end=G.originalEvent.touches[0].pageY};C.prototype.touchEnd=function(H){var G=H.data.elem;if(G._slideCoord.end>=0){if(Math.abs(G._slideCoord.start-G._slideCoord.end)>h){if(G._slideCoord.end<G._slideCoord.start){G.nextImg()}else{G.prevImg()}}}G._slideCoord.start=G._slideCoord.end=-1};C.prototype.addToScreen=function(G){this._$mainLink.append(G)};C.prototype.initScreen=function(){var G="<div class='desc'><div class='inner-bg'></div><div class='inner-text'></div></div>						<div class='preloader'></div>						<div class='timer'></div>";this._$screen.append(G);this._$textBox=this._$screen.find("div.desc");this._$preloader=this._$screen.find("div.preloader");this._$screen.css({width:this._screenWidth,height:this._screenHeight});this._$innerText=this._$textBox.find("div.inner-text");this._$strip=e("<div class='strip'></div>");if(this._globalEffect=="h.slide"){this._$screen.append(this._$strip);this._$strip.css({width:2*this._screenWidth,height:this._screenHeight});this._$thumbs.removeAttr("effect")}else{if(this._globalEffect=="v.slide"){this._$screen.append(this._$strip);this._$strip.css({width:this._screenWidth,height:2*this._screenHeight});this._$thumbs.removeAttr("effect")}else{this._$screen.append("<a href='#'></a>");this._$mainLink=this._$screen.find(">a:first")}}};C.prototype.initCPanel=function(){if(this._displayThumbs||this._displayDBtns||this._displayPlayBtn){if(this._cpPos==m){switch(l[this._cpAlign]){case l.BL:this.setHPanel("left");this.setInsideHP("bottom");break;case l.BC:this.setHPanel("center");this.setInsideHP("bottom");break;case l.BR:this.setHPanel("right");this.setInsideHP("bottom");break;case l.TL:this.setHPanel("left");this.setInsideHP("top");break;case l.TC:this.setHPanel("center");this.setInsideHP("top");break;case l.TR:this.setHPanel("right");this.setInsideHP("top");break;case l.LT:this.setVPanel("top");this.setInsideVP("left");break;case l.LC:this.setVPanel("center");this.setInsideVP("left");break;case l.LB:this.setVPanel("bottom");this.setInsideVP("left");break;case l.RT:this.setVPanel("top");this.setInsideVP("right");break;case l.RC:this.setVPanel("center");this.setInsideVP("right");break;case l.RB:this.setVPanel("bottom");this.setInsideVP("right");break}if(this._cpMouseover){var G=this._cpVertical?"left":"top";this._$rotator.bind("mouseenter",{elem:this,dir:G},this.displayCPanel).bind("mouseleave",{elem:this,dir:G},this.hideCPanel)}}else{switch(l[this._cpAlign]){case l.BL:this.setHPanel("left");this.setOutsideHP(false);break;case l.BC:this.setHPanel("center");this.setOutsideHP(false);break;case l.BR:this.setHPanel("right");this.setOutsideHP(false);break;case l.TL:this.setHPanel("left");this.setOutsideHP(true);break;case l.TC:this.setHPanel("center");this.setOutsideHP(true);break;case l.TR:this.setHPanel("right");this.setOutsideHP(true);break;case l.LT:this.setVPanel("top");this.setOutsideVP(true);break;case l.LC:this.setVPanel("center");this.setOutsideVP(true);break;case l.LB:this.setVPanel("bottom");this.setOutsideVP(true);break;case l.RT:this.setVPanel("top");this.setOutsideVP(false);break;case l.RC:this.setVPanel("center");this.setOutsideVP(false);break;case l.RB:this.setVPanel("bottom");this.setOutsideVP(false);break}}this._$cpanel.css("visibility","visible").click(p)}};C.prototype.setHPanel=function(H){this._$cpanel.css({"margin-top":this._margin,"margin-bottom":this._margin,height:Math.max(this._$thumbPanel.outerHeight(true),this._$buttonPanel.outerHeight(true))});var G;if(H=="center"){G=Math.round((this._screenWidth-this._$cpanel.width()-this._margin)/2)}else{if(H=="left"){G=this._margin}else{G=this._screenWidth-this._$cpanel.width()}}this._$cpanel.css("left",G)};C.prototype.setVPanel=function(H){this._$cpanel.css({"margin-left":this._margin,"margin-right":this._margin,width:Math.max(this._$thumbPanel.outerWidth(true),this._$buttonPanel.outerWidth(true))});var G;if(H=="center"){G=Math.round((this._screenHeight-this._$cpanel.height()-this._margin)/2)}else{if(H=="top"){G=this._margin}else{G=this._screenHeight-this._$cpanel.height()}}this._$cpanel.css("top",G)};C.prototype.setInsideHP=function(I){var H,G;if(I=="top"){G=0;H=-this._$cpanel.outerHeight(true)}else{G=this._screenHeight-this._$cpanel.outerHeight(true);H=this._screenHeight}this._$cpanel.data({offset:H,pos:G}).css({top:(this._cpMouseover?H:G)})};C.prototype.setInsideVP=function(I){var H,G;if(I=="left"){G=0;H=-this._$cpanel.outerWidth(true)}else{G=this._screenWidth-this._$cpanel.outerWidth(true);H=this._screenWidth}this._$cpanel.data({offset:H,pos:G}).css({left:(this._cpMouseover?H:G)})};C.prototype.setOutsideHP=function(G){this._$cpanel.wrap("<div class='outer-hp'></div>");this._$cpWrapper=this._$rotator.find(".outer-hp");this._$cpWrapper.height(this._$cpanel.outerHeight(true));if(G){this._$cpWrapper.css({"border-top":"none",top:0});this._$screen.css("top",this._$cpWrapper.outerHeight())}else{this._$cpWrapper.css({"border-bottom":"none",top:this._screenHeight});this._$screen.css("top",0)}this._$rotator.css({height:this._screenHeight+this._$cpWrapper.outerHeight()})};C.prototype.setOutsideVP=function(G){this._$cpanel.wrap("<div class='outer-vp'></div>");this._$cpWrapper=this._$rotator.find(".outer-vp");this._$cpWrapper.width(this._$cpanel.outerWidth(true));if(G){this._$cpWrapper.css({"border-left":"none",left:0});this._$screen.css("left",this._$cpWrapper.outerWidth())}else{this._$cpWrapper.css({"border-right":"none",left:this._screenWidth});this._$screen.css("left",0)}this._$rotator.css({width:this._screenWidth+this._$cpWrapper.outerWidth()})};C.prototype.initButtons=function(){this._$playBtn=this._$buttonPanel.find("div.play-btn");var I=this._$buttonPanel.find("div.prev-btn");var J=this._$buttonPanel.find("div.next-btn");if(this._displayDBtns){I.bind("click",{elem:this},this.prevImg);J.bind("click",{elem:this},this.nextImg)}else{I.hide();J.hide()}if(this._displayPlayBtn){this._$playBtn.toggleClass("pause",this._rotate).bind("click",{elem:this},this.togglePlay)}else{this._$playBtn.hide()}if(this._pauseMouseover){this._$rotator.bind("mouseenter",{elem:this},this.pause).bind("mouseleave",{elem:this},this.play)}if(this._displaySideBtns){this._$screen.append("<div class='s-prev'></div><div class='s-next'></div>");this._$sPrev=this._$screen.find(".s-prev");this._$sNext=this._$screen.find(".s-next");this._$sPrev.bind("click",{elem:this},this.prevImg).mousedown(p);this._$sNext.bind("click",{elem:this},this.nextImg).mousedown(p);if(this._cpMouseover){this._$sPrev.css("left",-this._$sPrev.width());this._$sNext.css("margin-left",0);this._$rotator.bind("mouseenter",{elem:this},this.showSideButtons).bind("mouseleave",{elem:this},this.hideSideButtons)}}var H=this._$buttonPanel.find(">div").css({width:this._buttonWidth,height:this._buttonHeight}).mousedown(p);if(this._cpVertical){I.addClass("up");J.addClass("down");H.css("margin-bottom",this._margin);this._$buttonPanel.width(H.outerWidth());if(A){this._$buttonPanel.height(this._$buttonPanel.find(">div:visible").size()*H.outerHeight(true))}if(this._displayThumbs&&this._thumbWidth>this._buttonWidth){var G=this._thumbWidth-this._buttonWidth;switch(l[this._cpAlign]){case l.RT:case l.RC:case l.RB:this._$buttonPanel.css("margin-left",G);break;default:this._$buttonPanel.css("margin-right",G)}}}else{H.css("margin-right",this._margin);this._$buttonPanel.height(H.outerHeight());if(A){this._$buttonPanel.width(this._$buttonPanel.find(">div:visible").size()*H.outerWidth(true))}if(this._displayThumbs&&this._thumbHeight>this._buttonHeight){var G=this._thumbHeight-this._buttonHeight;switch(l[this._cpAlign]){case l.TL:case l.TC:case l.TR:this._$buttonPanel.css("margin-bottom",G);break;default:this._$buttonPanel.css("margin-top",G)}}}};C.prototype.initTimerBar=function(){this._$timer=this._$screen.find(".timer").data("pct",1);if(this._displayTimer){this._$timer.css("visibility","visible");this._$timer.css("top",this._timerAlign=="top"?0:this._screenHeight-this._$timer.height())}else{this._$timer.hide()}};C.prototype.initItems=function(){var Q=this;var S=this._$innerText.outerHeight()-this._$innerText.height();var O=this._$thumbs.size();for(var M=0;M<O;M++){var W=this._$thumbs.eq(M);var J=W.find(">a:first");var L=k[W.attr("effect")];if((typeof L=="undefined")||L==k["h.slide"]||L==k["v.slide"]){L=k[this._globalEffect]}else{this.checkEffect(L)}W.data({imgurl:J.attr("href"),caption:J.attr("title"),effect:L,delay:c(W.attr("delay"),this._globalDelay)});this.initTextData(W,S);this._$items[M]=W;if(this._displayNumbers){W.append(M+1)}}this._$innerText.css({width:"auto",height:"auto"}).html("");this._$textBox.css("visibility","visible");if(this._displayThumbs){if(this._displayThumbImg){this._$thumbs.addClass("image");this._$thumbs.find(">a").removeAttr("title");var P=this._$thumbs.find(">a>img");P.removeAttr("alt");var I=P.size();for(var M=0;M<I;M++){var H=P.eq(M);if(H[0].complete||H[0].readyState=="complete"){H.css({top:(this._thumbHeight-H.height())/2,left:(this._thumbWidth-H.width())/2})}else{H.load(function(){e(this).css({top:(Q._thumbHeight-e(this).height())/2,left:(Q._thumbWidth-e(this).width())/2})})}}}this._$thumbs.css({width:this._thumbWidth,height:this._thumbHeight,"line-height":this._thumbHeight+"px"}).mousedown(p);if(this._mouseoverSelect){this._$thumbs.bind("mouseover",{elem:this},this.selectItem)}else{this._$thumbPanel.bind("click",{elem:this},this.selectItem)}if(this._cpVertical){this._$thumbs.css("margin-bottom",this._margin);this._$list.width(this._$thumbs.outerWidth());this._$thumbPanel.width(this._$list.width());if(A){this._$thumbPanel.height(this._numItems*this._$thumbs.outerHeight(true))}if((this._displayDBtns||this._displayPlayBtn)&&(this._buttonWidth>this._thumbWidth)){var K=this._buttonWidth-this._thumbWidth;switch(l[this._cpAlign]){case l.RT:case l.RC:case l.RB:this._$thumbPanel.css("margin-left",K);break;default:this._$thumbPanel.css("margin-right",K)}}var U=this._screenHeight-(this._$buttonPanel.height()+this._margin);if(this._$thumbPanel.height()>U){var G=this._$thumbs.outerHeight(true);this._$list.addClass("inside").height(this._numItems*G);this._$thumbPanel.css({height:Math.floor(U/G)*G-this._margin,"margin-bottom":this._margin});var N=this._$thumbPanel.height()-(this._$list.height()-this._margin);this._$thumbPanel.append("<div class='back-scroll'></div><div class='fwd-scroll'></div>");var V=this._$thumbPanel.find(".back-scroll");var R=this._$thumbPanel.find(".fwd-scroll");V.css({height:G,width:"100%"});R.css({height:G,width:"100%",top:"100%","margin-top":-G});if(!window.Touch){V.bind("mouseenter",function(){R.show();var X=-Q._$list.stop(true).position().top*z;Q._$list.stop(true).animate({top:0},X,"linear",function(){V.hide()})}).bind("mouseleave",{elem:this},this.stopList);R.bind("mouseenter",function(){V.show();var X=(-N+Q._$list.stop(true).position().top)*z;Q._$list.stop(true).animate({top:N},X,"linear",function(){R.hide()})}).bind("mouseleave",{elem:this},this.stopList)}else{V.hide();R.hide()}this._$rotator.bind(w,function(){if(!Q._$list.is(":animated")){var X=Q._$list.position().top+(Q._currIndex*G);if(X<0||X>Q._$thumbPanel.height()-Q._$thumbs.outerHeight()){X=-Q._currIndex*G;if(X<N){X=N}Q._$list.stop(true).animate({top:X},x,function(){if(!window.Touch){e(this).position().top==0?V.hide():V.show();e(this).position().top==N?R.hide():R.show()}})}}})}}else{this._$thumbs.css("margin-right",this._margin);this._$list.height(this._$thumbs.outerHeight());this._$thumbPanel.height(this._$list.height());if(A){this._$thumbPanel.width(this._numItems*this._$thumbs.outerWidth(true))}if((this._displayDBtns||this._displayPlayBtn)&&this._buttonHeight>this._thumbHeight){var K=this._buttonHeight-this._thumbHeight;switch(l[this._cpAlign]){case l.TL:case l.TC:case l.TR:this._$thumbPanel.css("margin-bottom",K);break;default:this._$thumbPanel.css("margin-top",K)}}var T=this._screenWidth-(this._$buttonPanel.width()+this._margin);if(this._$thumbPanel.width()>T){var G=this._$thumbs.outerWidth(true);this._$list.addClass("inside").width(this._numItems*G);this._$thumbPanel.css({width:Math.floor(T/G)*G-this._margin,"margin-right":this._margin});var N=this._$thumbPanel.width()-(this._$list.width()-this._margin);this._$thumbPanel.append("<div class='back-scroll'></div><div class='fwd-scroll'></div>");var V=this._$thumbPanel.find(".back-scroll");var R=this._$thumbPanel.find(".fwd-scroll");V.css({width:G,height:"100%"});R.css({width:G,height:"100%",left:"100%","margin-left":-G});if(!window.Touch){V.bind("mouseenter",function(){R.show();var X=-Q._$list.stop(true).position().left*z;Q._$list.stop(true).animate({left:0},X,"linear",function(){V.hide()})}).bind("mouseleave",{elem:this},this.stopList);R.bind("mouseenter",function(){V.show();var X=(-N+Q._$list.stop(true).position().left)*z;Q._$list.stop(true).animate({left:N},X,"linear",function(){R.hide()})}).bind("mouseleave",{elem:this},this.stopList)}this._$rotator.bind(w,function(){if(!Q._$list.is(":animated")){var X=Q._$list.position().left+(Q._currIndex*G);if(X<0||X>Q._$thumbPanel.width()-Q._$thumbs.outerWidth()){X=-Q._currIndex*G;if(X<N){X=N}Q._$list.stop(true).animate({left:X},x,function(){e(this).position().left==0?V.hide():V.show();e(this).position().left==N?R.hide():R.show()})}}})}}this.initTooltip()}else{this._$thumbs.hide()}};C.prototype.initTextData=function(G,I){var H=G.find(">div:hidden");var K=c(parseInt(H.css("width"))-I,300);var J=c(parseInt(H.css("height"))-I,0);this._$innerText.width(K).html(H.html());if(J<this._$innerText.height()){J=this._$innerText.height()}G.data("textbox",{x:H.css("left"),y:H.css("top"),w:K+I,h:J+I+1,color:H.css("color"),bgcolor:H.css("background-color")})};C.prototype.initTooltip=function(){if(this._tipType=="text"){e("body").append("<div id='rotator-tooltip'><div class='tt-txt'></div></div>");this._$tooltip=e("body").find("#rotator-tooltip");this._$thumbs.bind("mouseover",{elem:this},this.showTooltip).bind("mouseout",{elem:this},this.hideTooltip).bind("mousemove",{elem:this},this.moveTooltip);switch(l[this._cpAlign]){case l.TL:case l.TC:case l.TR:this._$tooltip.data("bottom",true).addClass("txt-down");break;default:this._$tooltip.data("bottom",false).addClass("txt-up")}}else{if(this._tipType=="image"){var J="<div id='rotator-tooltip'>";for(var I=0;I<this._numItems;I++){var H=this._$items[I].find(">a:first>img");if(H.size()==1){J+="<img src='"+H.attr("src")+"' />"}else{J+="<img/>"}}J+="</div>";e("body").append(J);this._$tooltip=e("body").find("#rotator-tooltip");switch(l[this._cpAlign]){case l.TL:case l.TC:case l.TR:this._$thumbs.bind("mouseover",{elem:this},this.showHImgTooltip);this._$tooltip.data("bottom",true).addClass("img-down");break;case l.LT:case l.LC:case l.LB:this._$thumbs.bind("mouseover",{elem:this},this.showVImgTooltip);this._$tooltip.data("right",true).addClass("img-right");break;case l.RT:case l.RC:case l.RB:this._$thumbs.bind("mouseover",{elem:this},this.showVImgTooltip);this._$tooltip.data("right",false).addClass("img-left");break;default:this._$thumbs.bind("mouseover",{elem:this},this.showHImgTooltip);this._$tooltip.data("bottom",false).addClass("img-up")}this._$thumbs.bind("mouseout",{elem:this},this.hideTooltip)}}if(jQuery.browser.msie&&parseInt(jQuery.browser.version)<=6){try{this._$tooltip.css("background-image","none").children().css("margin",0)}catch(G){}}};C.prototype.showHImgTooltip=function(I){var H=I.data.elem;var G=H._$tooltip.find(">img").eq(e(this).index());if(G.attr("src")){H._$tooltip.find(">img").hide();G.show();if(G[0].complete||G[0].readyState=="complete"){var K=H._$tooltip.data("bottom")?e(this).outerHeight():-H._$tooltip.outerHeight();var J=e(this).offset();H._$tooltip.css({top:J.top+K,left:J.left+((e(this).outerWidth()-H._$tooltip.outerWidth())/2)}).stop(true,true).delay(q).fadeIn(300)}}};C.prototype.showVImgTooltip=function(J){var I=J.data.elem;var G=I._$tooltip.find(">img").eq(e(this).index());if(G.attr("src")){I._$tooltip.find(">img").hide();G.show();if(G[0].complete||G[0].readyState=="complete"){var H=I._$tooltip.data("right")?e(this).outerWidth():-I._$tooltip.outerWidth();var K=e(this).offset();I._$tooltip.css({top:K.top+((e(this).outerHeight()-I._$tooltip.outerHeight())/2),left:K.left+H}).stop(true,true).delay(q).fadeIn(300)}}};C.prototype.showTooltip=function(I){var H=I.data.elem;var G=H._$items[e(this).index()].data("caption");if(G!=""){H._$tooltip.find(">div.tt-txt").html(G);var J=H._$tooltip.data("bottom")?0:-H._$tooltip.outerHeight(true);H._$tooltip.css({top:I.pageY+J,left:I.pageX}).stop(true,true).delay(q).fadeIn(300)}};C.prototype.moveTooltip=function(H){var G=H.data.elem;var I=G._$tooltip.data("bottom")?0:-G._$tooltip.outerHeight(true);G._$tooltip.css({top:H.pageY+I,left:H.pageX})};C.prototype.hideTooltip=function(H){var G=(typeof H!="undefined")?H.data.elem:this;G._$tooltip.stop(true,true).hide()};C.prototype.displayCPanel=function(H){var G=H.data.elem;var I={};I[H.data.dir]=G._$cpanel.data("pos");I.opacity=1;G._$cpanel.stop(true).animate(I,x)};C.prototype.hideCPanel=function(H){var G=H.data.elem;var I={};I[H.data.dir]=G._$cpanel.data("offset");I.opacity=0;G._$cpanel.stop(true).animate(I,x)};C.prototype.showSideButtons=function(H){var G=H.data.elem;G._$sPrev.stop(true).animate({left:0},x);G._$sNext.stop(true).animate({"margin-left":-G._$sNext.width()},x)};C.prototype.hideSideButtons=function(H){var G=H.data.elem;G._$sPrev.stop(true).animate({left:-G._$sPrev.width()},x);G._$sNext.stop(true).animate({"margin-left":0},x)};C.prototype.selectItem=function(J){var I=J.data.elem;var G=e(J.target);if(G[0].nodeName!="LI"){G=G.parents("li").eq(0)}var H=G.index();if(H>-1&&H!=I._currIndex){I._dir=H<I._currIndex?E:F;I.resetTimer();I._prevIndex=I._currIndex;I._currIndex=H;I.loadContent(I._currIndex);I.hideTooltip()}return false};C.prototype.prevImg=function(H){var G=(typeof H!="undefined")?H.data.elem:this;G._dir=E;G.resetTimer();G._prevIndex=G._currIndex;G._currIndex=(G._currIndex>0)?(G._currIndex-1):(G._numItems-1);G.loadContent(G._currIndex);return false};C.prototype.nextImg=function(H){var G=(typeof H!="undefined")?H.data.elem:this;G._dir=F;G.resetTimer();G._prevIndex=G._currIndex;G._currIndex=(G._currIndex<G._numItems-1)?(G._currIndex+1):0;G.loadContent(G._currIndex);return false};C.prototype.togglePlay=function(H){var G=H.data.elem;G._rotate=!G._rotate;G._$playBtn.toggleClass("pause",G._rotate);G._rotate?G.startTimer():G.pauseTimer();return false};C.prototype.play=function(H){var G=H.data.elem;G._rotate=true;G._$playBtn.addClass("pause");G.startTimer()};C.prototype.pause=function(H){var G=H.data.elem;G._rotate=false;G._$playBtn.removeClass("pause");G.pauseTimer()};C.prototype.pauseLast=function(G){if(G==this._numItems-1){this._rotate=false;this._$playBtn.removeClass("pause")}};C.prototype.updateText=function(I){var G=I.data.elem;if(!G._$textBox.data("visible")){G._$textBox.data("visible",true);var J=G._$items[G._currIndex].find(">div:first").html();if(J&&J.length>0){var H=G._$items[G._currIndex].data("textbox");G._$innerText.css("color",H.color);G._$textBox.find(".inner-bg").css({"background-color":H.bgcolor,height:H.h-1});switch(B[G._textEffect]){case B.fade:G.fadeInText(J,H);break;case B.down:G.expandText(J,H,{width:H.w,height:0},{height:H.h});break;case B.right:G.expandText(J,H,{width:0,height:H.h},{width:H.w});break;case B.left:G.expandText(J,H,{"margin-left":H.w,width:0,height:H.h},{width:H.w,"margin-left":0});break;case B.up:G.expandText(J,H,{"margin-top":H.h,height:0,width:H.w},{height:H.h,"margin-top":0});break;default:G.showText(J,H)}}}};C.prototype.resetText=function(){this._$textBox.data("visible",false).stop(true,true);switch(B[this._textEffect]){case B.fade:case B.down:case B.right:case B.left:case B.up:if(jQuery.browser.msie){this._$innerText.css("opacity",0)}this._$textBox.fadeOut(x,function(){e(this).css("display","none")});break;default:this._$textBox.css("display","none")}};C.prototype.expandText=function(K,J,I,G){var H=this;this._$innerText.css("opacity",1).html("");this._$textBox.stop(true,true).css({display:"block",top:J.y,left:J.x,"margin-top":0,"margin-left":0}).css(I).animate(G,x,function(){H._$innerText.html(K)})};C.prototype.fadeInText=function(I,H){var G=this;this._$innerText.css("opacity",1).html(I);this._$textBox.css({top:H.y,left:H.x,width:H.w,height:H.h}).stop(true,true).fadeIn(x,function(){if(jQuery.browser.msie){G._$innerText[0].style.removeAttribute("filter")}})};C.prototype.showText=function(H,G){this._$textBox.stop(true).css({display:"block",top:G.y,left:G.x,width:G.w,height:G.h});this._$innerText.html(H)};C.prototype.displayText=function(H){var G=H.data.elem;G._$rotator.unbind(f).bind(f,{elem:G},G.updateText).trigger(f)};C.prototype.hideText=function(H){var G=H.data.elem;G._$rotator.unbind(f);G.resetText()};C.prototype.loadContent=function(I){this._$rotator.trigger(w);if(this._playOnce){this.pauseLast(I)}this._$thumbs.filter(".curr-thumb").removeClass("curr-thumb");this._$thumbs.eq(I).addClass("curr-thumb");this._delay=this._$items[I].data("delay");this.resetText();if(!this._textSync){this._$rotator.trigger(f)}if(this._$mainLink){var K=this._$items[I].find(">a:nth-child(2)");var G=K.attr("href");if(G){this._$mainLink.unbind("click",p).css("cursor","pointer").attr({href:G,target:K.attr("target")})}else{this._$mainLink.click(p).css("cursor","default")}}if(this._$items[I].data("img")){this._$preloader.hide();this.displayContent(this._$items[I].data("img"))}else{var J=this;var H=e("<img class='main-img'/>");H.load(function(){J._$preloader.hide();J.storeImg(J._$items[I],e(this));J.displayContent(e(this))}).error(function(){alert("Error loading image")});this._$preloader.show();H.attr("src",this._$items[I].data("imgurl"))}};C.prototype.displayContent=function(G){if(this._vStripeEffect){this._vStripes.clear()}if(this._hStripeEffect){this._hStripes.clear()}if(this._blockEffect){this._blocks.clear()}if(this._vStripeEffect||this._hStripeEffect||this._blockEffect){this.setPrevious()}var H=this._$items[this._currIndex].data("effect");if(H==k.none||(typeof H=="undefined")){this.showContent(G);return}else{if(H==k.fade){this.fadeInContent(G);return}else{if(H==k["h.slide"]){this.slideContent(G,"left",this._screenWidth);return}else{if(H==k["v.slide"]){this.slideContent(G,"top",this._screenHeight);return}}}}if(H==k.random){H=Math.floor(Math.random()*(d-5))}if(H<=k["spiral.out"]){this._blocks.displayContent(G,H)}else{if(H<=k["vert.random.fade"]){this._vStripes.displayContent(G,H)}else{this._hStripes.displayContent(G,H)}}};C.prototype.setPrevious=function(){if(this._prevIndex>=0){var H=this._$mainLink.find("img#curr-img").attr("src");var I=this._$items[this._prevIndex].data("imgurl");if(H!=I){this._$mainLink.find("img.main-img").attr("id","").hide();var G=this._$mainLink.find("img.main-img").filter(function(){return e(this).attr("src")==I});G.eq(0).show()}}};C.prototype.showContent=function(G){if(this._textSync){this._$rotator.trigger(f)}this._$mainLink.find("img.main-img").attr("id","").hide();G.attr("id","curr-img").show();this.startTimer()};C.prototype.fadeInContent=function(G){var H=this;this._$mainLink.find("img#curr-img").stop(true,true);this._$mainLink.find("img.main-img").attr("id","").css("z-index",0);G.attr("id","curr-img").stop(true,true).css({opacity:0,"z-index":1}).show().animate({opacity:1},this._duration,this._easing,function(){H._$mainLink.find("img.main-img:not('#curr-img')").hide();if(H._textSync){H._$rotator.trigger(f)}H.startTimer()})};C.prototype.slideContent=function(L,N,K){this._$strip.stop(true,true);var G=e("#curr-img",this._$strip);if(G.size()>0){this._$strip.find(".main-img").attr("id","").parents(".content-box").css({top:0,left:0});L.attr("id","curr-img").parents(".content-box").show();var I,H;if(this._dir==E){this._$strip.css(N,-K);I=G;H=0}else{I=L;H=-K}I.parents(".content-box").css(N,K);var M=(N=="top")?{top:H}:{left:H};var J=this;this._$strip.stop(true,true).animate(M,this._duration,this._easing,function(){J._$strip.find(".main-img:not('#curr-img')").parents(".content-box").hide();J._$strip.find("#curr-img").parents(".content-box").show();I.parents(".content-box").css({top:0,left:0});J._$strip.css({top:0,left:0});if(J._textSync){J._$rotator.trigger(f)}J.startTimer()})}else{this._$strip.css({top:0,left:0});this._$strip.find(".main-img").parents(".content-box").hide().css({top:0,left:0});L.attr("id","curr-img").parents(".content-box").show();if(this._textSync){this._$rotator.trigger(f)}this.startTimer()}};C.prototype.loadImg=function(H){try{var G=this._$items[H];var J=e("<img class='main-img'/>");var K=this;J.load(function(){if(!G.data("img")){K.storeImg(G,e(this))}H++;if(H<K._numItems){K.loadImg(H)}}).error(function(){H++;if(H<K._numItems){K.loadImg(H)}});J.attr("src",G.data("imgurl"))}catch(I){}};C.prototype.storeImg=function(I,J){if(this._globalEffect=="h.slide"||this._globalEffect=="v.slide"){this._$strip.append(J);this.centerImg(J);var G=e("<div class='content-box'></div>").css({width:this._screenWidth,height:this._screenHeight});J.wrap(G);J.css("display","block");var H=I.find(">a:nth-child(2)");if(H){J.wrap(H)}}else{this._$mainLink.append(J);this.centerImg(J)}I.data("img",J)};C.prototype.centerImg=function(G){if(this._autoCenter&&G.width()>0&&G.height()>0){G.css({top:(this._screenHeight-G.height())/2,left:(this._screenWidth-G.width())/2})}};C.prototype.startTimer=function(){if(this._rotate&&this._timerId==null){var G=this;var H=Math.round(this._$timer.data("pct")*this._delay);this._$timer.stop(true).animate({width:(this._screenWidth+1)},H,"linear");this._timerId=setTimeout(function(I){G._dir=F;G.resetTimer();G._prevIndex=G._currIndex;G._currIndex=(G._currIndex<G._numItems-1)?(G._currIndex+1):0;G.loadContent(G._currIndex)},H)}};C.prototype.resetTimer=function(){clearTimeout(this._timerId);this._timerId=null;this._$timer.stop(true).width(0).data("pct",1)};C.prototype.pauseTimer=function(){clearTimeout(this._timerId);this._timerId=null;this._$timer.stop(true);var G=1-(this._$timer.width()/(this._screenWidth+1));this._$timer.data("pct",G)};C.prototype.stopList=function(G){G.data.elem._$list.stop(true)};C.prototype.shuffleItems=function(){var J=new Array(this._numItems);for(var I=0;I<this._numItems;I++){var G=Math.floor(Math.random()*this._numItems);var H=this._$thumbs.eq(I);J[I]=this._$thumbs.eq(G);J[G]=H}for(var I=0;I<this._numItems;I++){this._$list.append(J[I])}this._$thumbs=this._$list.find(">li")};C.prototype.checkEffect=function(G){if(G==k.random){this._blockEffect=this._hStripeEffect=this._vStripeEffect=true}else{if(G<=k["spiral.out"]){this._blockEffect=true}else{if(G<=k["vert.random.fade"]){this._vStripeEffect=true}else{if(G<=k["horz.random.fade"]){this._hStripeEffect=true}}}}};C.prototype.mouseScrollContent=function(H){var G=H.data.elem;if(!G._$strip.is(":animated")){var I=(typeof H.originalEvent.wheelDelta=="undefined")?-H.originalEvent.detail:H.originalEvent.wheelDelta;I>0?G.prevImg():G.nextImg()}return false};function p(){return false}function c(G,H){if(!isNaN(G)&&G>0){return G}return H}function D(G,H){if(!isNaN(G)&&G>=0){return G}return H}function u(G){var K=G.length;for(var J=0;J<K;J++){var H=Math.floor(Math.random()*K);var I=G[J];G[J]=G[H];G[H]=I}}e.fn.wtRotator=function(I){var H={width:825,height:300,thumb_width:24,thumb_height:24,button_width:24,button_height:24,button_margin:4,auto_start:true,delay:s,transition:"fade",transition_speed:y,cpanel_position:m,cpanel_align:"BR",timer_align:"top",display_thumbs:true,display_side_buttons:false,display_dbuttons:true,display_playbutton:true,display_imgtooltip:true,display_numbers:true,display_thumbimg:false,display_timer:true,mouseover_select:false,mouseover_pause:false,cpanel_mouseover:false,text_mouseover:false,text_effect:"fade",text_sync:true,tooltip_type:"text",shuffle:false,play_once:false,auto_center:false,block_size:g,vert_size:a,horz_size:a,block_delay:25,vstripe_delay:75,hstripe_delay:75,easing:""};var G=e.extend({},H,I);return this.each(function(){n=new C(e(this),G)})}})(jQuery);