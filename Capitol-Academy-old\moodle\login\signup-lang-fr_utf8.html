<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="fr" xml:lang="fr">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Nouveau compte "/>
    <title>Nouveau compte</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/login/signup-lang-fr_utf8.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
addonload(function() { if(document.forms['mform1']) document.forms['mform1'].elements['username'].focus(); });
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="login course-1 notloggedin dir-ltr lang-fr_utf8" id="login-signup">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">Nouveau compte</h1>
        <div class="headermenu"><div class="logininfo">Non connecté. (<a href="../../moodle/login/index.html">Connexion</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">Vous êtes ici</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../../moodle.html">Academy</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> <a onclick="this.target='_top'" href="../../moodle/login/index.html">Connexion</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> Nouveau compte</li></ul></div>
        <div class="navbutton"><div class="langmenu"><form action="/" method="get" id="chooselang" class="popupform"><div><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=ar_utf8">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=en_utf8">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=fr_utf8" selected="selected">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="BYRMhAAvab"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="Valider"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div></div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix">
<form autocomplete="off" action="../../moodle/login/signup.html" method="post" accept-charset="utf-8" id="mform1" class="mform">
	<div style="display: none;"><input name="MAX_FILE_SIZE" type="hidden" value="8388608"/>
<input name="sesskey" type="hidden" value="BYRMhAAvab"/>
<input name="_qf__login_signup_form" type="hidden" value="1"/>
</div>
	<fieldset class="clearfix">
		<legend class="ftoggler">Créer un compte</legend>
		<div class="advancedbutton"></div><div class="fcontainer clearfix">
		<div class="fitem required"><div class="fitemtitle"><label for="id_username">Nom d'utilisateur </label></div><div class="felement ftext"><input maxlength="100" size="12" name="username" type="text" id="id_username"/></div></div>
		<div class="fitem"><div class="fitemtitle"><div class="fstaticlabel"><label> </label></div></div><div class="felement fstatic">Le mot de passe doit comporter au moins 8 caractère(s), au moins 1 chiffre(s), au moins 1 lettre(s) minuscule(s), au moins 1 lettre(s) majuscule(s), au moins 1 caractère(s) non-alphanumérique(s)&nbsp;</div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_password">Mot de passe </label></div><div class="felement fpassword"><input maxlength="32" size="12" autocomplete="off" name="password" type="password" id="id_password"/><script type="text/javascript">
//<![CDATA[
document.write('<div class="unmask"><input id="id_passwordunmask" value="1" type="checkbox" onclick="unmaskPassword(\'/')"/><label for="id_passwordunmask">Révéler<\/label><\/div>');
document.getElementById("id_password").setAttribute("autocomplete", "off");
//]]>
</script></div></div>
		</div></fieldset>
	<fieldset class="clearfix">
		<legend class="ftoggler">Plus de détails</legend>
		<div class="advancedbutton"></div><div class="fcontainer clearfix">
		<div class="fitem required"><div class="fitemtitle"><label for="id_email">Adresse de courriel </label></div><div class="felement ftext"><input maxlength="100" size="25" name="email" type="text" id="id_email"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_email2">Courriel (confirmation) </label></div><div class="felement ftext"><input maxlength="100" size="25" name="email2" type="text" id="id_email2"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_firstname">Prénom </label></div><div class="felement ftext"><input maxlength="100" size="30" name="firstname" type="text" id="id_firstname"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_lastname">Nom </label></div><div class="felement ftext"><input maxlength="100" size="30" name="lastname" type="text" id="id_lastname"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_city">Ville </label></div><div class="felement ftext"><input maxlength="20" size="20" name="city" type="text" id="id_city"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_country">Pays </label></div><div class="felement fselect"><select name="country" id="id_country">
	<option value="" selected="selected">Choisir un pays</option>
	<option value="EG">Égypte</option>
	<option value="AE">Émirats Arabes Unis</option>
	<option value="EC">Équateur</option>
	<option value="ER">Érythrée</option>
	<option value="US">États-Unis</option>
	<option value="FM">États Fédérés de Micronésie</option>
	<option value="ET">Éthiopie</option>
	<option value="AF">Afghanistan</option>
	<option value="ZA">Afrique du Sud</option>
	<option value="AL">Albanie</option>
	<option value="DZ">Algérie</option>
	<option value="DE">Allemagne</option>
	<option value="AD">Andorre</option>
	<option value="AO">Angola</option>
	<option value="AI">Anguilla</option>
	<option value="AQ">Antarctique</option>
	<option value="AG">Antigua-et-barbuda</option>
	<option value="AN">Antilles Néerlandaises</option>
	<option value="SA">Arabie Saoudite</option>
	<option value="AR">Argentine</option>
	<option value="AM">Arménie</option>
	<option value="AW">Aruba</option>
	<option value="AU">Australie</option>
	<option value="AT">Autriche</option>
	<option value="AZ">Azerbaïdjan</option>
	<option value="BV">Île Bouvet</option>
	<option value="CX">Île Christmas</option>
	<option value="IM">Île de Man</option>
	<option value="HM">Île Heard et Îles Mcdonald</option>
	<option value="NF">Île Norfolk</option>
	<option value="AX">Îles Åland</option>
	<option value="KY">Îles Caïmanes</option>
	<option value="CC">Îles Cocos (Keeling)</option>
	<option value="CK">Îles Cook</option>
	<option value="FO">Îles Féroé</option>
	<option value="FK">Îles Falkland (Malvinas)</option>
	<option value="MP">Îles Mariannes du Nord</option>
	<option value="LY">Îles Mariannes du Nord</option>
	<option value="MH">Îles Marshall</option>
	<option value="UM">Îles mineures éloignées des États-Unis</option>
	<option value="SB">Îles Salomon</option>
	<option value="TC">Îles Turks et Caïques</option>
	<option value="VG">Îles Vierges Britanniques</option>
	<option value="VI">Îles Vierges des États-Unis</option>
	<option value="BY">Bélarus</option>
	<option value="BJ">Bénin</option>
	<option value="BS">Bahamas</option>
	<option value="BH">Bahreïn</option>
	<option value="BD">Bangladesh</option>
	<option value="BB">Barbade</option>
	<option value="BE">Belgique</option>
	<option value="BZ">Belize</option>
	<option value="BM">Bermudes</option>
	<option value="BT">Bhoutan</option>
	<option value="BO">Bolivie</option>
	<option value="BA">Bosnie-Herzégovine</option>
	<option value="BW">Botswana</option>
	<option value="BR">Brésil</option>
	<option value="BN">Brunéi Darussalam</option>
	<option value="BG">Bulgarie</option>
	<option value="BF">Burkina Faso</option>
	<option value="BI">Burundi</option>
	<option value="CI">Côte d'Ivoire</option>
	<option value="KH">Cambodge</option>
	<option value="CM">Cameroun</option>
	<option value="CA">Canada</option>
	<option value="CV">Cap-Vert</option>
	<option value="CL">Chili</option>
	<option value="CN">Chine</option>
	<option value="CY">Chypre</option>
	<option value="CO">Colombie</option>
	<option value="KM">Comores</option>
	<option value="CG">Congo</option>
	<option value="CR">Costa Rica</option>
	<option value="HR">Croatie</option>
	<option value="CU">Cuba</option>
	<option value="DK">Danemark</option>
	<option value="DJ">Djibouti</option>
	<option value="DM">Dominique</option>
	<option value="SV">El Salvador</option>
	<option value="ES">Espagne</option>
	<option value="EE">Estonie</option>
	<option value="MK">Ex-République Yougoslave de Macédoine</option>
	<option value="RU">Fédération de Russie</option>
	<option value="FJ">Fidji</option>
	<option value="FI">Finlande</option>
	<option value="FR">France</option>
	<option value="GE">Géorgie</option>
	<option value="GS">Géorgie du Sud et les Îles Sandwich du Sud</option>
	<option value="GA">Gabon</option>
	<option value="GM">Gambie</option>
	<option value="GH">Ghana</option>
	<option value="GI">Gibraltar</option>
	<option value="GR">Grèce</option>
	<option value="GD">Grenade</option>
	<option value="GL">Groenland</option>
	<option value="GP">Guadeloupe</option>
	<option value="GU">Guam</option>
	<option value="GT">Guatemala</option>
	<option value="GG">Guernesey</option>
	<option value="GN">Guinée</option>
	<option value="GW">Guinée-Bissau</option>
	<option value="GQ">Guinée Équatoriale</option>
	<option value="GY">Guyana</option>
	<option value="GF">Guyane Française</option>
	<option value="HT">Haïti</option>
	<option value="HN">Honduras</option>
	<option value="HK">Hong-kong</option>
	<option value="HU">Hongrie</option>
	<option value="IN">Inde</option>
	<option value="ID">Indonésie</option>
	<option value="IQ">Iraq</option>
	<option value="IE">Irlande</option>
	<option value="IS">Islande</option>
	<option value="IL">Israël</option>
	<option value="IT">Italie</option>
	<option value="JM">Jamaïque</option>
	<option value="JP">Japon</option>
	<option value="JE">Jersey</option>
	<option value="JO">Jordanie</option>
	<option value="KZ">Kazakhstan</option>
	<option value="KE">Kenya</option>
	<option value="KG">Kirghizistan</option>
	<option value="KI">Kiribati</option>
	<option value="KW">Koweït</option>
	<option value="LS">Lesotho</option>
	<option value="LV">Lettonie</option>
	<option value="LR">Libéria</option>
	<option value="LB">Liban</option>
	<option value="LI">Liechtenstein</option>
	<option value="LT">Lituanie</option>
	<option value="LU">Luxembourg</option>
	<option value="MO">Macao</option>
	<option value="MG">Madagascar</option>
	<option value="MY">Malaisie</option>
	<option value="MW">Malawi</option>
	<option value="MV">Maldives</option>
	<option value="ML">Mali</option>
	<option value="MT">Malte</option>
	<option value="MA">Maroc</option>
	<option value="MQ">Martinique</option>
	<option value="MU">Maurice</option>
	<option value="MR">Mauritanie</option>
	<option value="YT">Mayotte</option>
	<option value="MX">Mexique</option>
	<option value="MC">Monaco</option>
	<option value="MN">Mongolie</option>
	<option value="ME">Monténégro</option>
	<option value="MS">Montserrat</option>
	<option value="MZ">Mozambique</option>
	<option value="MM">Myanmar</option>
	<option value="NP">Népal</option>
	<option value="NA">Namibie</option>
	<option value="NR">Nauru</option>
	<option value="NI">Nicaragua</option>
	<option value="NG">Nigéria</option>
	<option value="NE">Niger</option>
	<option value="NU">Niué</option>
	<option value="NO">Norvège</option>
	<option value="NC">Nouvelle-Calédonie</option>
	<option value="NZ">Nouvelle-Zélande</option>
	<option value="OM">Oman</option>
	<option value="UG">Ouganda</option>
	<option value="UZ">Ouzbékistan</option>
	<option value="PE">Pérou</option>
	<option value="PK">Pakistan</option>
	<option value="PW">Palaos</option>
	<option value="PA">Panama</option>
	<option value="PG">Papouasie-Nouvelle-Guinée</option>
	<option value="PY">Paraguay</option>
	<option value="NL">Pays-Bas</option>
	<option value="PH">Philippines</option>
	<option value="PN">Pitcairn</option>
	<option value="PL">Pologne</option>
	<option value="PF">Polynésie Française</option>
	<option value="PR">Porto Rico</option>
	<option value="PT">Portugal</option>
	<option value="TW">Province de Chine Taïwan</option>
	<option value="QA">Qatar</option>
	<option value="TZ">République-Unie de Tanzanie</option>
	<option value="SY">République Arabe Syrienne</option>
	<option value="CF">République centrafricaine</option>
	<option value="CD">République Démocratique du Congo</option>
	<option value="LA">République Démocratique Populaire Lao</option>
	<option value="KR">République de Corée</option>
	<option value="MD">République de Moldova</option>
	<option value="DO">République Dominicaine</option>
	<option value="IR">République Islamique d'Iran</option>
	<option value="KP">République Populaire Démocratique de Corée</option>
	<option value="CZ">République Tchèque</option>
	<option value="RE">Réunion</option>
	<option value="RO">Roumanie</option>
	<option value="GB">Royaume-Uni</option>
	<option value="RW">Rwanda</option>
	<option value="SN">Sénégal</option>
	<option value="EH">Sahara Occidental</option>
	<option value="BL">Saint-Barthélemy</option>
	<option value="KN">Saint-Kitts-et-Nevis</option>
	<option value="SM">Saint-Marin</option>
	<option value="MF">Saint-Martin</option>
	<option value="PM">Saint-Pierre-et-Miquelon</option>
	<option value="VA">Saint-Siège (État de la Cité du Vatican)</option>
	<option value="VC">Saint-Vincent-et-les Grenadines</option>
	<option value="SH">Sainte-Hélène</option>
	<option value="LC">Sainte-Lucie</option>
	<option value="WS">Samoa</option>
	<option value="AS">Samoa Américaines</option>
	<option value="ST">Sao Tomé-et-Principe</option>
	<option value="RS">Serbie</option>
	<option value="SC">Seychelles</option>
	<option value="SL">Sierra Leone</option>
	<option value="SG">Singapour</option>
	<option value="SI">Slovénie</option>
	<option value="SK">Slovaquie</option>
	<option value="SO">Somalie</option>
	<option value="SD">Soudan</option>
	<option value="LK">Sri Lanka</option>
	<option value="SE">Suède</option>
	<option value="CH">Suisse</option>
	<option value="SR">Suriname</option>
	<option value="SJ">Svalbard et Île Jan Mayen</option>
	<option value="SZ">Swaziland</option>
	<option value="TJ">Tadjikistan</option>
	<option value="TD">Tchad</option>
	<option value="TF">Terres Australes Françaises</option>
	<option value="IO">Territoire Britannique de l'Océan Indien</option>
	<option value="PS">Territoire Palestinien occupé</option>
	<option value="TH">Thaïlande</option>
	<option value="TL">Timor-Leste</option>
	<option value="TG">Togo</option>
	<option value="TK">Tokelau</option>
	<option value="TO">Tonga</option>
	<option value="TT">Trinité-et-Tobago</option>
	<option value="TN">Tunisie</option>
	<option value="TM">Turkménistan</option>
	<option value="TR">Turquie</option>
	<option value="TV">Tuvalu</option>
	<option value="UA">Ukraine</option>
	<option value="UY">Uruguay</option>
	<option value="VU">Vanuatu</option>
	<option value="VE">Venezuela</option>
	<option value="VN">Viet Nam</option>
	<option value="WF">Wallis et Futuna</option>
	<option value="YE">Yémen</option>
	<option value="ZM">Zambie</option>
	<option value="ZW">Zimbabwe</option>
</select></div></div>
		</div></fieldset>
	<fieldset class="hidden"><div>
		<div class="fitem"><div class="fitemtitle"><div class="fgrouplabel"><label> </label></div></div><fieldset class="felement fgroup"><input name="submitbutton" value="Créer mon compte" type="submit" id="id_submitbutton"/> <input name="cancel" value="Annuler" type="submit" onclick="skipClientValidation = true; return true;" id="id_cancel"/></fieldset></div>
		<div class="fdescription required">Ce formulaire comprend des champs requis, marqués </div>
		</div></fieldset>
</form>
<script type="text/javascript">
//<![CDATA[
var mform1items = Array();
lockoptionsallsetup('mform1');
//]]>
</script>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">Non connecté. (<a href="../../moodle/login/index.html">Connexion</a>)</div><div class="homelink"><a href="../../moodle.html">Accueil</a></div></div>
</div>
</body>
</html>