<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add IP address tracking to User and Contact entities
 */
final class Version20250614010000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add IP address tracking to User and Contact entities';
    }

    public function up(Schema $schema): void
    {
        // Add IP address column to user table
        $this->addSql('ALTER TABLE `user` ADD ip_address VARCHAR(45) DEFAULT NULL');
        
        // Add IP address column to contact table
        $this->addSql('ALTER TABLE contact ADD ip_address VARCHAR(45) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove IP address columns
        $this->addSql('ALTER TABLE `user` DROP COLUMN ip_address');
        $this->addSql('ALTER TABLE contact DROP COLUMN ip_address');
    }
}
