<?php

namespace App\Repository;

use App\Entity\Admin;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @extends ServiceEntityRepository<Admin>
 *
 * @implements PasswordUpgraderInterface<Admin>
 *
 * @method Admin|null find($id, $lockMode = null, $lockVersion = null)
 * @method Admin|null findOneBy(array $criteria, array $orderBy = null)
 * @method Admin[]    findAll()
 * @method Admin[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AdminRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Admin::class);
    }

    /**
     * Used to upgrade (rehash) the admin's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof Admin) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    /**
     * Find active admins
     */
    public function findActiveAdmins(): array
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('a.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find admin by email
     */
    public function findByEmail(string $email): ?Admin
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.email = :email')
            ->setParameter('email', $email)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find admin by username
     */
    public function findByUsername(string $username): ?Admin
    {
        return $this->createQueryBuilder('a')
            ->andWhere('a.username = :username')
            ->setParameter('username', $username)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Get admin statistics
     */
    public function getAdminStats(): array
    {
        $qb = $this->createQueryBuilder('a');
        
        $totalAdmins = $qb->select('COUNT(a.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $activeAdmins = $qb->select('COUNT(a.id)')
            ->andWhere('a.isActive = :active')
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        $recentLogins = $qb->select('COUNT(a.id)')
            ->andWhere('a.lastLoginAt >= :date')
            ->setParameter('date', new \DateTimeImmutable('-30 days'))
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total' => $totalAdmins,
            'active' => $activeAdmins,
            'recent_logins' => $recentLogins
        ];
    }

    /**
     * Find active admins with specific permission
     */
    public function findActiveAdminsWithPermission(string $permission): array
    {
        $admins = $this->findActiveAdmins();

        return array_filter($admins, function (Admin $admin) use ($permission) {
            return $admin->hasPermission($permission) || $admin->isMasterAdmin();
        });
    }
}
