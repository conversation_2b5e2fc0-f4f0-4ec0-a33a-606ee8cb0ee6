<?php

namespace App\Service;

use Symfony\Component\Asset\Packages;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Service for managing assets and providing fallback mechanisms
 */
class AssetService
{
    private Packages $packages;
    private RequestStack $requestStack;
    private string $projectDir;

    public function __construct(Packages $packages, RequestStack $requestStack, string $projectDir)
    {
        $this->packages = $packages;
        $this->requestStack = $requestStack;
        $this->projectDir = $projectDir;
    }

    /**
     * Get asset URL with fallback support
     */
    public function getAssetUrl(string $path, ?string $fallback = null): string
    {
        // Check if the asset exists
        $fullPath = $this->projectDir . '/public' . $path;
        
        if (file_exists($fullPath)) {
            return $this->packages->getUrl($path);
        }
        
        // Return fallback if provided and exists
        if ($fallback) {
            $fallbackPath = $this->projectDir . '/public' . $fallback;
            if (file_exists($fallbackPath)) {
                return $this->packages->getUrl($fallback);
            }
        }
        
        // Return default placeholder
        return $this->packages->getUrl('/images/placeholders/image-placeholder.png');
    }

    /**
     * Get profile image URL with fallback
     */
    public function getProfileImageUrl(?string $imageName, string $type = 'user'): string
    {
        if (!$imageName) {
            return $this->getDefaultProfileImage($type);
        }
        
        $imagePath = '/uploads/profiles/' . $imageName;
        $fullPath = $this->projectDir . '/public' . $imagePath;
        
        if (file_exists($fullPath)) {
            return $this->packages->getUrl($imagePath);
        }
        
        return $this->getDefaultProfileImage($type);
    }

    /**
     * Get course image URL with fallback
     */
    public function getCourseImageUrl(?string $imageName, string $type = 'thumbnail'): string
    {
        if (!$imageName) {
            return $this->getDefaultCourseImage($type);
        }
        
        // Handle both full paths and just filenames
        if (strpos($imageName, '/uploads/') === 0) {
            $imagePath = $imageName;
        } else {
            $imagePath = '/uploads/courses/' . $type . 's/' . $imageName;
        }
        
        $fullPath = $this->projectDir . '/public' . $imagePath;
        
        if (file_exists($fullPath)) {
            return $this->packages->getUrl($imagePath);
        }
        
        return $this->getDefaultCourseImage($type);
    }

    /**
     * Get admin image URL with fallback
     */
    public function getAdminImageUrl(?string $imageName = null): string
    {
        if ($imageName) {
            $imagePath = '/uploads/profiles/' . $imageName;
            $fullPath = $this->projectDir . '/public' . $imagePath;
            
            if (file_exists($fullPath)) {
                return $this->packages->getUrl($imagePath);
            }
        }
        
        // Check for default admin image
        $defaultAdminImage = '/images/misc/admin-image.png';
        $defaultPath = $this->projectDir . '/public' . $defaultAdminImage;

        if (file_exists($defaultPath)) {
            return $this->packages->getUrl($defaultAdminImage);
        }

        // Fallback to generic avatar
        return $this->packages->getUrl('/images/placeholders/image-placeholder.png');
    }

    /**
     * Get logo URL with fallback
     */
    public function getLogoUrl(string $type = 'main'): string
    {
        $logoMappings = [
            'main' => '/images/logos/logo-horizontal.png',
            'horizontal' => '/images/logos/logo-horizontal.png',
            'round' => '/images/logos/logo-round.png',
            'circular' => '/images/logos/logo-round.png',
            'small' => '/images/logos/logo-round.png',
            'favicon' => '/images/logos/logo-round.png'
        ];

        $logoPath = $logoMappings[$type] ?? $logoMappings['main'];

        // Fallback chain: requested logo -> horizontal logo -> round logo -> old logo
        $fallbackPath = $logoMappings['main'] !== $logoPath ? $logoMappings['main'] : '/images/logos/logo-round.png';

        return $this->getAssetUrl($logoPath, $fallbackPath);
    }

    /**
     * Check if asset exists
     */
    public function assetExists(string $path): bool
    {
        $fullPath = $this->projectDir . '/public' . $path;
        return file_exists($fullPath);
    }

    /**
     * Get default profile image based on type
     */
    private function getDefaultProfileImage(string $type): string
    {
        $defaults = [
            'admin' => '/images/misc/admin-image.png',
            'user' => '/images/placeholders/image-placeholder.png',
            'instructor' => '/images/instructors/instructor-default-pp.png'
        ];

        $defaultPath = $defaults[$type] ?? $defaults['user'];

        return $this->getAssetUrl($defaultPath, '/images/placeholders/image-placeholder.png');
    }

    /**
     * Get default course image based on type
     */
    private function getDefaultCourseImage(string $type): string
    {
        $defaults = [
            'thumbnail' => '/images/placeholders/course-placeholder.png',
            'banner' => '/images/placeholders/course-placeholder.png'
        ];

        $defaultPath = $defaults[$type] ?? $defaults['thumbnail'];

        return $this->getAssetUrl($defaultPath, '/images/placeholders/image-placeholder.png');
    }

    /**
     * Get file size in human readable format
     */
    public function getFileSize(string $path): string
    {
        $fullPath = $this->projectDir . '/public' . $path;
        
        if (!file_exists($fullPath)) {
            return 'N/A';
        }
        
        $size = filesize($fullPath);
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Generate responsive image srcset
     */
    public function getResponsiveImageSrcset(string $imagePath, array $sizes = [400, 800, 1200]): string
    {
        $srcset = [];
        $pathInfo = pathinfo($imagePath);
        
        foreach ($sizes as $size) {
            $responsivePath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $size . 'w.' . $pathInfo['extension'];
            
            if ($this->assetExists($responsivePath)) {
                $srcset[] = $this->packages->getUrl($responsivePath) . ' ' . $size . 'w';
            }
        }
        
        // Always include original as fallback
        $srcset[] = $this->packages->getUrl($imagePath) . ' ' . max($sizes) . 'w';
        
        return implode(', ', $srcset);
    }
}
