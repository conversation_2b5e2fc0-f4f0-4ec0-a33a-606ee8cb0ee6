<?php

namespace App\Entity;

use App\Repository\PlanRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: PlanRepository::class)]
class Plan
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $code = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $price = null;

    #[ORM\Column]
    private int $enrolled_count = 0;

    #[ORM\Column]
    private int $active_enrollments = 0;

    #[ORM\Column]
    private int $completed_count = 0;

    #[ORM\Column]
    private int $certified_count = 0;

    #[ORM\Column(type: Types::DECIMAL, precision: 3, scale: 2)]
    private string $average_rating = '0.00';

    #[ORM\Column]
    private int $total_reviews = 0;

    #[ORM\Column]
    private int $view_count = 0;

    #[ORM\Column(nullable: true)]
    private ?int $duration = null; // Access duration in days (how many days after payment the plan expires)

    #[ORM\Column]
    private ?bool $is_active = true;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updated_at = null;

    /**
     * @var Collection<int, Video>
     */
    #[ORM\ManyToMany(targetEntity: Video::class)]
    #[ORM\JoinTable(name: 'plan_videos')]
    private Collection $videos;

    public function __construct()
    {
        $this->videos = new ArrayCollection();
        $this->created_at = new \DateTimeImmutable();
        $this->updated_at = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }



    public function getPrice(): ?string
    {
        return $this->price;
    }

    public function setPrice(?string $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getEnrolledCount(): int
    {
        return $this->enrolled_count;
    }

    public function setEnrolledCount(int $enrolled_count): static
    {
        $this->enrolled_count = $enrolled_count;

        return $this;
    }

    public function getActiveEnrollments(): int
    {
        return $this->active_enrollments;
    }

    public function setActiveEnrollments(int $active_enrollments): static
    {
        $this->active_enrollments = $active_enrollments;

        return $this;
    }

    public function getCompletedCount(): int
    {
        return $this->completed_count;
    }

    public function setCompletedCount(int $completed_count): static
    {
        $this->completed_count = $completed_count;

        return $this;
    }

    public function getCertifiedCount(): int
    {
        return $this->certified_count;
    }

    public function setCertifiedCount(int $certified_count): static
    {
        $this->certified_count = $certified_count;

        return $this;
    }

    public function getAverageRating(): string
    {
        return $this->average_rating;
    }

    public function setAverageRating(string $average_rating): static
    {
        $this->average_rating = $average_rating;

        return $this;
    }

    public function getTotalReviews(): int
    {
        return $this->total_reviews;
    }

    public function setTotalReviews(int $total_reviews): static
    {
        $this->total_reviews = $total_reviews;

        return $this;
    }

    public function getViewCount(): int
    {
        return $this->view_count;
    }

    public function setViewCount(int $view_count): static
    {
        $this->view_count = $view_count;

        return $this;
    }

    public function incrementViewCount(): static
    {
        $this->view_count++;
        return $this;
    }



    public function getDuration(): ?int
    {
        return $this->duration;
    }

    public function setDuration(?int $duration): static
    {
        $this->duration = $duration;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->is_active;
    }

    public function setActive(bool $is_active): static
    {
        $this->is_active = $is_active;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updated_at;
    }

    public function setUpdatedAt(\DateTimeImmutable $updated_at): static
    {
        $this->updated_at = $updated_at;

        return $this;
    }

    /**
     * @return Collection<int, Video>
     */
    public function getVideos(): Collection
    {
        return $this->videos;
    }

    public function addVideo(Video $video): static
    {
        if (!$this->videos->contains($video)) {
            $this->videos->add($video);
        }

        return $this;
    }

    public function removeVideo(Video $video): static
    {
        $this->videos->removeElement($video);

        return $this;
    }

    /**
     * Get formatted duration string (access duration in days)
     */
    public function getFormattedDuration(): string
    {
        if (!$this->duration) {
            return 'Lifetime Access';
        }

        $days = $this->duration;
        if ($days === 1) {
            return '1 day';
        }

        return $days . ' days';
    }
}
