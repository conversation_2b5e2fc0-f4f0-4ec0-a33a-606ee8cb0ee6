<?php

namespace App\Entity;

use App\Repository\CourseReviewRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: CourseReviewRepository::class)]
class CourseReview
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Course::class, inversedBy: 'reviews')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Course $course = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\Column(type: Types::SMALLINT)]
    #[Assert\Range(min: 1, max: 5, notInRangeMessage: 'Rating must be between 1 and 5')]
    private int $rating = 5;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Assert\Length(max: 1000, maxMessage: 'Review comment cannot be longer than 1000 characters')]
    private ?string $comment = null;

    #[ORM\Column]
    private bool $is_certified = false; // Only certified students can leave reviews

    #[ORM\Column]
    private bool $is_approved = false; // Admin approval for reviews

    #[ORM\Column]
    private bool $is_featured = false; // Featured reviews for display

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updated_at = null;

    public function __construct()
    {
        $this->created_at = new \DateTimeImmutable();
        $this->updated_at = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCourse(): ?Course
    {
        return $this->course;
    }

    public function setCourse(?Course $course): static
    {
        $this->course = $course;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function getRating(): int
    {
        return $this->rating;
    }

    public function setRating(int $rating): static
    {
        $this->rating = $rating;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): static
    {
        $this->comment = $comment;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function isCertified(): bool
    {
        return $this->is_certified;
    }

    public function setIsCertified(bool $is_certified): static
    {
        $this->is_certified = $is_certified;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function isApproved(): bool
    {
        return $this->is_approved;
    }

    public function setIsApproved(bool $is_approved): static
    {
        $this->is_approved = $is_approved;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    public function setIsFeatured(bool $is_featured): static
    {
        $this->is_featured = $is_featured;
        $this->updated_at = new \DateTimeImmutable();
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updated_at;
    }

    public function setUpdatedAt(\DateTimeImmutable $updated_at): static
    {
        $this->updated_at = $updated_at;
        return $this;
    }

    public function getRatingStars(): array
    {
        $stars = [];
        for ($i = 1; $i <= 5; $i++) {
            $stars[] = $i <= $this->rating ? 'full' : 'empty';
        }
        return $stars;
    }

    public function getFormattedDate(): string
    {
        return $this->created_at->format('F j, Y');
    }

    public function getUserFullName(): string
    {
        if ($this->user) {
            return $this->user->getFirstName() . ' ' . $this->user->getLastName();
        }
        return 'Anonymous';
    }
}
