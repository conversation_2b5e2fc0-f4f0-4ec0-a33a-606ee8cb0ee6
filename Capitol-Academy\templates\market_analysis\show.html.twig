{% extends 'base.html.twig' %}

{% block title %}{{ article.title }} - Market Analysis | Capitol Academy{% endblock %}

{% block meta_description %}{{ article.excerpt|slice(0, 160) }} - Expert market analysis from Capitol Academy's professional trading team.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
/* Article Detail Page Styles */
.article-detail-page {
    background: #f8f9fa;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(1, 26, 45, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(169, 4, 24, 0.02) 0%, transparent 50%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Enhanced Article Card - Same as main page */
.enhanced-article-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.enhanced-article-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #e9ecef;
    transition: all 0.3s ease;
}

.enhanced-article-item:hover::before {
    background: #a90418;
    width: 6px;
}

/* Breadcrumb Navigation */
.breadcrumb-navigation {
    margin-bottom: 2rem;
}

.breadcrumb-row {
    margin-bottom: 0.75rem;
}

.breadcrumb-row:last-child {
    margin-bottom: 0;
}

/* Back Navigation */
.back-to-analysis {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-analysis:hover {
    color: #011a2d;
    text-decoration: none;
    transform: translateX(-3px);
}

/* Asset Category Badge */
.asset-category {
    background: #011a2d;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 1rem;
}

/* Article Title */
.enhanced-article-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #011a2d;
    line-height: 1.3;
    margin-bottom: 1rem;
    font-family: 'Georgia', serif;
}

.enhanced-article-title a {
    color: inherit;
    text-decoration: none;
}

/* Article Excerpt */
.enhanced-article-excerpt {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Author and Date Row */
.author-date-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.author-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #011a2d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 600;
}

.author-name {
    font-weight: 600;
    color: #343a40;
    font-size: 0.95rem;
}

.publish-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Social Share */
.social-share {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.social-share-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #343a40;
}

.social-share-buttons {
    display: flex;
    gap: 0.5rem;
}

.social-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-btn.twitter {
    background: #1da1f2;
    color: white;
}

.social-btn.linkedin {
    background: #0077b5;
    color: white;
}

.social-btn.facebook {
    background: #1877f2;
    color: white;
}

.social-btn.email {
    background: #6c757d;
    color: white;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    color: white;
    text-decoration: none;
}

/* Featured Image */
.featured-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Content Body */
.content-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #343a40;
    margin-bottom: 2rem;
}

.content-body p {
    margin-bottom: 1.5rem;
}

.content-body h2,
.content-body h3,
.content-body h4 {
    color: #011a2d;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.content-body h2 {
    font-size: 1.5rem;
    border-bottom: 2px solid #a90418;
    padding-bottom: 0.5rem;
}

.content-body h3 {
    font-size: 1.3rem;
}

.content-body h4 {
    font-size: 1.1rem;
}

/* External Link Alert */
.external-link-alert {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-left: 4px solid #a90418;
    border-radius: 8px;
    padding: 2rem;
    margin: 2rem 0;
}

.external-link-btn {
    background: #a90418;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.external-link-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Related Articles Section - Using same card design */
.related-articles-section {
    margin-top: 3rem;
}

.related-articles-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #011a2d;
    margin-bottom: 2rem;
    text-align: left;
}

.articles-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Meta separator */
.meta-separator {
    color: #6c757d;
    font-weight: 300;
}

/* Article actions */
.article-actions {
    margin-left: auto;
}

.read-more-btn {
    background: #a90418;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: #8b0314;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Article Layout for related articles */
.article-layout {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.article-image-container {
    position: relative;
    flex-shrink: 0;
    width: 200px;
    height: 130px;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.enhanced-article-item:hover .article-image {
    transform: scale(1.05);
}

.article-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.article-meta-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.publish-time {
    color: #6c757d;
}

.enhanced-article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.title-link {
    color: #011a2d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.title-link:hover {
    color: #a90418;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-article-title {
        font-size: 1.8rem;
    }

    .enhanced-article-item {
        padding: 1.5rem;
    }

    .author-date-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .social-share {
        flex-wrap: wrap;
    }

    .article-layout {
        flex-direction: column;
    }

    .article-image-container {
        width: 100%;
        height: 200px;
    }
}

@media (max-width: 576px) {
    .article-detail-page {
        padding: 1rem 0;
    }

    .enhanced-article-title {
        font-size: 1.5rem;
    }

    .enhanced-article-item {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block body %}
<div class="article-detail-page">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <!-- Main Article Card -->
                <article class="enhanced-article-item">
                    <!-- Breadcrumb Navigation -->
                    <div class="breadcrumb-navigation">
                        <!-- First Row: Back to Market Analysis -->
                        <div class="breadcrumb-row">
                            <a href="{{ path('app_market_analysis') }}" class="back-to-analysis">
                                <i class="fas fa-arrow-left"></i>
                                Back to Market Analysis
                            </a>
                        </div>

                        <!-- Second Row: Article Category -->
                        <div class="breadcrumb-row">
                            <span class="asset-category {{ article.assetType }}">{{ article.assetTypeLabel }}</span>
                        </div>
                    </div>

                    <!-- 3. Article Title -->
                    <h1 class="enhanced-article-title">{{ article.title }}</h1>

                    <!-- 4. Article Excerpt/Description -->
                    {% if article.excerpt %}
                    <p class="enhanced-article-excerpt">{{ article.excerpt }}</p>
                    {% endif %}

                    <!-- 5. Author and Publication Date Row -->
                    <div class="author-date-row">
                        <div class="author-info">
                            <div class="author-avatar-small">
                                {% if article.author %}
                                    {{ article.author|slice(0, 1)|upper }}
                                {% else %}
                                    CA
                                {% endif %}
                            </div>
                            <span class="author-name">
                                {% if article.author %}
                                    {{ article.author }}
                                {% else %}
                                    Capitol Academy
                                {% endif %}
                            </span>
                        </div>
                        <div class="publish-date">
                            <i class="fas fa-calendar-alt"></i>
                            {{ article.publishDate|date('F j, Y') }}
                        </div>
                    </div>

                    <!-- 6. Social Share Options -->
                    <div class="social-share">
                        <span class="social-share-label">Share:</span>
                        <div class="social-share-buttons">
                            <a href="https://twitter.com/intent/tweet?text={{ article.title|url_encode }}&url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn twitter" title="Share on Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn linkedin" title="Share on LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn facebook" title="Share on Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="mailto:?subject={{ article.title|url_encode }}&body={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               class="social-btn email" title="Share via Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>

                    <!-- 7. Featured Image -->
                    {% if article.featuredImageUrl %}
                    <img src="{{ article.featuredImageUrl }}" alt="{{ article.title }}" class="featured-image">
                    {% endif %}

                    <!-- 8. Main Article Content -->
                    {% if article.content %}
                    <div class="content-body">
                        {{ article.content|raw }}
                    </div>
                    {% endif %}

                    <!-- 9. Social Share Options (Repeated at Bottom) -->
                    <div class="social-share" style="border-top: 1px solid #e9ecef; padding-top: 1.5rem; margin-top: 2rem;">
                        <span class="social-share-label">Share this analysis:</span>
                        <div class="social-share-buttons">
                            <a href="https://twitter.com/intent/tweet?text={{ article.title|url_encode }}&url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn twitter" title="Share on Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn linkedin" title="Share on LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               target="_blank" class="social-btn facebook" title="Share on Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="mailto:?subject={{ article.title|url_encode }}&body={{ url('app_market_analysis_show_seo', {'slug': article.slug})|url_encode }}"
                               class="social-btn email" title="Share via Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </article>
            </div>
        </div>

        <!-- Related Articles Section - Using Same Card Design as Main Page -->
        {% if relatedArticles|length > 0 %}
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="related-articles-section">
                    <h2 class="related-articles-title">Related Articles</h2>
                    <div class="articles-list">
                        {% for relatedArticle in relatedArticles %}
                        <article class="enhanced-article-item">
                            <div class="article-layout">
                                <!-- Article Thumbnail -->
                                <div class="article-image-container">
                                    <img src="{{ relatedArticle.thumbnailUrl }}"
                                         alt="{{ relatedArticle.title }}"
                                         class="article-image"
                                         loading="lazy">
                                </div>

                                <!-- Article Content -->
                                <div class="article-content-area">
                                    <!-- Meta Information -->
                                    <div class="article-meta-row">
                                        <span class="asset-category {{ relatedArticle.assetType }}">{{ relatedArticle.assetTypeLabel }}</span>
                                        <span class="meta-separator">•</span>
                                        <span class="publish-time">{{ relatedArticle.publishDate|date('M j, Y') }}</span>
                                    </div>

                                    <!-- Article Title -->
                                    <h3 class="enhanced-article-title" style="font-size: 1.3rem;">
                                        <a href="{{ path('app_market_analysis_show_seo', {'slug': relatedArticle.slug}) }}" class="title-link">
                                            {{ relatedArticle.title }}
                                        </a>
                                    </h3>

                                    <!-- Article Excerpt -->
                                    <p class="enhanced-article-excerpt">{{ relatedArticle.shortExcerpt(120) }}</p>

                                    <!-- Article Footer -->
                                    <div class="enhanced-article-footer">
                                        <div class="author-info">
                                            {% if relatedArticle.author %}
                                                <div class="author-avatar-small">{{ relatedArticle.author|slice(0, 1)|upper }}</div>
                                                <span class="author-name">{{ relatedArticle.author }}</span>
                                            {% else %}
                                                <div class="author-avatar-small">CA</div>
                                                <span class="author-name">Capitol Academy</span>
                                            {% endif %}
                                        </div>

                                        <div class="article-actions">
                                            <a href="{{ path('app_market_analysis_show_seo', {'slug': relatedArticle.slug}) }}" class="read-more-btn">
                                                <span>Read Analysis</span>
                                                <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </article>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}