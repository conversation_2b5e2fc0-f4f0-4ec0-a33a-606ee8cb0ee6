/* General Demo Style */
@import url(//fonts.googleapis.com/css?family=Lato:300,400,700);





.container {
	width: 100%;
	position: relative;
}


.main {
	width:100%;
	max-width: 995px;
	padding-right:2px;
	margin: 0 ;
	position: relative;
}



/* Header Style */
.codrops-top {
	line-height: 24px;
	font-size: 11px;
	background: #fff;
	background: rgba(255, 255, 255, 0.5);
	text-transform: uppercase;
	z-index: 9999;
	position: relative;
	font-family: Cambria, Georgia, serif;
	box-shadow: 1px 0px 2px rgba(0,0,0,0.2);
}

.codrops-top a {
	padding: 0px 10px;
	letter-spacing: 1px;
	color: #333;
	text-shadow: 0 -1px 0 #fff;
	display: inline-block;
}

.codrops-top a:hover {
	background: rgba(255,255,255,0.8);
	color: #000;
}

.codrops-top span.right {
	float: right;
}

.codrops-top span.right a {
	float: left;
	display: block;
}

/* Demo <PERSON>tons Style */
.codrops-demos {
	float: right;
	clear: none;
	padding-top: 10px;
}

.codrops-demos a {
    display: inline-block;
    margin: 10px 10px 10px 0;
    color: #666;
    font-weight: 700;
    line-height: 30px;
    border-bottom: 4px solid transparent;
}

.codrops-demos a:hover {
	color: #000;
	border-color: #000;
}

.codrops-demos a.current-demo,
.codrops-demos a.current-demo:hover {
	color: #aaa;
	border-color: #aaa;
}

.column {
	width: 50%;
	float: left;
	padding: 20px;
	min-height: 300px;
}

.column p {
	text-align: left;
}

.column-20 {
	width: 20%;
}

.column-80 {
	width: 80%;
}

.js .fixed-bar {
	position: fixed;
	width: 100%;
	left: 0;
	bottom: 0;
}

.js .gallery {
	width: 100%;
	max-width: 450px;
	margin: 0 auto;
	border-radius: 20px;
	position: relative;
	background: #000 url(http://capitol-academy.com/ar/images/black_denim.png);
	box-shadow: 0 2px 1px rgba(255,255,255,0.9), 0 -2px 1px rgba(255,255,255,0.9);
}

.lt-ie8 .elastislide-list {
	display: none;
}

@media screen and (max-width: 995px) {
	.codrops-demos {
		float: left;
		clear: both;
	}
}