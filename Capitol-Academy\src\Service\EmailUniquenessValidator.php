<?php

namespace App\Service;

use App\Repository\AdminRepository;
use App\Repository\UserRepository;

class EmailUniquenessValidator
{
    public function __construct(
        private UserRepository $userRepository,
        private AdminRepository $adminRepository
    ) {}

    /**
     * Check if email is unique across both User and Admin entities
     */
    public function isEmailUnique(string $email, ?object $excludeEntity = null): bool
    {
        // Check if email exists in User table
        $existingUser = $this->userRepository->findOneBy(['email' => $email]);
        if ($existingUser && $existingUser !== $excludeEntity) {
            return false;
        }

        // Check if email exists in Admin table
        $existingAdmin = $this->adminRepository->findOneBy(['email' => $email]);
        if ($existingAdmin && $existingAdmin !== $excludeEntity) {
            return false;
        }

        return true;
    }

    /**
     * Get the entity type that already uses this email
     */
    public function getEmailOwnerType(string $email): ?string
    {
        $existingUser = $this->userRepository->findOneBy(['email' => $email]);
        if ($existingUser) {
            return 'user';
        }

        $existingAdmin = $this->adminRepository->findOneBy(['email' => $email]);
        if ($existingAdmin) {
            return 'admin';
        }

        return null;
    }

    /**
     * Get appropriate error message for duplicate email
     */
    public function getEmailDuplicateMessage(string $email): string
    {
        $ownerType = $this->getEmailOwnerType($email);
        
        switch ($ownerType) {
            case 'user':
                return 'This email is already registered as a user account. Please use a different email or login with your existing account.';
            case 'admin':
                return 'This email is already registered as an admin account. Please use a different email.';
            default:
                return 'This email is already in use. Please use a different email address.';
        }
    }
}
