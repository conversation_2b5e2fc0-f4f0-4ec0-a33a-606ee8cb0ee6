# Capitol Academy Admin Forms Documentation

## Overview

This document provides comprehensive documentation for all admin create forms in the Capitol Academy system. All forms follow standardized design patterns, validation rules, and accessibility guidelines.

## Design Standards

### Header Design
- **Gradient Background**: `linear-gradient(135deg, #011a2d 0%, #1a3461 100%)`
- **Typography**: Font-size 1.8rem, font-weight 600
- **Icon Styling**: 2rem font-size with proper spacing
- **Button Styling**: White background with hover effects
- **Responsive Layout**: Proper column structure for all screen sizes

### Form Field Standards
- **Field Heights**: `calc(1.6em + 1.25rem + 4px)` for all input fields
- **Enhanced Borders**: 2px solid #ced4da with hover/focus effects
- **Professional Icons**: Blue primary color (#1e3c72) for form label icons
- **Validation**: Hidden until form submission with proper error messaging

### Footer Design
- **Background**: #f8f9fa with subtle border
- **Submit Button**: Green gradient (`linear-gradient(135deg, #28a745 0%, #20c997 100%)`)
- **Cancel Button**: Standard secondary styling
- **Layout**: Two-column responsive design

## Form Validation Rules

### Enrollment Creation Form (`/admin/enrollments/create`)

#### Required Fields
- **Student Selection** (`user_id`)
  - Must select a valid registered student
  - Dropdown with search functionality
  - Real-time validation feedback

- **Course Selection** (`course_id`)
  - Must select an active course
  - Displays course preview with details
  - Shows pricing information

#### Optional Fields
- **Admin Notes** (`notes`)
  - Maximum 1000 characters
  - Administrative use only
  - Not visible to students

#### Enrollment Options
- **Send Notification** (`send_notification`)
  - Default: Enabled
  - Sends email to student when checked

- **Mark Active** (`mark_active`)
  - Default: Enabled
  - Immediately activates enrollment

#### Validation Messages
- Student selection: "Please select a student."
- Course selection: "Please select a course."
- Form submission: Real-time validation with visual feedback

### Video Creation Form (`/admin/videos/new`)

#### Required Fields
- **Video Title** (`title`)
  - 3-255 characters
  - Must be unique
  - Real-time validation

- **Video Description** (`description`)
  - Minimum 10 characters
  - Rich text support
  - Character counter

- **Video File** (`videoFile`)
  - Supported formats: MP4, AVI, MOV, WMV
  - Maximum size: 500MB
  - Progress indicator during upload

#### Optional Fields
- **Thumbnail Image** (`thumbnailFile`)
  - Recommended: 1280x720px (16:9 ratio)
  - Formats: JPEG, PNG
  - Maximum size: 5MB
  - Live preview functionality

- **Pricing Type** (`pricing_type`)
  - Options: Free, Premium
  - Dynamic price field display

- **Price** (`price`)
  - Required if Premium selected
  - Minimum: $0.01
  - Maximum: $999.99
  - Currency formatting

#### Validation Messages
- Title: "Video title must be between 3 and 255 characters."
- Description: "Description must be at least 10 characters long."
- Video file: "Please select a valid video file."
- Price: "Price must be between $0.01 and $999.99 for premium videos."

### Video Plans Creation Form (`/admin/video-plans/new`)

#### Required Fields
- **Plan Name** (`name`)
  - 3-100 characters
  - Must be unique
  - Alphanumeric and spaces only

- **Plan Description** (`description`)
  - Minimum 20 characters
  - Maximum 500 characters

- **Video Selection** (`videos[]`)
  - Must select at least one video
  - Multi-select with thumbnails
  - Visual grouping by category

#### Optional Fields
- **Plan Price** (`price`)
  - Default: Free
  - Bundle pricing logic

- **Access Duration** (`access_duration`)
  - Options: 30, 60, 90, 365 days, Lifetime
  - Default: Lifetime

#### Validation Messages
- Plan name: "Plan name must be between 3 and 100 characters."
- Description: "Description must be between 20 and 500 characters."
- Video selection: "Please select at least one video for this plan."

### Categories Creation Form (`/admin/categories/new`)

#### Required Fields
- **Category Name** (`name`)
  - 2-50 characters
  - Must be unique
  - No special characters except hyphens and underscores

- **Category Slug** (`slug`)
  - Auto-generated from name
  - URL-friendly format
  - Uniqueness validation

#### Optional Fields
- **Description** (`description`)
  - Maximum 255 characters
  - SEO-friendly content

- **Parent Category** (`parent_id`)
  - Hierarchical structure support
  - Prevents circular references

#### Validation Messages
- Name: "Category name must be between 2 and 50 characters."
- Slug: "Category slug must be unique and URL-friendly."
- Parent: "Cannot select a child category as parent."

### Instructors Creation Form (`/admin/instructor/new`)

#### Required Fields
- **First Name** (`firstName`)
  - 2-50 characters
  - Letters only

- **Last Name** (`lastName`)
  - 2-50 characters
  - Letters only

- **Email** (`email`)
  - Valid email format
  - Must be unique
  - Professional domain preferred

- **Specialization** (`specialization`)
  - Minimum 10 characters
  - Professional description

#### Optional Fields
- **Bio** (`bio`)
  - Maximum 1000 characters
  - Rich text support
  - Professional background

- **Profile Image** (`profileImageFile`)
  - Recommended: 400x400px (1:1 ratio)
  - Formats: JPEG, PNG
  - Maximum size: 2MB

- **Social Links** (`socialLinks`)
  - LinkedIn, Twitter, Website
  - URL validation

#### Validation Messages
- Name fields: "Name must contain only letters and be 2-50 characters."
- Email: "Please provide a valid, unique email address."
- Specialization: "Specialization must be at least 10 characters."

### Partners Creation Form (`/admin/partners/create`)

#### Required Fields
- **Company Name** (`companyName`)
  - 2-100 characters
  - Must be unique

- **Contact Email** (`contactEmail`)
  - Valid email format
  - Business email preferred

- **Partnership Type** (`partnershipType`)
  - Options: Strategic, Technology, Content, Affiliate
  - Affects available features

#### Optional Fields
- **Company Logo** (`logoFile`)
  - Recommended: 300x150px (2:1 ratio)
  - Formats: JPEG, PNG, SVG
  - Maximum size: 1MB

- **Website URL** (`websiteUrl`)
  - Valid URL format
  - HTTPS preferred

- **Description** (`description`)
  - Maximum 500 characters
  - Partnership details

#### Validation Messages
- Company name: "Company name must be between 2 and 100 characters."
- Email: "Please provide a valid business email address."
- Website: "Please provide a valid website URL."

### Promotional Banners Creation Form (`/admin/promotional-banners/create`)

#### Required Fields
- **Banner Title** (`title`)
  - 5-100 characters
  - Marketing-focused content

- **Banner Message** (`message`)
  - 10-200 characters
  - Call-to-action text

- **Display Duration** (`displayDuration`)
  - Start and end dates
  - Cannot be in the past

#### Optional Fields
- **Banner Image** (`bannerImageFile`)
  - Recommended: 1200x300px (4:1 ratio)
  - Formats: JPEG, PNG
  - Maximum size: 3MB

- **Target URL** (`targetUrl`)
  - Valid URL format
  - Internal or external links

- **Target Audience** (`targetAudience`)
  - Options: All Users, Students, Instructors, Guests
  - Affects display logic

#### Validation Messages
- Title: "Banner title must be between 5 and 100 characters."
- Message: "Banner message must be between 10 and 200 characters."
- Duration: "End date must be after start date and in the future."

## User Interaction Patterns

### Form Navigation
- **Tab Order**: Logical flow from top to bottom, left to right
- **Keyboard Shortcuts**: 
  - Ctrl/Cmd + S: Submit form
  - Escape: Cancel/go back (with confirmation)
- **Focus Management**: Clear visual indicators, proper focus trapping

### Real-time Validation
- **Input Events**: Validation on blur for better UX
- **Visual Feedback**: Green checkmarks for valid fields, red borders for errors
- **Error Messages**: Contextual, helpful, and accessible
- **Success States**: Positive reinforcement for completed fields

### File Upload Experience
- **Progress Indicators**: Visual progress bars with speed information
- **Preview Functionality**: Immediate preview for images and videos
- **File Information**: Size, type, and dimension details
- **Error Handling**: Clear messages for unsupported files or size limits

### Dynamic Content
- **Course Preview**: Real-time course information display
- **Conditional Fields**: Show/hide based on selections
- **Auto-save**: Draft functionality for long forms
- **Character Counters**: Real-time feedback for text fields

## Accessibility Features

### WCAG 2.1 AA Compliance
- **Color Contrast**: Minimum 4.5:1 ratio for all text
- **Keyboard Navigation**: Full functionality without mouse
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Clear focus indicators and logical tab order

### Assistive Technology Support
- **ARIA Labels**: Descriptive labels for all form elements
- **Live Regions**: Dynamic content announcements
- **Error Announcements**: Screen reader notifications for validation errors
- **Help Text**: Contextual assistance for complex fields

### Responsive Design
- **Mobile Optimization**: Touch-friendly targets (minimum 44px)
- **Flexible Layouts**: Adapts to different screen sizes
- **Reduced Motion**: Respects user preferences for animations
- **High Contrast**: Support for high contrast mode

## Performance Optimizations

### Loading Performance
- **Lazy Loading**: Images and non-critical resources
- **Code Splitting**: JavaScript loaded as needed
- **Caching**: Efficient browser caching strategies
- **Compression**: Optimized assets and responses

### Runtime Performance
- **Debounced Validation**: Prevents excessive API calls
- **Virtual Scrolling**: For large dropdown lists
- **Memory Management**: Proper cleanup of event listeners
- **GPU Acceleration**: Hardware-accelerated animations

### Network Optimization
- **Progressive Enhancement**: Core functionality works without JavaScript
- **Offline Support**: Basic form validation works offline
- **Error Recovery**: Graceful handling of network failures
- **Retry Logic**: Automatic retry for failed submissions

## Browser Support

### Supported Browsers
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile Safari**: 14+
- **Chrome Mobile**: 90+

### Fallbacks
- **CSS Grid**: Flexbox fallback for older browsers
- **JavaScript Features**: Polyfills for missing functionality
- **Form Validation**: Server-side validation as backup
- **Progressive Enhancement**: Core functionality without modern features

## Security Considerations

### Input Validation
- **Client-side**: User experience enhancement only
- **Server-side**: Primary security validation
- **Sanitization**: All inputs properly sanitized
- **CSRF Protection**: Token-based protection for all forms

### File Upload Security
- **Type Validation**: Server-side file type checking
- **Size Limits**: Enforced on both client and server
- **Virus Scanning**: Uploaded files scanned for malware
- **Storage Security**: Secure file storage with proper permissions

### Data Protection
- **Encryption**: Sensitive data encrypted in transit and at rest
- **Access Control**: Role-based permissions for form access
- **Audit Logging**: All form submissions logged for security
- **Data Retention**: Proper data lifecycle management

## Maintenance Guidelines

### Code Organization
- **Consistent Structure**: All forms follow the same template pattern
- **Reusable Components**: Shared CSS and JavaScript components
- **Documentation**: Inline comments for complex functionality
- **Version Control**: Proper git workflow for changes

### Testing Requirements
- **Unit Tests**: JavaScript validation functions
- **Integration Tests**: Form submission workflows
- **Accessibility Tests**: Automated and manual accessibility testing
- **Cross-browser Tests**: Functionality across supported browsers

### Update Procedures
- **Staging Environment**: All changes tested in staging first
- **Rollback Plan**: Quick rollback procedures for issues
- **User Communication**: Advance notice for significant changes
- **Training Materials**: Updated documentation for admin users

---

*Last Updated: 2025-01-04*
*Version: 1.0*
*Maintained by: Capitol Academy Development Team*
