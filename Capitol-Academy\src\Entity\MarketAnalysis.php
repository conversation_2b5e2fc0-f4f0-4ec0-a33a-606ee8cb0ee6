<?php

namespace App\Entity;

use App\Repository\MarketAnalysisRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: MarketAnalysisRepository::class)]
#[ORM\Table(name: 'market_analysis')]
#[ORM\Index(columns: ['asset_type'], name: 'idx_asset_type')]
#[ORM\Index(columns: ['publish_date'], name: 'idx_publish_date')]

class MarketAnalysis
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank(message: 'Asset type is required')]
    #[Assert\Choice(
        choices: ['stocks', 'forex', 'crypto', 'crude_oil', 'gold', 'commodities'],
        message: 'Please select a valid asset type'
    )]
    private ?string $assetType = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Title is required')]
    #[Assert\Length(
        max: 255,
        maxMessage: 'Title cannot be longer than 255 characters'
    )]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'Excerpt is required')]
    #[Assert\Length(
        max: 500,
        maxMessage: 'Excerpt cannot be longer than 500 characters'
    )]
    private ?string $excerpt = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $content = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(
        max: 255,
        maxMessage: 'Thumbnail image path cannot be longer than 255 characters'
    )]
    private ?string $thumbnailImage = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(
        max: 255,
        maxMessage: 'Featured image path cannot be longer than 255 characters'
    )]
    private ?string $featuredImage = null;





    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Assert\NotNull(message: 'Publish date is required')]
    private ?\DateTimeInterface $publishDate = null;





    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Length(
        max: 255,
        maxMessage: 'Author name cannot be longer than 255 characters'
    )]
    private ?string $author = null;





    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $views = 0;

    #[ORM\Column(type: 'boolean', options: ['default' => true])]
    private bool $isActive = true;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        $this->publishDate = new \DateTime();
        $this->views = 0;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAssetType(): ?string
    {
        return $this->assetType;
    }

    public function setAssetType(string $assetType): static
    {
        $this->assetType = $assetType;
        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;
        return $this;
    }

    public function getExcerpt(): ?string
    {
        return $this->excerpt;
    }

    public function setExcerpt(string $excerpt): static
    {
        $this->excerpt = $excerpt;
        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(?string $content): static
    {
        $this->content = $content;
        return $this;
    }

    public function getThumbnailImage(): ?string
    {
        return $this->thumbnailImage;
    }

    public function setThumbnailImage(?string $thumbnailImage): static
    {
        $this->thumbnailImage = $thumbnailImage;
        return $this;
    }

    public function getFeaturedImage(): ?string
    {
        return $this->featuredImage;
    }

    public function setFeaturedImage(?string $featuredImage): static
    {
        $this->featuredImage = $featuredImage;
        return $this;
    }





    public function getPublishDate(): ?\DateTimeInterface
    {
        return $this->publishDate;
    }

    public function setPublishDate(\DateTimeInterface $publishDate): static
    {
        $this->publishDate = $publishDate;
        return $this;
    }





    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function getAuthor(): ?string
    {
        return $this->author;
    }

    public function setAuthor(?string $author): static
    {
        $this->author = $author;
        return $this;
    }





    public function getViews(): int
    {
        return $this->views;
    }

    public function setViews(int $views): static
    {
        $this->views = $views;
        return $this;
    }

    public function incrementViews(): static
    {
        $this->views++;
        return $this;
    }



    // Helper methods
    public function getAssetTypeLabel(): string
    {
        return match($this->assetType) {
            'stocks' => 'Stocks',
            'forex' => 'Forex',
            'crypto' => 'Crypto',
            'crude_oil' => 'Crude Oil',
            'gold' => 'Gold',
            'commodities' => 'Commodities',
            default => 'Unknown'
        };
    }

    public function isPublished(): bool
    {
        return true; // All articles are considered published now
    }

    public function getFormattedPublishDate(): string
    {
        return $this->publishDate ? $this->publishDate->format('M d, Y') : '';
    }

    public function getRelativeTime(): string
    {
        if (!$this->publishDate) {
            return '';
        }

        $now = new \DateTime();
        $diff = $now->diff($this->publishDate);

        if ($diff->days == 0) {
            if ($diff->h == 0) {
                if ($diff->i == 0) {
                    return 'Just now';
                }
                return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
            }
            return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
        } elseif ($diff->days == 1) {
            return 'Yesterday';
        } elseif ($diff->days < 7) {
            return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
        } elseif ($diff->days < 30) {
            $weeks = floor($diff->days / 7);
            return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
        } elseif ($diff->days < 365) {
            $months = floor($diff->days / 30);
            return $months . ' month' . ($months > 1 ? 's' : '') . ' ago';
        } else {
            $years = floor($diff->days / 365);
            return $years . ' year' . ($years > 1 ? 's' : '') . ' ago';
        }
    }

    public function getStatusBadgeClass(): string
    {
        return $this->isActive ? 'badge-success' : 'badge-secondary';
    }

    public function getStatusLabel(): string
    {
        return $this->isActive ? 'Published' : 'Draft';
    }

    public function getThumbnailUrl(): string
    {
        if ($this->thumbnailImage) {
            return '/uploads/market_analysis/' . $this->thumbnailImage;
        }

        // Return asset-specific placeholder based on asset type
        switch ($this->assetType) {
            case 'stocks':
                return '/images/placeholders/image-placeholder.png';
            case 'forex':
                return '/images/placeholders/image-placeholder.png';
            case 'crypto':
                return '/images/placeholders/image-placeholder.png';
            case 'gold':
                return '/images/placeholders/image-placeholder.png';
            case 'crude_oil':
                return '/images/placeholders/image-placeholder.png';
            case 'commodities':
                return '/images/placeholders/image-placeholder.png';
            default:
                return '/images/placeholders/image-placeholder.png';
        }
    }

    public function getFeaturedImageUrl(): string
    {
        if ($this->featuredImage) {
            return '/uploads/market_analysis/' . $this->featuredImage;
        }

        // Fallback to thumbnail if no featured image
        return $this->getThumbnailUrl();
    }

    public function getShortExcerpt(int $length = 100): string
    {
        if (strlen($this->excerpt) <= $length) {
            return $this->excerpt;
        }
        return substr($this->excerpt, 0, $length) . '...';
    }



    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        return $this;
    }



    /**
     * Generate a URL-friendly slug from the title
     */
    public function getTitleSlug(): string
    {
        return $this->slugify($this->title ?? '');
    }

    /**
     * Generate a URL-friendly slug from the author/publisher
     */
    public function getAuthorSlug(): string
    {
        $author = $this->author ?? 'capitol-academy';
        return $this->slugify($author);
    }

    /**
     * Generate the complete SEO-friendly slug for URLs
     * Format: {title-slug}-{author-slug}
     */
    public function getSlug(): string
    {
        return $this->getTitleSlug() . '-' . $this->getAuthorSlug();
    }

    /**
     * Convert a string to a URL-friendly slug
     */
    private function slugify(string $text): string
    {
        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-zA-Z0-9]+/', '-', $text);

        // Convert to lowercase
        $slug = strtolower($slug);

        // Remove leading/trailing hyphens
        $slug = trim($slug, '-');

        // Limit length to 50 characters for URL friendliness
        if (strlen($slug) > 50) {
            $slug = substr($slug, 0, 50);
            $slug = rtrim($slug, '-');
        }

        return $slug ?: 'article';
    }
}
