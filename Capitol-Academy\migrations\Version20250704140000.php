<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove tags column from video table
 */
final class Version20250704140000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove tags column from video table';
    }

    public function up(Schema $schema): void
    {
        // Remove tags column from video table
        $this->addSql('ALTER TABLE video DROP tags');
    }

    public function down(Schema $schema): void
    {
        // Add tags column back to video table
        $this->addSql('ALTER TABLE video ADD tags JSON DEFAULT NULL');
    }
}
