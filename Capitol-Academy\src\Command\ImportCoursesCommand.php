<?php

namespace App\Command;

use App\Entity\Course;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:import-courses',
    description: 'Import courses from course.sql data',
)]
class ImportCoursesCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $coursesData = [
            [
                'id' => 1,
                'code' => 'FMA101',
                'title' => 'Global Financial Markets',
                'description' => 'Recent years have witnessed an increased interest in the financial markets as well in developed countries and in developing countries, thanks to its important role in the economic field due to its high liquidity.',
                'category' => 'Finance',
                'banner_image' => '/uploads/courses/banners/images-684f14c19f7f5.jpg',
                'content' => 'Financial markets have a key role in mobilizing and allocating savings to support the economy and in the purchase and sale of a financial instrument for capital profit. However, many investors are looking for profit without doing its due diligence and studying the market, and they fall easily into the market obstacles and get heavy losses.',
                'slug' => 'global-financial-markets',
                'is_active' => true,
                'price' => '500.00',
                'level' => 'Beginner',
                'duration' => 40,
                'thumbnail_image' => '/uploads/courses/thumbnails/gfm-684f14c19d6c4.png',
                'learning_outcomes' => [
                    'Choose the market that fits best your needs',
                    'Make the difference between the different financial markets and their instruments',
                    'Select a portfolio with different instruments according to the features of each market'
                ],
                'features' => [
                    'You will be able to interact with analysts and experts of the financial markets during a professional workshop.',
                    'You will be trained and will be followed up by our in-house experts.',
                    'You will pass a test in order to evaluate your training.',
                    'You will get a "General Introduction on Risk Management" Certificate from Capitol Academy, after succeeding your test.'
                ]
            ],
            [
                'code' => 'TEC',
                'title' => 'Technical Analysis',
                'description' => 'Master chart patterns, indicators, and technical analysis tools to make informed trading decisions.',
                'category' => 'Analysis',
                'content' => 'Technical analysis is the study of past market data, primarily price and volume, to forecast future price movements.',
                'slug' => 'technical-analysis',
                'is_active' => true,
                'learning_outcomes' => [
                    'Understand chart patterns and their significance',
                    'Master technical indicators and oscillators',
                    'Develop skills in trend analysis and market timing'
                ],
                'features' => [
                    'Hands-on chart analysis practice',
                    'Real-time market examples',
                    'Professional trading tools training'
                ]
            ],
            [
                'code' => 'TRS',
                'title' => 'Trading Strategies',
                'description' => 'Learn professional trading strategies and how to implement them effectively in different market conditions.',
                'category' => 'Strategy',
                'content' => 'Professional trading strategies that have been tested and proven in various market conditions.',
                'slug' => 'trading-strategies',
                'is_active' => true,
                'learning_outcomes' => [
                    'Master multiple trading strategies',
                    'Learn strategy selection for different markets',
                    'Understand risk-reward optimization'
                ],
                'features' => [
                    'Strategy backtesting workshops',
                    'Live trading simulations',
                    'Performance analysis tools'
                ]
            ],
            [
                'code' => 'FUN',
                'title' => 'Fundamental Analysis',
                'description' => 'Understand economic indicators, news events, and fundamental factors that drive market movements.',
                'category' => 'Analysis',
                'content' => 'Fundamental analysis involves evaluating economic, financial, and other qualitative and quantitative factors.',
                'slug' => 'fundamental-analysis',
                'is_active' => true,
                'learning_outcomes' => [
                    'Analyze economic indicators and their impact',
                    'Understand central bank policies',
                    'Evaluate market sentiment and news events'
                ],
                'features' => [
                    'Economic calendar training',
                    'News analysis workshops',
                    'Central bank policy interpretation'
                ]
            ],
            [
                'code' => 'SSA',
                'title' => 'Psychological Analysis',
                'description' => 'Master the psychological aspects of trading and develop the mental discipline required for success.',
                'category' => 'Psychology',
                'content' => 'Trading psychology is crucial for success in financial markets.',
                'slug' => 'psychological-analysis',
                'is_active' => true,
                'learning_outcomes' => [
                    'Develop emotional control in trading',
                    'Understand cognitive biases',
                    'Build mental resilience and discipline'
                ],
                'features' => [
                    'Psychology assessment tools',
                    'Mental training exercises',
                    'Stress management techniques'
                ]
            ],
            [
                'code' => 'MMA',
                'title' => 'Capital Management',
                'description' => 'Learn essential money management techniques and position sizing strategies for long-term success.',
                'category' => 'Management',
                'content' => 'Proper capital management is the foundation of successful trading.',
                'slug' => 'capital-management',
                'is_active' => true,
                'learning_outcomes' => [
                    'Master position sizing techniques',
                    'Understand portfolio allocation',
                    'Learn capital preservation strategies'
                ],
                'features' => [
                    'Portfolio management tools',
                    'Risk calculation worksheets',
                    'Capital allocation strategies'
                ]
            ],
            [
                'code' => 'RSK',
                'title' => 'Risk Management',
                'description' => 'Understand and implement risk management strategies to protect your trading capital.',
                'category' => 'Management',
                'content' => 'Risk management is essential for preserving capital and achieving long-term success.',
                'slug' => 'risk-management',
                'is_active' => true,
                'learning_outcomes' => [
                    'Implement stop-loss strategies',
                    'Calculate risk-reward ratios',
                    'Develop risk assessment skills'
                ],
                'features' => [
                    'Risk assessment tools',
                    'Stop-loss optimization',
                    'Portfolio risk analysis'
                ]
            ],
            [
                'code' => 'DTR',
                'title' => 'Day Trading',
                'description' => 'Learn the skills and strategies needed for successful day trading.',
                'category' => 'Strategy',
                'content' => 'Day trading requires specific skills, strategies, and risk management techniques.',
                'slug' => 'day-trading',
                'is_active' => true,
                'learning_outcomes' => [
                    'Master intraday trading techniques',
                    'Understand market microstructure',
                    'Develop scalping strategies'
                ],
                'features' => [
                    'Live trading sessions',
                    'Real-time market analysis',
                    'Professional trading platforms'
                ]
            ],
            [
                'code' => 'PRO',
                'title' => 'Professional Trader',
                'description' => 'Advanced course for becoming a professional trader with institutional-level skills.',
                'category' => 'Advanced',
                'content' => 'This comprehensive program prepares you for professional trading careers.',
                'slug' => 'professional-trader',
                'is_active' => true,
                'learning_outcomes' => [
                    'Develop institutional trading skills',
                    'Master advanced trading strategies',
                    'Understand professional risk management'
                ],
                'features' => [
                    'Institutional trading simulations',
                    'Professional certification',
                    'Career placement assistance'
                ]
            ]
        ];

        $imported = 0;
        $updated = 0;

        foreach ($coursesData as $courseData) {
            // Check if course already exists
            $existingCourse = $this->entityManager->getRepository(Course::class)->findOneBy(['code' => $courseData['code']]);
            
            if ($existingCourse) {
                // Update existing course
                $course = $existingCourse;
                $updated++;
                $io->text("Updating course: {$courseData['title']}");
            } else {
                // Create new course
                $course = new Course();
                $imported++;
                $io->text("Creating course: {$courseData['title']}");
            }

            $course->setCode($courseData['code']);
            $course->setTitle($courseData['title']);
            $course->setDescription($courseData['description']);
            $course->setCategory($courseData['category']);
            $course->setSlug($courseData['slug']);
            $course->setActive($courseData['is_active']);
            
            if (isset($courseData['content'])) {
                $course->setContent($courseData['content']);
            }
            
            if (isset($courseData['price'])) {
                $course->setPrice($courseData['price']);
            }
            
            if (isset($courseData['level'])) {
                $course->setLevel($courseData['level']);
            }
            
            if (isset($courseData['duration'])) {
                $course->setDuration($courseData['duration']);
            }
            
            if (isset($courseData['banner_image'])) {
                $course->setBannerImage($courseData['banner_image']);
            }
            
            if (isset($courseData['thumbnail_image'])) {
                $course->setThumbnailImage($courseData['thumbnail_image']);
            }
            
            if (isset($courseData['learning_outcomes'])) {
                $course->setLearningOutcomes($courseData['learning_outcomes']);
            }
            
            if (isset($courseData['features'])) {
                $course->setFeatures($courseData['features']);
            }

            if (!$existingCourse) {
                $course->setCreatedAt(new \DateTimeImmutable());
            }
            $course->setUpdatedAt(new \DateTimeImmutable());

            $this->entityManager->persist($course);
        }

        $this->entityManager->flush();

        $io->success("Course import completed! Imported: {$imported}, Updated: {$updated}");

        return Command::SUCCESS;
    }
}
