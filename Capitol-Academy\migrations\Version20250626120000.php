<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250626120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create market_analysis table for Capitol Academy Market Analysis feature';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE market_analysis (
            id INT AUTO_INCREMENT NOT NULL, 
            asset_type VARCHAR(50) NOT NULL, 
            title VARCHAR(255) NOT NULL, 
            excerpt LONGTEXT NOT NULL, 
            content LONGTEXT DEFAULT NULL, 
            thumbnail VARCHAR(255) DEFAULT NULL, 
            publish_date DATETIME NOT NULL, 
            status VARCHAR(20) NOT NULL, 
            is_featured TINYINT(1) NOT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            author VARCHAR(255) DEFAULT NULL, 
            external_link VARCHAR(500) DEFAULT NULL, 
            tags JSON DEFAULT NULL, 
            PRIMARY KEY(id),
            INDEX idx_asset_type (asset_type),
            INDEX idx_status (status),
            INDEX idx_publish_date (publish_date),
            INDEX idx_featured (is_featured)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE market_analysis');
    }
}
