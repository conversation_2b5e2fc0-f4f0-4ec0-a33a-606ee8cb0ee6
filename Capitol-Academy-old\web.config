﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <httpErrors>
      <remove statusCode="400" />
      <error statusCode="400" path="G:\PleskVhosts\capitol-academy.com\error_docs\bad_request.html" />
      <remove statusCode="401" />
      <error statusCode="401" path="G:\PleskVhosts\capitol-academy.com\error_docs\unauthorized.html" />
      <remove statusCode="403" />
      <error statusCode="403" path="G:\PleskVhosts\capitol-academy.com\error_docs\forbidden.html" />
      <remove statusCode="404" />
      <error statusCode="404" path="G:\PleskVhosts\capitol-academy.com\error_docs\not_found.html" />
      <remove statusCode="405" />
      <error statusCode="405" path="G:\PleskVhosts\capitol-academy.com\error_docs\method_not_allowed.html" />
      <remove statusCode="406" />
      <error statusCode="406" path="G:\PleskVhosts\capitol-academy.com\error_docs\not_acceptable.html" />
      <remove statusCode="407" />
      <error statusCode="407" path="G:\PleskVhosts\capitol-academy.com\error_docs\proxy_authentication_required.html" />
      <remove statusCode="412" />
      <error statusCode="412" path="G:\PleskVhosts\capitol-academy.com\error_docs\precondition_failed.html" />
      <remove statusCode="414" />
      <error statusCode="414" path="G:\PleskVhosts\capitol-academy.com\error_docs\request-uri_too_long.html" />
      <remove statusCode="415" />
      <error statusCode="415" path="G:\PleskVhosts\capitol-academy.com\error_docs\unsupported_media_type.html" />
      <remove statusCode="500" />
      <error statusCode="500" path="G:\PleskVhosts\capitol-academy.com\error_docs\internal_server_error.html" />
      <remove statusCode="501" />
      <error statusCode="501" path="G:\PleskVhosts\capitol-academy.com\error_docs\not_implemented.html" />
      <remove statusCode="502" />
      <error statusCode="502" path="G:\PleskVhosts\capitol-academy.com\error_docs\bad_gateway.html" />
      <remove statusCode="503" />
      <error statusCode="503" path="G:\PleskVhosts\capitol-academy.com\error_docs\maintenance.html" />
    </httpErrors>
    <tracing>
      <traceFailedRequests>
        <clear />
      </traceFailedRequests>
    </tracing>
  </system.webServer>
  <system.web>
    <compilation tempDirectory="G:\PleskVhosts\capitol-academy.com\tmp" />
  </system.web>
</configuration>