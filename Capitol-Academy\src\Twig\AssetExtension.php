<?php

namespace App\Twig;

use App\Service\AssetService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

/**
 * Twig extension for asset management functions
 */
class AssetExtension extends AbstractExtension
{
    private AssetService $assetService;

    public function __construct(AssetService $assetService)
    {
        $this->assetService = $assetService;
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('asset_url', [$this->assetService, 'getAssetUrl']),
            new TwigFunction('profile_image_url', [$this->assetService, 'getProfileImageUrl']),
            new TwigFunction('course_image_url', [$this->assetService, 'getCourseImageUrl']),
            new TwigFunction('admin_image_url', [$this->assetService, 'getAdminImageUrl']),
            new TwigFunction('logo_url', [$this->assetService, 'getLogoUrl']),
            new TwigFunction('asset_exists', [$this->assetService, 'assetExists']),
            new TwigFunction('file_size', [$this->assetService, 'getFileSize']),
            new TwigFunction('responsive_srcset', [$this->assetService, 'getResponsiveImageSrcset']),
        ];
    }
}
