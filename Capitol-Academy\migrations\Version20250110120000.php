<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250110120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add thumbnail_image and featured_image fields to market_analysis table and remove external_link field';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE market_analysis ADD thumbnail_image VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE market_analysis ADD featured_image VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE market_analysis DROP external_link');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE market_analysis ADD external_link VARCHAR(500) DEFAULT NULL');
        $this->addSql('ALTER TABLE market_analysis DROP thumbnail_image');
        $this->addSql('ALTER TABLE market_analysis DROP featured_image');
    }
}
