# Capitol Academy Homepage Background Image Recommendations

This document provides optimal background image dimension recommendations for each section of the Capitol Academy homepage to ensure proper display across all devices and screen sizes.

## General Guidelines

- **Format**: PNG or JPG (PNG preferred for better quality)
- **Color Space**: sRGB
- **Compression**: Optimize for web (balance between quality and file size)
- **File Size**: Keep individual images under 2MB for optimal loading performance

## Section-Specific Recommendations

### 1. Hero Section
- **Current Image**: `Banner 1 HP.png`
- **Recommended Dimensions**: 1920px × 1080px (16:9 aspect ratio)
- **Minimum Dimensions**: 1600px × 900px
- **Notes**: 
  - Should work well at 80vh height
  - Ensure important content is centered for mobile cropping
  - Consider focal point placement for text overlay readability

### 2. Partner Logos Section
- **Background**: White (no background image needed)
- **Logo Dimensions**: 120px × 60px (2:1 aspect ratio recommended)
- **Notes**: 
  - Individual partner logos should be optimized for 35px height display
  - Maintain transparent backgrounds for logos
  - Consider SVG format for crisp scaling

### 3. Courses Section
- **Current Image**: `fond-sitemap.png`
- **Recommended Dimensions**: 1920px × 1200px
- **Minimum Dimensions**: 1600px × 1000px
- **Notes**: 
  - Should accommodate variable content height
  - Ensure good contrast for text readability
  - Pattern/texture works well for this section

### 4. Free Trading Stream Section
- **Current Image**: `Background 3 Free Trading Stream.png`
- **Recommended Dimensions**: 1920px × 800px
- **Minimum Dimensions**: 1600px × 600px
- **Notes**: 
  - Dark overlay (60% opacity) is applied
  - Should work well with white text overlay
  - Consider video content placement on right side

### 5. Past Trading Stream Section
- **Current Image**: `Background 3 Free Trading Stream.png` (same as above)
- **Recommended Dimensions**: 1920px × 600px
- **Minimum Dimensions**: 1600px × 400px
- **Notes**: 
  - Darker overlay (70% opacity) is applied
  - Minimum height of 300px is enforced
  - Should accommodate 4-column video grid

### 6. Analysis Section
- **Current Image**: `Background Get your Analysis HP.png`
- **Recommended Dimensions**: 1920px × 800px
- **Minimum Dimensions**: 1600px × 600px
- **Notes**: 
  - Light background recommended for dark text
  - Should provide good contrast for form elements
  - Two-column layout (text left, form right)

### 7. Trading Made Easy Section
- **Current Image**: `Background Trading Made Easy HP.png`
- **Recommended Dimensions**: 1920px × 800px
- **Minimum Dimensions**: 1600px × 600px
- **Notes**: 
  - Dark overlay (50% opacity) is applied
  - Should work with glassmorphism container on right side
  - Consider leaving left side less busy for container placement

## Mobile Responsiveness Considerations

### Breakpoints to Consider:
- **Large Desktop**: 1920px and above
- **Desktop**: 1200px - 1919px
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

### Mobile-Specific Recommendations:
- Ensure critical visual elements are centered
- Test background positioning at `background-position: center`
- Consider providing mobile-specific images for better optimization
- Ensure text remains readable on all background areas

## Performance Optimization

### Image Optimization Tips:
1. **Compression**: Use tools like TinyPNG or ImageOptim
2. **Format Selection**: 
   - Use WebP format with fallback to PNG/JPG
   - Consider AVIF for modern browsers
3. **Lazy Loading**: Implement for below-the-fold sections
4. **Responsive Images**: Use `srcset` for different screen densities

### File Size Targets:
- **Hero Section**: < 500KB (most important for loading speed)
- **Other Sections**: < 300KB each
- **Total Page Images**: < 2MB combined

## Implementation Notes

### CSS Background Properties Used:
```css
background-size: cover;
background-position: center;
background-attachment: fixed; /* Creates parallax effect */
background-repeat: no-repeat;
```

### Accessibility Considerations:
- Ensure sufficient contrast ratios (4.5:1 minimum)
- Provide alternative text descriptions
- Consider users with motion sensitivity (parallax effects)
- Test with screen readers

## Testing Checklist

- [ ] Test on various screen sizes (320px to 1920px+)
- [ ] Verify text readability on all backgrounds
- [ ] Check loading performance on slow connections
- [ ] Validate color contrast ratios
- [ ] Test with different browser zoom levels
- [ ] Verify parallax effects work smoothly
- [ ] Check mobile landscape orientation

## Future Considerations

- Consider implementing art direction for different screen sizes
- Evaluate CDN usage for faster image delivery
- Monitor Core Web Vitals impact
- Consider progressive image loading techniques
- Plan for seasonal/promotional background updates

---

*Last Updated: [Current Date]*
*Version: 1.0*
