<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Update plan table structure - remove category, level, thumbnail_image, banner_image columns
 */
final class Version20250105000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove category, level, thumbnail_image, banner_image columns from plan table';
    }

    public function up(Schema $schema): void
    {
        // Remove the unwanted columns from plan table
        $this->addSql('ALTER TABLE plan DROP COLUMN IF EXISTS category');
        $this->addSql('ALTER TABLE plan DROP COLUMN IF EXISTS level');
        $this->addSql('ALTER TABLE plan DROP COLUMN IF EXISTS thumbnail_image');
        $this->addSql('ALTER TABLE plan DROP COLUMN IF EXISTS banner_image');
    }

    public function down(Schema $schema): void
    {
        // Add back the columns for rollback
        $this->addSql('ALTER TABLE plan ADD COLUMN category VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE plan ADD COLUMN level VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE plan ADD COLUMN thumbnail_image VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE plan ADD COLUMN banner_image VARCHAR(255) DEFAULT NULL');
    }
}
