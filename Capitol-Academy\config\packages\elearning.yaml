parameters:
    # Application Settings
    app.base_url: '%env(APP_BASE_URL)%'
    app.email_from: '%env(APP_EMAIL_FROM)%'
    
    # PayPal Configuration
    paypal.client_id: '%env(PAYPAL_CLIENT_ID)%'
    paypal.client_secret: '%env(PAYPAL_CLIENT_SECRET)%'
    paypal.sandbox: '%env(bool:PAYPAL_SANDBOX)%'
    
    # VdoCipher Configuration
    vdocipher.api_secret: '%env(VDOCIPHER_API_SECRET)%'

    # Google OAuth Configuration
    google.oauth.enabled: '%env(bool:GOOGLE_OAUTH_ENABLED)%'

services:
    # E-Learning Services
    App\Service\VdoCipherService:
        arguments:
            $httpClient: '@http_client'
            $logger: '@logger'
            $params: '@parameter_bag'
        tags:
            - { name: monolog.logger, channel: vdocipher }

    App\Service\PayPalService:
        arguments:
            $httpClient: '@http_client'
            $logger: '@logger'
            $params: '@parameter_bag'
        tags:
            - { name: monolog.logger, channel: paypal }

    App\Service\AccessControlService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $videoAccessRepository: '@App\Repository\UserVideoAccessRepository'
            $orderRepository: '@App\Repository\OrderRepository'
            $vdoCipherService: '@App\Service\VdoCipherService'
            $logger: '@logger'
        tags:
            - { name: monolog.logger, channel: access_control }

    App\Service\CartService:
        arguments:
            $requestStack: '@request_stack'
            $entityManager: '@doctrine.orm.entity_manager'
            $logger: '@logger'
        tags:
            - { name: monolog.logger, channel: cart }
