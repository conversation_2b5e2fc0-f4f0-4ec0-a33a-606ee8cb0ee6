{% extends 'base.html.twig' %}

{% block title %}Search Results for "{{ query }}" | Capitol Academy{% endblock %}

{% block meta_description %}Search results for "{{ query }}" on Capitol Academy - Find trading courses, market analysis, and educational content.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .search-results-page {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .search-header {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
        border: 1px solid #e9ecef;
    }

    .search-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .search-subtitle {
        color: #6c757d;
        font-size: 1.1rem;
    }

    .search-result-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(1, 26, 45, 0.05);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .search-result-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(1, 26, 45, 0.12);
        border-color: #011a2d;
    }

    .result-category {
        background: #011a2d;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .result-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        text-decoration: none;
        display: block;
    }

    .result-title:hover {
        color: #a90418;
        text-decoration: none;
    }

    .result-excerpt {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .result-link {
        color: #011a2d;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: color 0.3s ease;
    }

    .result-link:hover {
        color: #a90418;
        text-decoration: none;
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(1, 26, 45, 0.08);
    }

    .no-results-icon {
        font-size: 4rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .no-results-title {
        color: #011a2d;
        font-weight: 700;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .no-results-text {
        color: #6c757d;
        font-size: 1.1rem;
    }
</style>
{% endblock %}

{% block body %}
<div class="search-results-page">
    <div class="container">
        <!-- Search Header -->
        <div class="search-header">
            <h1 class="search-title">Search Results</h1>
            <p class="search-subtitle">
                {% if query %}
                    Found {{ total }} result{{ total != 1 ? 's' : '' }} for "<strong>{{ query }}</strong>"
                {% else %}
                    Please enter a search term
                {% endif %}
            </p>
        </div>

        <!-- Search Results -->
        {% if results|length > 0 %}
            <div class="search-results">
                {% for result in results %}
                    <div class="search-result-item">
                        <span class="result-category">{{ result.category }}</span>
                        <a href="{{ result.url }}" class="result-title">{{ result.title }}</a>
                        {% if result.excerpt %}
                            <p class="result-excerpt">{{ result.excerpt }}</p>
                        {% endif %}
                        <a href="{{ result.url }}" class="result-link">
                            {% if result.type == 'video' %}
                                <i class="fas fa-video"></i>
                                Watch Video
                            {% else %}
                                <i class="fas fa-arrow-right"></i>
                                Read More
                            {% endif %}
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h2 class="no-results-title">No Results Found</h2>
                <p class="no-results-text">
                    {% if query %}
                        We couldn't find any content matching "{{ query }}". Try using different keywords or browse our categories.
                    {% else %}
                        Enter a search term to find trading courses, market analysis, and educational content.
                    {% endif %}
                </p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
