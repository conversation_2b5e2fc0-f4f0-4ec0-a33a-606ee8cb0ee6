<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add sourcePage field to contact table
 */
final class Version20250620000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add sourcePage field to contact table for tracking form submission sources';
    }

    public function up(Schema $schema): void
    {
        // Add sourcePage column to contact table
        $this->addSql('ALTER TABLE contact ADD sourcePage VARCHAR(100) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove sourcePage column from contact table
        $this->addSql('ALTER TABLE contact DROP sourcePage');
    }
}
