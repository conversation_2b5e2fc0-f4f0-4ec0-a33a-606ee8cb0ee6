<?php

namespace App\Service;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Service for comprehensive error handling and logging
 */
class ErrorHandlingService
{
    private LoggerInterface $logger;
    private IpAddressService $ipAddressService;

    public function __construct(LoggerInterface $logger, IpAddressService $ipAddressService)
    {
        $this->logger = $logger;
        $this->ipAddressService = $ipAddressService;
    }

    /**
     * Log error with context information
     */
    public function logError(\Throwable $error, ?Request $request = null, array $context = []): void
    {
        $errorContext = [
            'message' => $error->getMessage(),
            'file' => $error->getFile(),
            'line' => $error->getLine(),
            'trace' => $error->getTraceAsString(),
            'timestamp' => new \DateTimeImmutable(),
        ];

        if ($request) {
            $errorContext['request'] = [
                'method' => $request->getMethod(),
                'uri' => $request->getUri(),
                'ip' => $this->ipAddressService->getClientIpAddress($request),
                'user_agent' => $request->headers->get('User-Agent'),
                'referer' => $request->headers->get('Referer'),
            ];
        }

        $errorContext = array_merge($errorContext, $context);

        $this->logger->error('Application Error: ' . $error->getMessage(), $errorContext);
    }

    /**
     * Log security incident
     */
    public function logSecurityIncident(string $incident, ?Request $request = null, array $context = []): void
    {
        $securityContext = [
            'incident' => $incident,
            'timestamp' => new \DateTimeImmutable(),
            'severity' => 'HIGH',
        ];

        if ($request) {
            $securityContext['request'] = [
                'method' => $request->getMethod(),
                'uri' => $request->getUri(),
                'ip' => $this->ipAddressService->getClientIpAddress($request),
                'user_agent' => $request->headers->get('User-Agent'),
                'referer' => $request->headers->get('Referer'),
                'headers' => $request->headers->all(),
            ];
        }

        $securityContext = array_merge($securityContext, $context);

        $this->logger->critical('Security Incident: ' . $incident, $securityContext);
    }

    /**
     * Handle validation errors and format them for display
     */
    public function formatValidationErrors(array $errors): array
    {
        $formattedErrors = [];

        foreach ($errors as $field => $error) {
            if (is_array($error)) {
                $formattedErrors[$field] = implode(', ', $error);
            } else {
                $formattedErrors[$field] = $error;
            }
        }

        return $formattedErrors;
    }

    /**
     * Create user-friendly error response
     */
    public function createErrorResponse(\Throwable $error, Request $request): Response
    {
        $isAjax = $request->isXmlHttpRequest();
        $isDev = $_ENV['APP_ENV'] === 'dev';

        if ($isAjax) {
            return $this->createJsonErrorResponse($error, $isDev);
        }

        return $this->createHtmlErrorResponse($error, $isDev);
    }

    /**
     * Create JSON error response for AJAX requests
     */
    private function createJsonErrorResponse(\Throwable $error, bool $isDev): JsonResponse
    {
        $data = [
            'success' => false,
            'message' => $this->getUserFriendlyMessage($error),
            'timestamp' => (new \DateTimeImmutable())->format('c'),
        ];

        if ($isDev) {
            $data['debug'] = [
                'message' => $error->getMessage(),
                'file' => $error->getFile(),
                'line' => $error->getLine(),
                'trace' => $error->getTraceAsString(),
            ];
        }

        $statusCode = $this->getHttpStatusCode($error);

        return new JsonResponse($data, $statusCode);
    }

    /**
     * Create HTML error response
     */
    private function createHtmlErrorResponse(\Throwable $error, bool $isDev): Response
    {
        $statusCode = $this->getHttpStatusCode($error);
        $message = $this->getUserFriendlyMessage($error);

        $html = $this->generateErrorHtml($statusCode, $message, $isDev ? $error : null);

        return new Response($html, $statusCode);
    }

    /**
     * Get user-friendly error message
     */
    private function getUserFriendlyMessage(\Throwable $error): string
    {
        $errorClass = get_class($error);

        $friendlyMessages = [
            'Symfony\Component\HttpKernel\Exception\NotFoundHttpException' => 'The requested page could not be found.',
            'Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException' => 'You do not have permission to access this resource.',
            'Symfony\Component\Security\Core\Exception\AuthenticationException' => 'Authentication failed. Please check your credentials.',
            'Doctrine\DBAL\Exception\ConnectionException' => 'Database connection error. Please try again later.',
            'Symfony\Component\HttpFoundation\File\Exception\FileException' => 'File upload error. Please try again.',
        ];

        return $friendlyMessages[$errorClass] ?? 'An unexpected error occurred. Please try again later.';
    }

    /**
     * Get appropriate HTTP status code for error
     */
    private function getHttpStatusCode(\Throwable $error): int
    {
        if (method_exists($error, 'getStatusCode')) {
            return $error->getStatusCode();
        }

        $errorClass = get_class($error);

        $statusCodes = [
            'Symfony\Component\HttpKernel\Exception\NotFoundHttpException' => 404,
            'Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException' => 403,
            'Symfony\Component\Security\Core\Exception\AuthenticationException' => 401,
            'Symfony\Component\HttpFoundation\File\Exception\FileException' => 400,
        ];

        return $statusCodes[$errorClass] ?? 500;
    }

    /**
     * Generate error HTML page
     */
    private function generateErrorHtml(int $statusCode, string $message, ?\Throwable $error = null): string
    {
        $title = $this->getStatusTitle($statusCode);

        $debugInfo = '';
        if ($error) {
            $debugInfo = sprintf(
                '<div class="debug-info mt-4"><h4>Debug Information</h4><p><strong>Error:</strong> %s</p><p><strong>File:</strong> %s:%d</p><pre>%s</pre></div>',
                htmlspecialchars($error->getMessage()),
                htmlspecialchars($error->getFile()),
                $error->getLine(),
                htmlspecialchars($error->getTraceAsString())
            );
        }

        return sprintf('
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>%s - Capitol Academy</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #1e3c72 0%%, #2a5298 100%%); color: white; min-height: 100vh; display: flex; align-items: center; }
        .error-container { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 3rem; }
        .debug-info { background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 8px; color: #fff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="error-container text-center">
                    <h1 class="display-1 fw-bold">%d</h1>
                    <h2 class="mb-4">%s</h2>
                    <p class="lead mb-4">%s</p>
                    <a href="/" class="btn btn-light btn-lg">Return to Homepage</a>
                    %s
                </div>
            </div>
        </div>
    </div>
</body>
</html>',
            $title,
            $statusCode,
            $title,
            htmlspecialchars($message),
            $debugInfo
        );
    }

    /**
     * Get status title for HTTP code
     */
    private function getStatusTitle(int $statusCode): string
    {
        $titles = [
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Page Not Found',
            405 => 'Method Not Allowed',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable',
        ];

        return $titles[$statusCode] ?? 'Error';
    }

    /**
     * Log user action for audit trail
     */
    public function logUserAction(string $action, ?Request $request = null, array $context = []): void
    {
        $actionContext = [
            'action' => $action,
            'timestamp' => new \DateTimeImmutable(),
        ];

        if ($request) {
            $actionContext['request'] = [
                'method' => $request->getMethod(),
                'uri' => $request->getUri(),
                'ip' => $this->ipAddressService->getClientIpAddress($request),
            ];
        }

        $actionContext = array_merge($actionContext, $context);

        $this->logger->info('User Action: ' . $action, $actionContext);
    }
}
