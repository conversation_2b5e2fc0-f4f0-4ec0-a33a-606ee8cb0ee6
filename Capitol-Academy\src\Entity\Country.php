<?php

namespace App\Entity;

use App\Repository\CountryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CountryRepository::class)]
#[ORM\Table(name: 'countries')]
class Country
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 100, unique: true)]
    private ?string $countryName = null;

    #[ORM\Column(length: 10)]
    private ?string $phonePrefix = null;

    #[ORM\Column]
    private ?int $phoneNumberLength = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCountryName(): ?string
    {
        return $this->countryName;
    }

    public function setCountryName(string $countryName): static
    {
        $this->countryName = $countryName;

        return $this;
    }

    public function getPhonePrefix(): ?string
    {
        return $this->phonePrefix;
    }

    public function setPhonePrefix(string $phonePrefix): static
    {
        $this->phonePrefix = $phonePrefix;

        return $this;
    }

    public function getPhoneNumberLength(): ?int
    {
        return $this->phoneNumberLength;
    }

    public function setPhoneNumberLength(int $phoneNumberLength): static
    {
        $this->phoneNumberLength = $phoneNumberLength;

        return $this;
    }

    public function __toString(): string
    {
        return $this->countryName ?? '';
    }

    /**
     * Get formatted display name with phone prefix
     */
    public function getDisplayName(): string
    {
        return sprintf('%s (%s)', $this->countryName, $this->phonePrefix);
    }

    /**
     * Get phone number validation pattern based on length
     */
    public function getPhoneValidationPattern(): string
    {
        return sprintf('/^\d{%d}$/', $this->phoneNumberLength);
    }

    /**
     * Validate phone number for this country
     */
    public function isValidPhoneNumber(string $phoneNumber): bool
    {
        // Remove any non-digit characters
        $cleanPhone = preg_replace('/\D/', '', $phoneNumber);
        
        // Check if length matches expected length
        return strlen($cleanPhone) === $this->phoneNumberLength;
    }

    /**
     * Format phone number with country prefix
     */
    public function formatPhoneNumber(string $phoneNumber): string
    {
        $cleanPhone = preg_replace('/\D/', '', $phoneNumber);
        return $this->phonePrefix . ' ' . $cleanPhone;
    }
}
