<?php

namespace App\Controller\Admin;

use App\Entity\Order;
use App\Repository\OrderRepository;
use App\Service\AccessControlService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/orders')]
#[IsGranted('ROLE_ADMIN')]
class OrderController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private AccessControlService $accessControlService;

    public function __construct(
        EntityManagerInterface $entityManager,
        AccessControlService $accessControlService
    ) {
        $this->entityManager = $entityManager;
        $this->accessControlService = $accessControlService;
    }

    #[Route('', name: 'admin_order_index', methods: ['GET'])]
    public function index(OrderRepository $orderRepository, Request $request): Response
    {
        $search = $request->query->get('search');
        $status = $request->query->get('status');
        $gateway = $request->query->get('gateway');
        $dateFrom = $request->query->get('date_from');
        $dateTo = $request->query->get('date_to');

        $queryBuilder = $orderRepository->createQueryBuilder('o')
            ->leftJoin('o.user', 'u')
            ->orderBy('o.createdAt', 'DESC');

        if ($search) {
            $queryBuilder->andWhere('o.orderNumber LIKE :search OR u.email LIKE :search OR u.firstName LIKE :search OR u.lastName LIKE :search')
                ->setParameter('search', '%' . $search . '%');
        }

        if ($status) {
            $queryBuilder->andWhere('o.paymentStatus = :status')
                ->setParameter('status', $status);
        }

        if ($gateway) {
            $queryBuilder->andWhere('o.paymentGateway = :gateway')
                ->setParameter('gateway', $gateway);
        }

        if ($dateFrom) {
            $queryBuilder->andWhere('o.createdAt >= :dateFrom')
                ->setParameter('dateFrom', new \DateTime($dateFrom));
        }

        if ($dateTo) {
            $queryBuilder->andWhere('o.createdAt <= :dateTo')
                ->setParameter('dateTo', new \DateTime($dateTo . ' 23:59:59'));
        }

        $orders = $queryBuilder->getQuery()->getResult();

        // Calculate statistics
        $stats = $this->calculateOrderStats($orders);

        return $this->render('admin/order/index.html.twig', [
            'orders' => $orders,
            'stats' => $stats,
            'current_search' => $search,
            'current_status' => $status,
            'current_gateway' => $gateway,
            'current_date_from' => $dateFrom,
            'current_date_to' => $dateTo,
        ]);
    }

    #[Route('/{id}', name: 'admin_order_show', methods: ['GET'])]
    public function show(Order $order): Response
    {
        return $this->render('admin/order/show.html.twig', [
            'order' => $order,
        ]);
    }

    #[Route('/{id}/refund', name: 'admin_order_refund', methods: ['POST'])]
    public function refund(Request $request, Order $order): JsonResponse
    {
        if (!$this->isCsrfTokenValid('refund'.$order->getId(), $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 400);
        }

        if (!$order->isCompleted()) {
            return new JsonResponse(['success' => false, 'message' => 'Only completed orders can be refunded'], 400);
        }

        try {
            // Update order status
            $order->setPaymentStatus(Order::STATUS_REFUNDED);

            // Add refund note
            $refundReason = $request->request->get('reason', 'Admin refund');
            $refundNote = sprintf(
                'Refunded by admin on %s. Reason: %s',
                (new \DateTime())->format('Y-m-d H:i:s'),
                $refundReason
            );
            $order->setNotes($refundNote);

            $this->entityManager->flush();

            // TODO: Process actual refund through payment gateway
            // For now, we just mark it as refunded in our system

            $this->addFlash('success', 'Order marked as refunded successfully.');

            return new JsonResponse([
                'success' => true,
                'message' => 'Order refunded successfully',
                'order_status' => $order->getStatusLabel()
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to process refund: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/{id}/resend-access', name: 'admin_order_resend_access', methods: ['POST'])]
    public function resendAccess(Request $request, Order $order): JsonResponse
    {
        if (!$this->isCsrfTokenValid('resend'.$order->getId(), $request->request->get('_token'))) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 400);
        }

        if (!$order->isCompleted()) {
            return new JsonResponse(['success' => false, 'message' => 'Only completed orders can have access resent'], 400);
        }

        try {
            // Re-process order completion (without enrollment since system is simplified)
            $this->accessControlService->processOrderCompletion($order);

            $this->addFlash('success', 'Access permissions have been re-granted successfully.');

            return new JsonResponse([
                'success' => true,
                'message' => 'Access permissions re-granted successfully'
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to resend access: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/export', name: 'admin_order_export', methods: ['GET'])]
    public function export(OrderRepository $orderRepository): Response
    {
        $orders = $orderRepository->findBy([], ['createdAt' => 'DESC']);

        $csvData = [];
        $csvData[] = [
            'Order Number',
            'Customer Email',
            'Customer Name',
            'Total Price',
            'Payment Status',
            'Payment Gateway',
            'Transaction ID',
            'Items',
            'Created At',
            'Completed At'
        ];

        foreach ($orders as $order) {
            $items = [];
            foreach ($order->getItems() as $item) {
                $items[] = $item['type'] . ': ' . $item['title'];
            }

            $csvData[] = [
                $order->getOrderNumber(),
                $order->getUser()->getEmail(),
                $order->getUser()->getFirstName() . ' ' . $order->getUser()->getLastName(),
                '$' . number_format($order->getTotalPrice(), 2),
                $order->getStatusLabel(),
                $order->getPaymentGateway() ?: 'N/A',
                $order->getPaypalTransactionId() ?: 'N/A',
                implode('; ', $items),
                $order->getCreatedAt()->format('Y-m-d H:i:s'),
                $order->getCompletedAt() ? $order->getCompletedAt()->format('Y-m-d H:i:s') : 'N/A'
            ];
        }

        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="orders_export_' . date('Y-m-d') . '.csv"');

        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $response->setContent(stream_get_contents($output));
        fclose($output);

        return $response;
    }

    private function calculateOrderStats(array $orders): array
    {
        $stats = [
            'total_orders' => count($orders),
            'completed_orders' => 0,
            'pending_orders' => 0,
            'failed_orders' => 0,
            'refunded_orders' => 0,
            'total_revenue' => 0,
            'completed_revenue' => 0,
            'average_order_value' => 0,
            'payment_gateways' => [],
            'top_items' => []
        ];

        $itemCounts = [];

        foreach ($orders as $order) {
            switch ($order->getPaymentStatus()) {
                case Order::STATUS_COMPLETED:
                    $stats['completed_orders']++;
                    $stats['completed_revenue'] += $order->getTotalPrice();
                    break;
                case Order::STATUS_PENDING:
                    $stats['pending_orders']++;
                    break;
                case Order::STATUS_FAILED:
                    $stats['failed_orders']++;
                    break;
                case Order::STATUS_REFUNDED:
                    $stats['refunded_orders']++;
                    break;
            }

            $stats['total_revenue'] += $order->getTotalPrice();

            // Payment gateway stats
            $gateway = $order->getPaymentGateway() ?: 'Unknown';
            if (!isset($stats['payment_gateways'][$gateway])) {
                $stats['payment_gateways'][$gateway] = 0;
            }
            $stats['payment_gateways'][$gateway]++;

            // Item popularity
            foreach ($order->getItems() as $item) {
                $key = $item['type'] . ':' . $item['title'];
                if (!isset($itemCounts[$key])) {
                    $itemCounts[$key] = 0;
                }
                $itemCounts[$key] += $item['quantity'] ?? 1;
            }
        }

        if ($stats['total_orders'] > 0) {
            $stats['average_order_value'] = $stats['total_revenue'] / $stats['total_orders'];
        }

        // Top 5 items
        arsort($itemCounts);
        $stats['top_items'] = array_slice($itemCounts, 0, 5, true);

        return $stats;
    }
}
