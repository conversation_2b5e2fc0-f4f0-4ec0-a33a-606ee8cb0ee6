{% extends 'base.html.twig' %}

{% block title %}Order Confirmation - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .success-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
        }

        .success-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }

        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
        }

        .success-content {
            padding: 40px;
        }

        .order-details {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #011a2d;
            font-weight: 500;
        }

        .order-items {
            margin-bottom: 30px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            background: white;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #28a745;
        }

        .access-info {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .access-info i {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-primary:hover {
            background: #0d2a42;
            border-color: #0d2a42;
        }

        .btn-outline-primary {
            color: #011a2d;
            border-color: #011a2d;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-outline-primary:hover {
            background: #011a2d;
            border-color: #011a2d;
        }

        .next-steps {
            background: #e8f4fd;
            border: 1px solid #b8daff;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .next-steps h6 {
            color: #004085;
            margin-bottom: 15px;
        }

        .next-steps ul {
            margin-bottom: 0;
            color: #004085;
        }

        .payment-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .payment-info h6 {
            color: #856404;
            margin-bottom: 10px;
        }

        .payment-info p {
            color: #856404;
            margin-bottom: 0;
        }

        @media (max-width: 768px) {
            .success-section {
                padding: 40px 0;
            }
            
            .success-header {
                padding: 30px 20px;
            }
            
            .success-content {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class="success-section">
        <div class="container">
            <div class="success-container">
                <div class="success-header">
                    <div class="success-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <h1 class="mb-3">Payment Successful!</h1>
                    <p class="lead mb-0">Thank you for your purchase. Your order has been confirmed.</p>
                </div>

                <div class="success-content">
                    <!-- Order Details -->
                    <div class="order-details">
                        <h5 class="mb-3">
                            <i class="fas fa-receipt me-2"></i>Order Details
                        </h5>
                        
                        <div class="detail-row">
                            <span class="detail-label">Order Number:</span>
                            <span class="detail-value">#{{ order.orderNumber }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Order Date:</span>
                            <span class="detail-value">{{ order.completedAt|date('M d, Y \\a\\t g:i A') }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Payment Method:</span>
                            <span class="detail-value">{{ order.paymentGateway|title }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Transaction ID:</span>
                            <span class="detail-value">{{ order.paypalTransactionId ?: 'Processing...' }}</span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Total Amount:</span>
                            <span class="detail-value">${{ order.totalPrice|number_format(2) }}</span>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="order-items">
                        <h5 class="mb-3">
                            <i class="fas fa-shopping-bag me-2"></i>Items Purchased
                        </h5>
                        
                        {% for item in order.items %}
                            <div class="order-item">
                                <div class="item-info">
                                    <h6>{{ item.title }}</h6>
                                    <span class="item-type">{{ item.type|replace({'_': ' '})|title }}</span>
                                    {% if item.quantity > 1 %}
                                        <small class="text-muted d-block">Quantity: {{ item.quantity }}</small>
                                    {% endif %}
                                </div>
                                <div class="item-price">
                                    ${{ item.subtotal|number_format(2) }}
                                </div>
                            </div>
                        {% endfor %}
                    </div>

                    <!-- Access Information -->
                    <div class="access-info">
                        <i class="fas fa-unlock-alt"></i>
                        <h5 class="mb-3">Your Content is Now Available!</h5>
                        <p class="mb-0">
                            You now have access to all purchased videos and courses. 
                            Start trading immediately by visiting your dashboard or browsing your content.
                        </p>
                    </div>

                    <!-- Next Steps -->
                    <div class="next-steps">
                        <h6>
                            <i class="fas fa-list-check me-2"></i>What's Next?
                        </h6>
                        <ul>
                            <li>Access your purchased videos from the <strong>Premium Videos</strong> section</li>
                            <li>Check your email for order confirmation and access details</li>
                            <li>Visit your user dashboard to track your trading progress</li>
                            <li>Contact our support team if you need assistance with your purchase</li>
                        </ul>
                    </div>

                    <!-- Payment Information -->
                    <div class="payment-info">
                        <h6>
                            <i class="fas fa-info-circle me-2"></i>Payment Information
                        </h6>
                        <p>
                            A confirmation email has been sent to <strong>{{ order.user.email }}</strong> 
                            with your receipt and access instructions.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="{{ path('app_premium_videos') }}" class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>Start Trading
                        </a>
                        
                        <a href="{{ path('app_user_orders') }}" class="btn btn-outline-primary">
                            <i class="fas fa-history me-2"></i>View Order History
                        </a>
                        
                        <a href="{{ path('app_home') }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Auto-refresh page after 30 seconds to update transaction ID if still processing
        {% if not order.paypalTransactionId %}
            setTimeout(function() {
                location.reload();
            }, 30000);
        {% endif %}
    </script>
{% endblock %}
