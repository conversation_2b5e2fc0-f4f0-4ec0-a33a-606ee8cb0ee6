<br />
<b>Deprecated</b>:  Assigning the return value of new by reference is deprecated in <b>D:\hosting\9422093\html\moodle\calendar\view.php</b> on line <b>318</b><br />
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php" />
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php" />
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Learn Financial Markets: Calendar: Detailed Month View " />
    <title>Learn Financial Markets: Calendar: Detailed Month View</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/calendar/view-view-month-course-1-cal_d-1-cal_m-3-cal_y-2013.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico" />
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-mod.php"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body  class="calendar course-1 notloggedin dir-ltr lang-en_utf8" id="calendar-view">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">Calendar</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a  href="http://www.capitol-academy.com/moodle/login/index.php">Login</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide " >You are here</h2> <ul>
<li class="first"><a  onclick="this.target='_top'" href="http://www.capitol-academy.com/moodle/">Learn Financial Markets</a></li><li> <span class="accesshide " >/&nbsp;</span><span class="arrow sep">&#x25BA;</span> <a  onclick="this.target='_top'" href="http://www.capitol-academy.com/moodle/calendar/view.php?view=upcoming&amp;course=1&amp;cal_d=22&amp;cal_m=3&amp;cal_y=2013">Calendar</a></li><li> <span class="accesshide " >/&nbsp;</span><span class="arrow sep">&#x25BA;</span> March 2013</li></ul></div>
        <div class="navbutton">&nbsp;</div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix"><div id="overDiv" style="position: absolute; visibility: hidden; z-index:1000;"></div><script type="text/javascript" src="http://www.capitol-academy.com/moodle/calendar/overlib.cfg.php"></script><table id="calendar" style="height:100%;"><tr><td class="maincalendar"><div class="heightcontainer"><div class="header"><label for="cal_course_flt_jump">Detailed Month View:</label></div><div class="controls">
<div class="calendar-controls"><a class="previous" href="/" title="February 2013"><span class="arrow ">&#x25C4;</span>&nbsp;February 2013</a><span class="hide"> | </span><span class="current">March 2013</span>
<span class="hide"> | </span><a class="next" href="/" title="April 2013">April 2013&nbsp;<span class="arrow ">&#x25BA;</span></a><span class="clearer"><!-- --></span></div>
</div><table class="calendarmonth"><tr class="weekdays"><th scope="col">Sunday</th><th scope="col">Monday</th><th scope="col">Tuesday</th><th scope="col">Wednesday</th><th scope="col">Thursday</th><th scope="col">Friday</th><th scope="col">Saturday</th></tr><tr><td class="nottoday">&nbsp;</td>
<td class="nottoday">&nbsp;</td>
<td class="nottoday">&nbsp;</td>
<td class="nottoday">&nbsp;</td>
<td class="nottoday">&nbsp;</td>
<td class="nottoday"><div class="day">1</div></td>
<td class="weekend nottoday"><div class="day">2</div></td>
</tr>
<tr><td class="weekend nottoday"><div class="day">3</div></td>
<td class="nottoday"><div class="day">4</div></td>
<td class="nottoday"><div class="day">5</div></td>
<td class="nottoday"><div class="day">6</div></td>
<td class="nottoday"><div class="day">7</div></td>
<td class="nottoday"><div class="day">8</div></td>
<td class="weekend nottoday"><div class="day">9</div></td>
</tr>
<tr><td class="weekend nottoday"><div class="day">10</div></td>
<td class="nottoday"><div class="day">11</div></td>
<td class="nottoday"><div class="day">12</div></td>
<td class="nottoday"><div class="day">13</div></td>
<td class="nottoday"><div class="day">14</div></td>
<td class="nottoday"><div class="day">15</div></td>
<td class="weekend nottoday"><div class="day">16</div></td>
</tr>
<tr><td class="weekend nottoday"><div class="day">17</div></td>
<td class="nottoday"><div class="day">18</div></td>
<td class="nottoday"><div class="day">19</div></td>
<td class="nottoday"><div class="day">20</div></td>
<td class="nottoday"><div class="day">21</div></td>
<td class="today"><div class="day">22</div></td>
<td class="weekend nottoday"><div class="day">23</div></td>
</tr>
<tr><td class="weekend nottoday"><div class="day">24</div></td>
<td class="nottoday"><div class="day">25</div></td>
<td class="nottoday"><div class="day">26</div></td>
<td class="nottoday"><div class="day">27</div></td>
<td class="nottoday"><div class="day">28</div></td>
<td class="nottoday"><div class="day">29</div></td>
<td class="weekend nottoday"><div class="day">30</div></td>
</tr>
<tr><td class="weekend nottoday"><div class="day">31</div></td>
<td class="nottoday">&nbsp;</td><td class="nottoday">&nbsp;</td><td class="nottoday">&nbsp;</td><td class="nottoday">&nbsp;</td><td class="nottoday">&nbsp;</td><td class="nottoday">&nbsp;</td></tr>
</table>
<div class="filters"><table><tr><td class="event_global" style="width: 8px;"></td><td><strong>Global events:</strong> shown (<a href="http://www.capitol-academy.com/moodle/calendar/set.php?var=showglobal&amp;from=month&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013">click to hide</a>)</td>
<td class="event_course" style="width: 8px;"></td><td><strong>Course events:</strong> shown (<a href="http://www.capitol-academy.com/moodle/calendar/set.php?var=showcourses&amp;from=month&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013">click to hide</a>)</td>
</tr>
</table></div><div class="bottom"><div class="singlebutton"><form action="/" method="get"><div><input type="hidden" name="course" value="1" /><input type="submit" value="Export calendar"   /></div></form></div></div></div></td><td class="sidecalendar"><div class="sideblock"><div class="header"><h2>Events Key</h2></div><div class="filters"><table><tr><td class="eventskey event_global" style="width: 11px;"><img src="http://www.capitol-academy.com/moodle/pix/t/hide.gif" class="iconsmall" alt="Hide" title="Global events are shown (click to hide)" style="cursor:pointer" onclick="location.href='http://www.capitol-academy.com/moodle/calendar/set.php?var=showglobal&amp;from=month&amp;id=1&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013'" /></td><td><a href="http://www.capitol-academy.com/moodle/calendar/set.php?var=showglobal&amp;from=month&amp;id=1&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013" title="Global events are shown (click to hide)">Global</a></td>
<td class="eventskey event_course" style="width: 11px;"><img src="http://www.capitol-academy.com/moodle/pix/t/hide.gif" class="iconsmall" alt="Hide" title="Course events are shown (click to hide)" style="cursor:pointer" onclick="location.href='http://www.capitol-academy.com/moodle/calendar/set.php?var=showcourses&amp;from=month&amp;id=1&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013'" /></td><td><a href="http://www.capitol-academy.com/moodle/calendar/set.php?var=showcourses&amp;from=month&amp;id=1&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013" title="Course events are shown (click to hide)">Course</a></td>
</tr>
</table>
</div></div><div class="sideblock"><div class="header"><h2>Monthly View</h2></div><div class="minicalendarblock minicalendartop"><div style="text-align: center;"><a href="http://www.capitol-academy.com/moodle/calendar/view.php?view=month&amp;course=1&amp;cal_d=1&amp;cal_m=2&amp;cal_y=2013">February 2013</a></div>
<table class="minicalendar" summary="Data table, February 2013 Calendar"><tr class="weekdays"><th scope="col"><abbr title="Sunday">Sun</abbr></th>
<th scope="col"><abbr title="Monday">Mon</abbr></th>
<th scope="col"><abbr title="Tuesday">Tue</abbr></th>
<th scope="col"><abbr title="Wednesday">Wed</abbr></th>
<th scope="col"><abbr title="Thursday">Thu</abbr></th>
<th scope="col"><abbr title="Friday">Fri</abbr></th>
<th scope="col"><abbr title="Saturday">Sat</abbr></th>
</tr><tr><td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="day">1</td>
<td class="weekend day">2</td>
</tr><tr><td class="weekend day">3</td>
<td class="day">4</td>
<td class="day">5</td>
<td class="day">6</td>
<td class="day">7</td>
<td class="day">8</td>
<td class="weekend day">9</td>
</tr><tr><td class="weekend day">10</td>
<td class="day">11</td>
<td class="day">12</td>
<td class="day">13</td>
<td class="day">14</td>
<td class="day">15</td>
<td class="weekend day">16</td>
</tr><tr><td class="weekend day">17</td>
<td class="day">18</td>
<td class="day">19</td>
<td class="day">20</td>
<td class="day">21</td>
<td class="day">22</td>
<td class="weekend day">23</td>
</tr><tr><td class="weekend day">24</td>
<td class="day">25</td>
<td class="day">26</td>
<td class="day">27</td>
<td class="day">28</td>
<td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td></tr></table></div><div class="minicalendarblock"><div style="text-align: center;"><a href="http://www.capitol-academy.com/moodle/calendar/view.php?view=month&amp;course=1&amp;cal_d=1&amp;cal_m=3&amp;cal_y=2013">March 2013</a></div>
<table class="minicalendar" summary="Data table, March 2013 Calendar"><tr class="weekdays"><th scope="col"><abbr title="Sunday">Sun</abbr></th>
<th scope="col"><abbr title="Monday">Mon</abbr></th>
<th scope="col"><abbr title="Tuesday">Tue</abbr></th>
<th scope="col"><abbr title="Wednesday">Wed</abbr></th>
<th scope="col"><abbr title="Thursday">Thu</abbr></th>
<th scope="col"><abbr title="Friday">Fri</abbr></th>
<th scope="col"><abbr title="Saturday">Sat</abbr></th>
</tr><tr><td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="day">1</td>
<td class="weekend day">2</td>
</tr><tr><td class="weekend day">3</td>
<td class="day">4</td>
<td class="day">5</td>
<td class="day">6</td>
<td class="day">7</td>
<td class="day">8</td>
<td class="weekend day">9</td>
</tr><tr><td class="weekend day">10</td>
<td class="day">11</td>
<td class="day">12</td>
<td class="day">13</td>
<td class="day">14</td>
<td class="day">15</td>
<td class="weekend day">16</td>
</tr><tr><td class="weekend day">17</td>
<td class="day">18</td>
<td class="day">19</td>
<td class="day">20</td>
<td class="day">21</td>
<td class="day today eventnone"><span class="accesshide " >Today Friday,  22 March </span><a href="#" onmouseover="return overlib('No events', CAPTION, 'Today Friday,  22 March');" onmouseout="return nd();">22</a></td>
<td class="weekend day">23</td>
</tr><tr><td class="weekend day">24</td>
<td class="day">25</td>
<td class="day">26</td>
<td class="day">27</td>
<td class="day">28</td>
<td class="day">29</td>
<td class="weekend day">30</td>
</tr><tr><td class="weekend day">31</td>
<td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td></tr></table></div><div class="minicalendarblock"><div style="text-align: center;"><a href="http://www.capitol-academy.com/moodle/calendar/view.php?view=month&amp;course=1&amp;cal_d=1&amp;cal_m=4&amp;cal_y=2013">April 2013</a></div>
<table class="minicalendar" summary="Data table, April 2013 Calendar"><tr class="weekdays"><th scope="col"><abbr title="Sunday">Sun</abbr></th>
<th scope="col"><abbr title="Monday">Mon</abbr></th>
<th scope="col"><abbr title="Tuesday">Tue</abbr></th>
<th scope="col"><abbr title="Wednesday">Wed</abbr></th>
<th scope="col"><abbr title="Thursday">Thu</abbr></th>
<th scope="col"><abbr title="Friday">Fri</abbr></th>
<th scope="col"><abbr title="Saturday">Sat</abbr></th>
</tr><tr><td class="dayblank">&nbsp;</td>
<td class="day">1</td>
<td class="day">2</td>
<td class="day">3</td>
<td class="day">4</td>
<td class="day">5</td>
<td class="weekend day">6</td>
</tr><tr><td class="weekend day">7</td>
<td class="day">8</td>
<td class="day">9</td>
<td class="day">10</td>
<td class="day">11</td>
<td class="day">12</td>
<td class="weekend day">13</td>
</tr><tr><td class="weekend day">14</td>
<td class="day">15</td>
<td class="day">16</td>
<td class="day">17</td>
<td class="day">18</td>
<td class="day">19</td>
<td class="weekend day">20</td>
</tr><tr><td class="weekend day">21</td>
<td class="day">22</td>
<td class="day">23</td>
<td class="day">24</td>
<td class="day">25</td>
<td class="day">26</td>
<td class="weekend day">27</td>
</tr><tr><td class="weekend day">28</td>
<td class="day">29</td>
<td class="day">30</td>
<td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td></tr></table></div></div></td></tr></table></div><div id="footer"><hr /><p class="helplink"></p><div class="logininfo">You are not logged in. (<a  href="http://www.capitol-academy.com/moodle/login/index.php">Login</a>)</div><div class="homelink"><a  href="http://www.capitol-academy.com/moodle/">Home</a></div></div>
</div>
</body>
</html>