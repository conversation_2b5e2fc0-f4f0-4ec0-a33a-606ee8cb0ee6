{% extends 'base.html.twig' %}

{% block title %}Live Trading Webinars - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section: Live Trading Webinar Contact Form -->
    <section class="py-5 position-relative" style="background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-6">
                    <!-- Left Column: Title and Text -->
                    <div class="text-white pe-lg-5" style="margin-right: 3rem;">
                        <h2 class="h1 fw-bold mb-4" style="color: white; font-family: 'Montserrat', sans-serif;">Join Our Live Trading Webinars</h2>
                        <p class="lead mb-4" style="font-size: 1.2rem; line-height: 1.8; color: white; font-family: '<PERSON><PERSON>ri', Arial, sans-serif;">
                            Experience real-time trading sessions with our expert instructors. Learn advanced trading strategies, market analysis, and risk management techniques through interactive live webinars.
                        </p>
                        <div class="webinar-features mb-4">
                            <div class="feature-item mb-3 d-flex align-items-center">
                                <i class="fas fa-video text-white me-3" style="font-size: 1.2rem;"></i>
                                <span style="color: white; font-family: 'Calibri', Arial, sans-serif;">Interactive live trading sessions</span>
                            </div>
                            <div class="feature-item mb-3 d-flex align-items-center">
                                <i class="fas fa-chart-line text-white me-3" style="font-size: 1.2rem;"></i>
                                <span style="color: white; font-family: 'Calibri', Arial, sans-serif;">Real-time market analysis</span>
                            </div>
                            <div class="feature-item mb-3 d-flex align-items-center">
                                <i class="fas fa-users text-white me-3" style="font-size: 1.2rem;"></i>
                                <span style="color: white; font-family: 'Calibri', Arial, sans-serif;">Q&A sessions with experts</span>
                            </div>
                            <div class="feature-item mb-3 d-flex align-items-center">
                                <i class="fas fa-certificate text-white me-3" style="font-size: 1.2rem;"></i>
                                <span style="color: white; font-family: 'Calibri', Arial, sans-serif;">Participation certificates</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 offset-lg-1">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class="card border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);">
                        <div class="card-body p-4">
                            <h5 class="text-white mb-4 text-center" style="font-family: 'Montserrat', sans-serif;">Register Your Interest</h5>
                            
                            <!-- Flash Messages -->
                            {% for message in app.flashes('success') %}
                                <div class="alert alert-success alert-dismissible fade show mb-3" role="alert" style="background: rgba(40, 167, 69, 0.9); border: none; color: white;">
                                    <i class="fas fa-check-circle me-2"></i>{{ message }}
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}

                            {% for message in app.flashes('error') %}
                                <div class="alert alert-danger alert-dismissible fade show mb-3" role="alert" style="background: rgba(220, 53, 69, 0.9); border: none; color: white;">
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}

                            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                                <div class="mb-3">
                                    {{ form_widget(form.fullName, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'placeholder': 'Full Name *',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;'
                                        }
                                    }) }}
                                    <div class="invalid-feedback">Please provide your full name.</div>
                                </div>

                                <div class="mb-3">
                                    {{ form_widget(form.email, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'placeholder': 'Email Address *',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;'
                                        }
                                    }) }}
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    {{ form_widget(form.message, {
                                        'attr': {
                                            'class': 'form-control glassmorphism-input',
                                            'rows': 4,
                                            'placeholder': 'Tell us about your trading experience and what you hope to learn from our webinars...',
                                            'style': 'border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;'
                                        }
                                    }) }}
                                    <div class="invalid-feedback">Please tell us about your interest in our webinars.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" checked required>
                                        <label class="form-check-label" for="privacyConsent" style="font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);">
                                            I agree to receive information about Capitol Academy's live trading webinars and educational content. I understand I can unsubscribe at any time.
                                        </label>
                                        <div class="invalid-feedback">Please accept our privacy terms.</div>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 8px; padding: 12px 20px; font-weight: 600; transition: all 0.3s ease; font-family: 'Montserrat', sans-serif;">
                                        <i class="fas fa-paper-plane me-2"></i>Register Interest
                                    </button>
                                </div>
                            {{ form_end(form) }}

                            <div class="text-center mt-3">
                                <small style="color: rgba(255, 255, 255, 0.7); font-size: 0.8rem;">
                                    We'll contact you with upcoming webinar schedules and registration details.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
    /* Glassmorphism Input Styling */
    .glassmorphism-input::placeholder {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    .glassmorphism-input:focus {
        background: rgba(255, 255, 255, 0.15) !important;
        border-color: rgba(255, 255, 255, 0.4) !important;
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25) !important;
        color: white !important;
    }

    .glassmorphism-input:valid {
        color: white !important;
    }

    /* Button Hover Effects */
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
    }

    /* Feature Items Animation */
    .feature-item {
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        transform: translateX(10px);
    }

    /* Form Validation Styling */
    .was-validated .form-control:valid {
        border-color: rgba(40, 167, 69, 0.5);
        background-image: none;
    }

    .was-validated .form-control:invalid {
        border-color: rgba(220, 53, 69, 0.5);
        background-image: none;
    }

    .invalid-feedback {
        color: #ff6b6b;
        font-weight: 500;
    }

    /* Alert Auto-dismiss Animation */
    .alert {
        animation: slideInDown 0.5s ease-out;
    }

    @keyframes slideInDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    </style>

    <script>
    // Form validation and enhancement
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');

        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Auto-dismiss alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        });

        // Add loading state to form submission
        const webinarForm = document.querySelector('form');
        if (webinarForm) {
            webinarForm.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn && this.checkValidity()) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Registering...';
                    submitBtn.disabled = true;
                }
            });
        }
    });
    </script>
{% endblock %}
