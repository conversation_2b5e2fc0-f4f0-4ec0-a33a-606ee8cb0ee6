<?php

namespace App\Controller;

use App\Repository\UserRepository;
use App\Service\IpAddressService;
use App\Service\PasswordResetService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class PasswordResetController extends AbstractController
{
    public function __construct(
        private PasswordResetService $passwordResetService,
        private IpAddressService $ipAddressService,
        private CsrfTokenManagerInterface $csrfTokenManager,
        private ValidatorInterface $validator,
        private UserRepository $userRepository
    ) {}

    #[Route('/forgot-password', name: 'app_forgot_password')]
    public function forgotPassword(Request $request): Response
    {
        // Redirect if user is already logged in
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        $error = null;

        if ($request->isMethod('POST')) {
            // Verify CSRF token
            $token = new CsrfToken('forgot_password', $request->request->get('_token'));
            if (!$this->csrfTokenManager->isTokenValid($token)) {
                $error = 'Invalid security token. Please try again.';
            } else {
                $email = $request->request->get('email');

                // Validate email
                $violations = $this->validator->validate($email, [
                    new Assert\NotBlank(message: 'Email is required'),
                    new Assert\Email(message: 'Please enter a valid email address')
                ]);

                if (count($violations) > 0) {
                    $error = $violations[0]->getMessage();
                } else {
                    // Check if user exists first
                    $user = $this->userRepository->findOneBy(['email' => $email]);
                    if (!$user) {
                        $error = 'Email doesn\'t exist';
                    } else {
                        $ipAddress = $this->ipAddressService->getClientIpAddress($request);

                        // Send password reset code
                        $result = $this->passwordResetService->sendPasswordResetCode($email, $ipAddress);

                        if ($result) {
                            // Redirect to merged reset password page with email
                            return $this->redirectToRoute('app_reset_password_merged', ['email' => $email]);
                        } else {
                            // Rate limit exceeded
                            $error = 'Too many password reset attempts. Please try again later.';
                        }
                    }
                }
            }
        }

        return $this->render('security/forgot_password.html.twig', [
            'error' => $error
        ]);
    }

    #[Route('/reset-password', name: 'app_reset_password')]
    public function resetPassword(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        // Redirect if user is already logged in
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        $email = $request->query->get('email', $request->request->get('email', ''));
        $code = $request->query->get('code', $request->request->get('code', ''));
        $error = null;

        // Redirect to forgot password if no email or code provided
        if (empty($email) || empty($code)) {
            return $this->redirectToRoute('app_forgot_password');
        }

        if ($request->isMethod('POST')) {
            // Verify CSRF token
            $token = new CsrfToken('reset_password', $request->request->get('_token'));
            if (!$this->csrfTokenManager->isTokenValid($token)) {
                $error = 'Invalid security token. Please try again.';
            } else {
                $email = $request->request->get('email');
                $code = $request->request->get('code');
                $newPassword = $request->request->get('password');
                $confirmPassword = $request->request->get('confirm_password');

                // Validate inputs
                $violations = $this->validator->validate([
                    'email' => $email,
                    'code' => $code,
                    'password' => $newPassword,
                    'confirm_password' => $confirmPassword
                ], new Assert\Collection([
                    'email' => [
                        new Assert\NotBlank(message: 'Email is required'),
                        new Assert\Email(message: 'Please enter a valid email address')
                    ],
                    'code' => [
                        new Assert\NotBlank(message: 'Verification code is required'),
                        new Assert\Length(exactly: 6, exactMessage: 'Verification code must be 6 digits'),
                        new Assert\Regex(pattern: '/^\d{6}$/', message: 'Verification code must contain only digits')
                    ],
                    'password' => [
                        new Assert\NotBlank(message: 'Password is required'),
                        new Assert\Length(min: 8, minMessage: 'Password must be at least 8 characters long')
                    ],
                    'confirm_password' => [
                        new Assert\NotBlank(message: 'Password confirmation is required')
                    ]
                ]));

                if (count($violations) > 0) {
                    $error = $violations[0]->getMessage();
                } elseif ($newPassword !== $confirmPassword) {
                    $error = 'Passwords do not match.';
                } else {
                    // Verify the code first
                    $user = $this->passwordResetService->verifyResetCode($email, $code);
                    if (!$user) {
                        $error = 'Invalid or expired verification code.';
                    } else {
                        // Hash the new password
                        $hashedPassword = $passwordHasher->hashPassword($user, $newPassword);
                        
                        $ipAddress = $this->ipAddressService->getClientIpAddress($request);
                        
                        // Reset the password
                        if ($this->passwordResetService->resetPassword($email, $code, $hashedPassword, $ipAddress)) {
                            $this->addFlash('success', 'Your password has been reset successfully. You can now log in with your new password.');
                            return $this->redirectToRoute('app_login');
                        } else {
                            $error = 'Failed to reset password. Please try again.';
                        }
                    }
                }
            }
        }

        return $this->render('security/reset_password.html.twig', [
            'error' => $error,
            'email' => $email,
            'code' => $code
        ]);
    }

    #[Route('/verify-reset-code', name: 'app_verify_reset_code')]
    public function verifyResetCode(Request $request): Response
    {
        // Redirect if user is already logged in
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        $email = $request->query->get('email', $request->request->get('email', ''));
        $error = null;

        // Redirect to forgot password if no email provided
        if (empty($email)) {
            return $this->redirectToRoute('app_forgot_password');
        }

        if ($request->isMethod('POST')) {
            // Verify CSRF token
            $token = new CsrfToken('verify_code', $request->request->get('_token'));
            if (!$this->csrfTokenManager->isTokenValid($token)) {
                $error = 'Invalid security token. Please try again.';
            } else {
                $email = $request->request->get('email');
                $code = $request->request->get('code');

                // Validate inputs
                $violations = $this->validator->validate([
                    'email' => $email,
                    'code' => $code
                ], new Assert\Collection([
                    'email' => [
                        new Assert\NotBlank(message: 'Email is required'),
                        new Assert\Email(message: 'Please enter a valid email address')
                    ],
                    'code' => [
                        new Assert\NotBlank(message: 'Verification code is required'),
                        new Assert\Length(exactly: 6, exactMessage: 'Verification code must be 6 digits'),
                        new Assert\Regex(pattern: '/^\d{6}$/', message: 'Verification code must contain only digits')
                    ]
                ]));

                if (count($violations) > 0) {
                    $error = $violations[0]->getMessage();
                } else {
                    // Verify the code
                    $user = $this->passwordResetService->verifyResetCode($email, $code);
                    if ($user) {
                        // Redirect to password reset form with verified email and code
                        return $this->redirectToRoute('app_reset_password', [
                            'email' => $email,
                            'code' => $code
                        ]);
                    } else {
                        $error = 'Invalid or expired verification code.';
                    }
                }
            }
        }

        return $this->render('security/verify_reset_code.html.twig', [
            'error' => $error,
            'email' => $email
        ]);
    }

    #[Route('/reset-password-merged', name: 'app_reset_password_merged')]
    public function resetPasswordMerged(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        // Redirect if user is already logged in
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        $email = $request->query->get('email', $request->request->get('email', ''));
        $error = null;

        // Redirect to forgot password if no email provided
        if (empty($email)) {
            return $this->redirectToRoute('app_forgot_password');
        }

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            $token = new CsrfToken('reset_password_merged', $request->request->get('_token'));
            if (!$this->csrfTokenManager->isTokenValid($token)) {
                $error = 'Invalid security token. Please try again.';
            } else {
                $code = $request->request->get('code', '');
                $newPassword = $request->request->get('new_password', '');
                $confirmPassword = $request->request->get('confirm_password', '');

                // Validate input
                $violations = $this->validator->validate([
                    'code' => $code,
                    'new_password' => $newPassword,
                    'confirm_password' => $confirmPassword
                ], new Assert\Collection([
                    'code' => [
                        new Assert\NotBlank(message: 'Verification code is required'),
                        new Assert\Length(exactly: 6, exactMessage: 'Verification code must be exactly 6 digits'),
                        new Assert\Regex(pattern: '/^\d{6}$/', message: 'Verification code must contain only digits')
                    ],
                    'new_password' => [
                        new Assert\NotBlank(message: 'New password is required'),
                        new Assert\Length(min: 8, minMessage: 'Password must be at least 8 characters long')
                    ],
                    'confirm_password' => [
                        new Assert\NotBlank(message: 'Password confirmation is required')
                    ]
                ]));

                if (count($violations) > 0) {
                    $error = $violations[0]->getMessage();
                } elseif ($newPassword !== $confirmPassword) {
                    $error = 'Passwords do not match.';
                } else {
                    // Verify the code first
                    $user = $this->passwordResetService->verifyResetCode($email, $code);
                    if (!$user) {
                        $error = 'Invalid or expired verification code.';
                    } else {
                        // Hash the new password
                        $hashedPassword = $passwordHasher->hashPassword($user, $newPassword);

                        $ipAddress = $this->ipAddressService->getClientIpAddress($request);

                        // Reset the password
                        if ($this->passwordResetService->resetPassword($email, $code, $hashedPassword, $ipAddress)) {
                            $this->addFlash('success', 'Your password has been reset successfully. You can now log in with your new password.');
                            return $this->redirectToRoute('app_login');
                        } else {
                            $error = 'Failed to reset password. Please try again.';
                        }
                    }
                }
            }
        }

        return $this->render('security/reset_password_merged.html.twig', [
            'error' => $error,
            'email' => $email
        ]);
    }
}
