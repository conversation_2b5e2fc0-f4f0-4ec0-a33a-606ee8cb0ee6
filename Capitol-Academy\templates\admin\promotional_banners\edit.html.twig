{% extends 'admin/base.html.twig' %}

{% block title %}Edit Promotional Banner - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Promotional Banner{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_promotional_banners') }}">Promotional Banners</a></li>
<li class="breadcrumb-item active">Edit: {{ banner.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-bullhorn mr-3" style="font-size: 2rem;"></i>
                        Edit Promotional Banner: {{ banner.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Banners Button -->
                        <a href="{{ path('admin_promotional_banners') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Banners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('promotional_banner_edit_' ~ banner.id) }}">
            <input type="hidden" name="is_active" value="1">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Banner Title -->
                            <div class="form-group">
                                <label for="title" class="form-label">
                                    <i class="fas fa-bullhorn text-primary mr-1"></i>
                                    Banner Title <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control enhanced-field"
                                       id="title"
                                       name="title"
                                       value="{{ banner.title }}"
                                       placeholder="e.g., Cyber Friday Sale - Save up to 25%"
                                       required
                                       maxlength="255"
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                <div class="invalid-feedback">
                                    Please provide a banner title.
                                </div>
                            </div>

                            <!-- Banner Description -->
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left text-primary mr-1"></i>
                                    Banner Description
                                </label>
                                <textarea class="form-control enhanced-field"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="e.g., Join our Analysis Group Today & Save Over $150"
                                          maxlength="500"
                                          style="border: 2px solid #ced4da;">{{ banner.description }}</textarea>
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Brief description that appears alongside the title.
                                </small>
                            </div>

                            <!-- End Date and Background Color Row -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="endDate" class="form-label">
                                            <i class="fas fa-calendar-times text-primary mr-1"></i>
                                            End Date/Time
                                        </label>
                                        <input type="datetime-local"
                                               class="form-control enhanced-field datetime-input"
                                               id="endDate"
                                               name="endDate"
                                               value="{{ banner.endDate ? banner.endDate|date('Y-m-d\\TH:i') : '' }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background: #ffffff;">
                                        <small class="form-text text-muted help-text" style="display: none;">
                                            When the banner should stop being displayed (optional).
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="backgroundColor" class="form-label">
                                            <i class="fas fa-palette text-primary mr-1"></i>
                                            Background Color
                                        </label>
                                        <input type="color"
                                               class="form-control enhanced-field"
                                               id="backgroundColor"
                                               name="backgroundColor"
                                               value="{{ banner.backgroundColor|default('#001427') }}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <small class="form-text text-muted help-text" style="display: none;">
                                            Choose the banner background color.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Banner Preview Section -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-eye text-primary mr-1"></i>
                                    Live Preview
                                </label>
                                <div id="banner-preview" class="promotional-banner-preview border rounded" style="background-color: {{ banner.backgroundColor|default('#001427') }}; color: white; padding: 15px; margin-bottom: 1rem;">
                                    <div class="container-fluid">
                                        <div class="row align-items-center">
                                            <!-- Banner Content -->
                                            <div class="col-md-8">
                                                <div class="promo-text">
                                                    <h6 class="mb-1 text-white fw-bold" id="preview-title">{{ banner.title }}</h6>
                                                    <p class="mb-0 text-light small" id="preview-description">{{ banner.description }}</p>
                                                </div>
                                            </div>

                                            <!-- Call to Action -->
                                            <div class="col-md-4 text-end">
                                                <div class="d-flex align-items-center justify-content-end gap-2">
                                                    <a href="#" class="btn btn-sm fw-bold px-3 py-2"
                                                       style="background-color: #28a745; border-color: #28a745; color: white; border-radius: 25px; text-decoration: none; transition: all 0.3s ease;">
                                                        Join Now
                                                    </a>
                                                    <button class="btn btn-sm btn-outline-light" type="button"
                                                            style="border: none; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;">
                                                        <i class="fas fa-times" style="font-size: 0.75rem;"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>





                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;">
                                <i class="fas fa-save mr-2"></i>
                                Update Banner
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_promotional_banners') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>

{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form field focus effects
    $('.form-control, .enhanced-field').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const backgroundColorInput = document.getElementById('backgroundColor');

    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const bannerPreview = document.getElementById('banner-preview');

    // Update title preview
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            previewTitle.textContent = this.value || '{{ banner.title }}';
        });
    }

    // Update description preview
    if (descriptionInput) {
        descriptionInput.addEventListener('input', function() {
            previewDescription.textContent = this.value || '{{ banner.description }}';
            previewDescription.style.display = this.value ? 'block' : 'block';
        });
    }

    // Update background color preview
    if (backgroundColorInput) {
        backgroundColorInput.addEventListener('input', function() {
            bannerPreview.style.backgroundColor = this.value;
        });
    }


});
</script>

<style>
.form-group.focused .form-label {
    color: #011a2d;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #011a2d !important;
    transition: all 0.3s ease;
}

.enhanced-field:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Capitol Academy Datetime Picker Styling */
input[type="datetime-local"] {
    position: relative;
    background: white;
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    color: #343a40;
    transition: all 0.3s ease;
    height: calc(1.6em + 1.25rem + 4px);
}

input[type="datetime-local"]:hover {
    border-color: #011a2d !important;
    box-shadow: 0 2px 8px rgba(1, 26, 45, 0.15);
    background-color: #f8f9fa;
}

input[type="datetime-local"]:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
    background-color: white;
    transform: translateY(-1px);
}

/* Webkit datetime picker calendar icon styling */
input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    background-color: #011a2d;
    border-radius: 4px;
    cursor: pointer;
    padding: 4px;
    transition: all 0.3s ease;
}

input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
    background-color: #011a2d;
    transform: scale(1.1);
}

/* Firefox datetime picker styling */
input[type="datetime-local"]::-moz-datetime-picker {
    background-color: white;
    border: 1px solid #011a2d;
    border-radius: 8px;
}

/* Custom calendar popup styling for webkit browsers */
input[type="datetime-local"]::-webkit-datetime-edit {
    color: #343a40;
    font-weight: 500;
}

input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
    background: transparent;
}

input[type="datetime-local"]::-webkit-datetime-edit-text {
    color: #6c757d;
    padding: 0 0.25rem;
}

input[type="datetime-local"]::-webkit-datetime-edit-month-field,
input[type="datetime-local"]::-webkit-datetime-edit-day-field,
input[type="datetime-local"]::-webkit-datetime-edit-year-field,
input[type="datetime-local"]::-webkit-datetime-edit-hour-field,
input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
    background: transparent;
    color: #343a40;
    font-weight: 500;
    padding: 0 0.125rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

input[type="datetime-local"]::-webkit-datetime-edit-month-field:focus,
input[type="datetime-local"]::-webkit-datetime-edit-day-field:focus,
input[type="datetime-local"]::-webkit-datetime-edit-year-field:focus,
input[type="datetime-local"]::-webkit-datetime-edit-hour-field:focus,
input[type="datetime-local"]::-webkit-datetime-edit-minute-field:focus {
    background-color: rgba(1, 26, 45, 0.1);
    color: #011a2d;
    outline: none;
}

/* Banner Preview Styling */
.promotional-banner-preview {
    transition: all 0.3s ease;
}

.promotional-banner-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.countdown-timer {
    font-family: 'Courier New', monospace;
}

.countdown-item {
    min-width: 45px;
}

.countdown-number {
    font-size: 1.2rem;
    line-height: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 2px;
}

.countdown-label {
    font-size: 0.7rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.countdown-separator {
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0.7;
    margin: 0 5px;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}

/* Fix datetime input calendar icon styling */
.datetime-input::-webkit-calendar-picker-indicator {
    background: transparent !important;
    color: #6c757d !important;
    cursor: pointer !important;
    border: none !important;
    padding: 4px !important;
    border-radius: 4px !important;
}

.datetime-input::-webkit-calendar-picker-indicator:hover {
    background: rgba(108, 117, 125, 0.1) !important;
}

/* Firefox datetime input styling */
.datetime-input::-moz-calendar-picker-indicator {
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}
</style>
{% endblock %}
