<?php

namespace App\Tests;

use App\Service\StripeService;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class StripeIntegrationTest extends KernelTestCase
{
    public function testStripeServiceConfiguration(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        
        $stripeService = $container->get(StripeService::class);
        $this->assertInstanceOf(StripeService::class, $stripeService);
        
        // Test that publishable key is available
        $publishableKey = $stripeService->getPublishableKey();
        $this->assertNotEmpty($publishableKey);
        $this->assertStringStartsWith('pk_test_', $publishableKey);
    }
    
    public function testStripeAmountConversion(): void
    {
        self::bootKernel();
        $container = static::getContainer();
        
        $stripeService = $container->get(StripeService::class);
        
        // Test amount conversion to Stripe format (cents)
        $this->assertEquals(2999, $stripeService->convertToStripeAmount(29.99));
        $this->assertEquals(10000, $stripeService->convertToStripeAmount(100.00));
        $this->assertEquals(0, $stripeService->convertToStripeAmount(0));
        
        // Test amount formatting from Stripe format
        $this->assertEquals('29.99', $stripeService->formatAmount(2999));
        $this->assertEquals('100.00', $stripeService->formatAmount(10000));
        $this->assertEquals('0.00', $stripeService->formatAmount(0));
    }
}
