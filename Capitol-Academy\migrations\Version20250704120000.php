<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250704120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create payments and enrollments tables for Stripe integration';
    }

    public function up(Schema $schema): void
    {
        // Create payments table
        $this->addSql('CREATE TABLE payments (
            id INT AUTO_INCREMENT NOT NULL, 
            user_id INT NOT NULL, 
            course_id INT NOT NULL, 
            stripe_payment_id VARCHAR(255) NOT NULL, 
            amount NUMERIC(10, 2) NOT NULL, 
            currency VARCHAR(3) NOT NULL DEFAULT "USD", 
            status VARCHAR(50) NOT NULL DEFAULT "pending", 
            stripe_data JSON DEFAULT NULL, 
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)", 
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)", 
            UNIQUE INDEX UNIQ_65D29B32E7B2D8E5 (stripe_payment_id), 
            INDEX IDX_65D29B32A76ED395 (user_id), 
            INDEX IDX_65D29B32591CC992 (course_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create enrollments table
        $this->addSql('CREATE TABLE enrollments (
            id INT AUTO_INCREMENT NOT NULL, 
            user_id INT NOT NULL, 
            course_id INT NOT NULL, 
            payment_id INT DEFAULT NULL, 
            enrolled_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)", 
            completed_at DATETIME DEFAULT NULL COMMENT "(DC2Type:datetime_immutable)", 
            is_active TINYINT(1) NOT NULL DEFAULT 1, 
            progress_percentage INT DEFAULT 0, 
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)", 
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)", 
            UNIQUE INDEX unique_user_course (user_id, course_id), 
            INDEX IDX_CCD8C132A76ED395 (user_id), 
            INDEX IDX_CCD8C132591CC992 (course_id), 
            INDEX IDX_CCD8C1324C3A3BB (payment_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints
        $this->addSql('ALTER TABLE payments ADD CONSTRAINT FK_65D29B32A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE payments ADD CONSTRAINT FK_65D29B32591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C132A76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C132591CC992 FOREIGN KEY (course_id) REFERENCES course (id)');
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C1324C3A3BB FOREIGN KEY (payment_id) REFERENCES payments (id)');
    }

    public function down(Schema $schema): void
    {
        // Drop foreign key constraints first
        $this->addSql('ALTER TABLE enrollments DROP FOREIGN KEY FK_CCD8C1324C3A3BB');
        $this->addSql('ALTER TABLE enrollments DROP FOREIGN KEY FK_CCD8C132591CC992');
        $this->addSql('ALTER TABLE enrollments DROP FOREIGN KEY FK_CCD8C132A76ED395');
        $this->addSql('ALTER TABLE payments DROP FOREIGN KEY FK_65D29B32591CC992');
        $this->addSql('ALTER TABLE payments DROP FOREIGN KEY FK_65D29B32A76ED395');
        
        // Drop tables
        $this->addSql('DROP TABLE enrollments');
        $this->addSql('DROP TABLE payments');
    }
}
