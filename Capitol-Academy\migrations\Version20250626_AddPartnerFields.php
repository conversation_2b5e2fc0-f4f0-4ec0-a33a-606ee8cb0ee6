<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add websiteUrl and description fields to partner table
 */
final class Version20250626_AddPartnerFields extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add websiteUrl and description fields to partner table';
    }

    public function up(Schema $schema): void
    {
        // Add new fields to partner table
        $this->addSql('ALTER TABLE partner ADD COLUMN website_url VARCHAR(500) NULL AFTER logo_path');
        $this->addSql('ALTER TABLE partner ADD COLUMN description TEXT NULL AFTER website_url');
    }

    public function down(Schema $schema): void
    {
        // Remove the added fields
        $this->addSql('ALTER TABLE partner DROP COLUMN website_url');
        $this->addSql('ALTER TABLE partner DROP COLUMN description');
    }
}
