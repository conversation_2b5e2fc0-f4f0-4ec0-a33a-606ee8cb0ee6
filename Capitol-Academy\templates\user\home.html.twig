{% extends 'base.html.twig' %}

{% block title %}Welcome - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- User Welcome Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        Hello {{ user.firstName }} {{ user.lastName }}!
                    </h1>
                    <p class="lead mb-4">
                        Welcome back to Capitol Academy. Continue your trading journey with our professional courses.
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <a href="{{ path('app_courses') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Browse Courses
                        </a>
                        <a href="{{ path('app_user_profile') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-user me-2"></i>
                            My Profile
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="user-avatar-large">
                        <img src="https://via.placeholder.com/200x200/dc3545/ffffff?text={{ user.firstName|first }}{{ user.lastName|first }}" 
                             alt="User Avatar" 
                             class="rounded-circle img-fluid shadow-lg"
                             style="max-width: 200px; border: 5px solid rgba(255,255,255,0.3);">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- User Dashboard Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- User Information Card -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                Your Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Name:</strong><br>
                                {{ user.fullName }}
                            </div>
                            <div class="mb-3">
                                <strong>Email:</strong><br>
                                <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                            </div>
                            {% if user.country %}
                            <div class="mb-3">
                                <strong>Country:</strong><br>
                                <span class="badge bg-info">{{ user.country }}</span>
                            </div>
                            {% endif %}
                            {% if user.phone %}
                            <div class="mb-3">
                                <strong>Phone:</strong><br>
                                <a href="tel:{{ user.phone }}">{{ user.phone }}</a>
                            </div>
                            {% endif %}
                            <div class="mb-3">
                                <strong>Member Since:</strong><br>
                                <small class="text-muted">{{ user.createdAt|date('F j, Y') }}</small>
                            </div>
                            <div class="mb-3">
                                <strong>Account Status:</strong><br>
                                {% if user.isVerified %}
                                    <span class="badge bg-success">Verified</span>
                                {% else %}
                                    <span class="badge bg-warning">Pending Verification</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="{{ path('app_user_profile') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-edit me-2"></i>
                                Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-3">
                                <a href="{{ path('app_courses') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Browse All Courses
                                </a>
                                <a href="{{ path('app_contact') }}" class="btn btn-outline-info">
                                    <i class="fas fa-envelope me-2"></i>
                                    Contact Support
                                </a>
                                <a href="{{ path('app_about') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-info-circle me-2"></i>
                                    About Capitol Academy
                                </a>
                                <a href="{{ path('app_logout') }}" class="btn btn-outline-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Card -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Account Created</h6>
                                        <p class="timeline-text">
                                            You joined Capitol Academy
                                        </p>
                                        <small class="text-muted">{{ user.createdAt|date('M d, Y') }}</small>
                                    </div>
                                </div>
                                {% if user.isVerified %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Account Verified</h6>
                                        <p class="timeline-text">
                                            Your account was verified
                                        </p>
                                        <small class="text-muted">{{ user.updatedAt|date('M d, Y') }}</small>
                                    </div>
                                </div>
                                {% endif %}
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Last Login</h6>
                                        <p class="timeline-text">
                                            You logged in to your account
                                        </p>
                                        <small class="text-muted">Today</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Courses Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-center mb-5">Continue Your Trading Journey</h2>
                    <div class="text-center">
                        <a href="{{ path('app_courses') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-graduation-cap me-2"></i>
                            Explore Our Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.85rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.user-avatar-large img {
    transition: transform 0.3s ease;
}

.user-avatar-large img:hover {
    transform: scale(1.05);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}
</style>
{% endblock %}
