<?php

namespace App\Entity;

use App\Repository\ContactRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ContactRepository::class)]
class Contact
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(name: 'full_name', length: 200)]
    #[Assert\NotBlank(message: 'Full name is required')]
    #[Assert\Length(max: 200, maxMessage: 'Full name cannot be longer than 200 characters')]
    private ?string $fullName = null;

    #[ORM\Column(length: 255)]
    private ?string $email = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $country = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $subject = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $message = null;

    #[ORM\Column(length: 45, nullable: true)]
    private ?string $ipAddress = null;

    #[ORM\Column(name: 'createdAt')]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(name: 'isProcessed')]
    private ?bool $isProcessed = false;

    #[ORM\Column(name: 'source_page', length: 100, nullable: true)]
    private ?string $sourcePage = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFullName(): ?string
    {
        return $this->fullName;
    }

    public function setFullName(string $fullName): static
    {
        $this->fullName = $fullName;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(?string $subject): static
    {
        $this->subject = $subject;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(?string $message): static
    {
        $this->message = $message;

        return $this;
    }

    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    public function setIpAddress(?string $ipAddress): static
    {
        $this->ipAddress = $ipAddress;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function isProcessed(): ?bool
    {
        return $this->isProcessed;
    }

    public function setProcessed(bool $isProcessed): static
    {
        $this->isProcessed = $isProcessed;
        return $this;
    }

    public function getSourcePage(): ?string
    {
        return $this->sourcePage;
    }

    public function setSourcePage(?string $sourcePage): static
    {
        $this->sourcePage = $sourcePage;
        return $this;
    }

    /**
     * Generate a URL-friendly slug from name and subject
     * Format: {name-slug}-{subject-slug}
     */
    public function getUrlSlug(): string
    {
        $nameSlug = $this->slugify($this->fullName ?? 'unknown');
        $subjectSlug = $this->slugify($this->subject ?? 'contact');

        return $nameSlug . '-' . $subjectSlug;
    }

    /**
     * Convert a string to a URL-friendly slug
     */
    private function slugify(string $text): string
    {
        // Replace non-alphanumeric characters with hyphens
        $slug = preg_replace('/[^a-zA-Z0-9]+/', '-', $text);

        // Convert to lowercase
        $slug = strtolower($slug);

        // Remove leading/trailing hyphens
        $slug = trim($slug, '-');

        // Limit length to 30 characters for URL friendliness
        if (strlen($slug) > 30) {
            $slug = substr($slug, 0, 30);
            $slug = rtrim($slug, '-');
        }

        return $slug ?: 'contact';
    }

}
