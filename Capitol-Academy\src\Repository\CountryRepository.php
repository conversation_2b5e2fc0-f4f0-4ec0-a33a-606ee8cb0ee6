<?php

namespace App\Repository;

use App\Entity\Country;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Country>
 */
class CountryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Country::class);
    }

    /**
     * Find all countries ordered by name
     */
    public function findAllOrderedByName(): array
    {
        return $this->createQueryBuilder('c')
            ->orderBy('c.countryName', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find country by name
     */
    public function findByName(string $name): ?Country
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.countryName = :name')
            ->setParameter('name', $name)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find countries for form choices
     */
    public function getCountryChoices(): array
    {
        $countries = $this->findAllOrderedByName();
        $choices = [];
        
        foreach ($countries as $country) {
            $choices[$country->getDisplayName()] = $country->getId();
        }
        
        return $choices;
    }

    /**
     * Find popular countries (commonly used ones)
     */
    public function findPopularCountries(): array
    {
        $popularCountryNames = [
            'Tunisia',
            'Algeria',
            'Morocco',
            'Egypt',
            'Libya',
            'United States of America',
            'United Kingdom',
            'France',
            'Germany',
            'Canada'
        ];

        return $this->createQueryBuilder('c')
            ->andWhere('c.countryName IN (:names)')
            ->setParameter('names', $popularCountryNames)
            ->orderBy('c.countryName', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get country data for JavaScript
     */
    public function getCountryDataForJs(): array
    {
        $countries = $this->findAllOrderedByName();
        $data = [];
        
        foreach ($countries as $country) {
            $data[$country->getId()] = [
                'name' => $country->getCountryName(),
                'prefix' => $country->getPhonePrefix(),
                'length' => $country->getPhoneNumberLength(),
                'pattern' => $country->getPhoneValidationPattern()
            ];
        }
        
        return $data;
    }

    public function save(Country $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Country $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
