<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add username, permissions, and isMasterAdmin fields to admin table
 */
final class Version20250615120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add username, permissions, and isMasterAdmin fields to admin table for enhanced admin system';
    }

    public function up(Schema $schema): void
    {
        // Add username field
        $this->addSql('ALTER TABLE `admin` ADD username VARCHAR(50) NOT NULL');
        
        // Add unique constraint for username
        $this->addSql('ALTER TABLE `admin` ADD UNIQUE INDEX UNIQ_880E0D76F85E0677 (username)');
        
        // Add permissions field (JSON)
        $this->addSql('ALTER TABLE `admin` ADD permissions JSON NOT NULL');
        
        // Add isMasterAdmin field
        $this->addSql('ALTER TABLE `admin` ADD is_master_admin TINYINT(1) NOT NULL DEFAULT 0');
        
        // Set default empty permissions array for existing admins
        $this->addSql('UPDATE `admin` SET permissions = JSON_ARRAY()');
    }

    public function down(Schema $schema): void
    {
        // Remove the added fields
        $this->addSql('ALTER TABLE `admin` DROP INDEX UNIQ_880E0D76F85E0677');
        $this->addSql('ALTER TABLE `admin` DROP username');
        $this->addSql('ALTER TABLE `admin` DROP permissions');
        $this->addSql('ALTER TABLE `admin` DROP is_master_admin');
    }
}
