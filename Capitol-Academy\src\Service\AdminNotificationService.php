<?php

namespace App\Service;

use App\Entity\Payment;
use App\Entity\Admin;
use App\Repository\AdminRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class AdminNotificationService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AdminRepository $adminRepository,
        private MailerInterface $mailer,
        private UrlGeneratorInterface $urlGenerator,
        private LoggerInterface $logger
    ) {}

    /**
     * Notify admins about successful payment (simplified version without enrollment)
     */
    public function notifyPaymentSuccess(Payment $payment): void
    {
        try {
            // Get all active admins with payment permissions
            $admins = $this->adminRepository->findActiveAdminsWithPermission('payments.read');

            if (empty($admins)) {
                $this->logger->warning('No admins found to notify about payment success');
                return;
            }

            $course = $payment->getCourse();
            $user = $payment->getUser();

            // Create email content
            $subject = sprintf(
                'Payment Received - %s (%s)',
                $course->getTitle(),
                $course->getCode()
            );

            $emailBody = $this->createSimplePaymentNotificationEmailBody($payment);

            // Send notification to each admin
            foreach ($admins as $admin) {
                $this->sendEmailNotification($admin, $subject, $emailBody);
            }

            $this->logger->info(sprintf(
                'Payment success notification sent to %d admins for payment ID: %d',
                count($admins),
                $payment->getId()
            ));

        } catch (\Exception $e) {
            $this->logger->error('Failed to send payment success notification: ' . $e->getMessage());
        }
    }



    /**
     * Notify admins about failed payment
     */
    public function notifyPaymentFailure(Payment $payment): void
    {
        try {
            $admins = $this->adminRepository->findActiveAdminsWithPermission('enrollments.read');
            
            if (empty($admins)) {
                return;
            }

            $course = $payment->getCourse();
            $user = $payment->getUser();
            
            $subject = sprintf(
                'Payment Failed - %s (%s)',
                $course->getTitle(),
                $course->getCode()
            );
            
            $emailBody = $this->createPaymentFailureEmailBody($payment);

            foreach ($admins as $admin) {
                $this->sendEmailNotification($admin, $subject, $emailBody);
            }

            $this->logger->info('Payment failure notification sent to admins for payment ID: ' . $payment->getId());

        } catch (\Exception $e) {
            $this->logger->error('Failed to send payment failure notification: ' . $e->getMessage());
        }
    }



    private function createPaymentFailureEmailBody(Payment $payment): string
    {
        $course = $payment->getCourse();
        $user = $payment->getUser();
        
        return sprintf('
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background: linear-gradient(135deg, #a90418 0%%, #8b0314 100%%); color: white; padding: 20px; text-align: center;">
                    <h2 style="margin: 0;">Payment Failed</h2>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">Capitol Academy Admin Notification</p>
                </div>
                
                <div style="padding: 30px; background: #f8f9fa;">
                    <h3 style="color: #a90418; margin-top: 0;">Payment Failure Details</h3>
                    
                    <table style="width: 100%%; border-collapse: collapse; margin: 20px 0;">
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold; width: 30%%;">Student:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s %s (%s)</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Course:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s (%s)</td>
                        </tr>
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Amount:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">$%s</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Payment Status:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 4px;">Failed</span></td>
                        </tr>
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Failure Date:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s</td>
                        </tr>
                    </table>
                    
                    <p style="color: #6c757d; font-size: 14px; margin-top: 30px;">
                        A payment attempt has failed. You may want to follow up with the student or investigate the issue.
                    </p>
                </div>
            </div>
        ',
            $user->getFirstName(),
            $user->getLastName(),
            $user->getEmail(),
            $course->getTitle(),
            $course->getCode(),
            number_format((float)$payment->getAmount(), 2),
            (new \DateTimeImmutable())->format('M j, Y g:i A')
        );
    }

    private function createSimplePaymentNotificationEmailBody(Payment $payment): string
    {
        $course = $payment->getCourse();
        $user = $payment->getUser();

        return sprintf('
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background: linear-gradient(135deg, #011a2d 0%%, #1a3461 100%%); color: white; padding: 20px; text-align: center;">
                    <h2 style="margin: 0;">Payment Received</h2>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">Capitol Academy Admin Notification</p>
                </div>

                <div style="padding: 30px; background: #f8f9fa;">
                    <h3 style="color: #011a2d; margin-top: 0;">Payment Details</h3>

                    <table style="width: 100%%; border-collapse: collapse; margin: 20px 0;">
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold; width: 30%%;">Customer:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s %s (%s)</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Course:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s (%s)</td>
                        </tr>
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Amount:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">$%s</td>
                        </tr>
                        <tr style="background: #f8f9fa;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Payment Status:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px;">Successful</span></td>
                        </tr>
                        <tr style="background: white;">
                            <td style="padding: 12px; border: 1px solid #dee2e6; font-weight: bold;">Payment Date:</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">%s</td>
                        </tr>
                    </table>

                    <p style="color: #6c757d; font-size: 14px; margin-top: 30px;">
                        This is an automated notification from Capitol Academy. Please follow up with the customer regarding course access and enrollment.
                    </p>
                </div>
            </div>
        ',
            $user->getFirstName(),
            $user->getLastName(),
            $user->getEmail(),
            $course->getTitle(),
            $course->getCode(),
            number_format((float)$payment->getAmount(), 2),
            $payment->getCreatedAt()->format('M j, Y g:i A')
        );
    }

    private function sendEmailNotification(Admin $admin, string $subject, string $body): void
    {
        try {
            $email = (new Email())
                ->from('<EMAIL>')
                ->to($admin->getEmail())
                ->subject($subject)
                ->html($body);

            $this->mailer->send($email);

            $this->logger->info(sprintf(
                'Email notification sent successfully to admin: %s',
                $admin->getEmail()
            ));

        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                'Failed to send email to admin %s: %s',
                $admin->getEmail(),
                $e->getMessage()
            ));

            // Log the failure but don't throw exception to prevent breaking the payment flow
            $this->logger->warning(sprintf(
                'Email notification failed for admin %s, but payment processing will continue',
                $admin->getEmail()
            ));
        }
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration(): bool
    {
        try {
            // Try to send a test email to verify configuration
            $testEmail = (new Email())
                ->from('<EMAIL>')
                ->to('<EMAIL>')
                ->subject('Test Email Configuration')
                ->text('This is a test email to verify email configuration.');

            // Don't actually send, just validate the configuration
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Email configuration test failed: ' . $e->getMessage());
            return false;
        }
    }
}
