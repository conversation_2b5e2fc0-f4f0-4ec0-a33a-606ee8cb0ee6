{% extends 'admin/base.html.twig' %}

{% block title %}Edit Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Course{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_courses') }}">Courses</a></li>
<li class="breadcrumb-item active">Edit Course</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Edit Course
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Courses Button -->
                        <a href="{{ path('admin_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <form method="post" id="course-form" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('course_edit') }}">
            <input type="hidden" name="is_active" value="{{ course.isActive ? '1' : '0' }}">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Course Code and Title Row -->
                            <div class="row">
                                <!-- Course Code -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code" class="form-label">
                                            <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="code"
                                               name="code"
                                               value="{{ course.code }}"
                                               placeholder="e.g., TRAD101, FIN200"
                                               required
                                               maxlength="10"
                                               pattern="[A-Za-z]{2,4}[0-9]{1,4}"
                                               title="Format: 2-4 letters followed by 1-4 numbers (e.g., TRAD101, FIN200)"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a valid course code (e.g., TRAD101, FIN200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Title <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="title"
                                               name="title"
                                               value="{{ course.title }}"
                                               placeholder="Enter comprehensive course title"
                                               required
                                               maxlength="255"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class="row">
                                <!-- Course Category -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category" class="form-label">
                                            <i class="fas fa-tags" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                            Category <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="category"
                                                name="category"
                                                required
                                                aria-describedby="category_help category_error"
                                                aria-label="Select a course category"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value="{{ category.name }}" {% if course.category == category.name %}selected{% endif %}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id="category_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="level" class="form-label">
                                            <i class="fas fa-layer-group" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                            Level <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="level"
                                                name="level"
                                                required
                                                aria-describedby="level_help level_error"
                                                aria-label="Select a course difficulty level"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a level...</option>
                                            <option value="Beginner" {% if course.level == 'Beginner' %}selected{% endif %}>Beginner</option>
                                            <option value="Intermediate" {% if course.level == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                            <option value="Advanced" {% if course.level == 'Advanced' %}selected{% endif %}>Advanced</option>
                                        </select>
                                        <div id="level_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <!-- Course Description -->
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Description <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control enhanced-field"
                                          id="description"
                                          name="description"
                                          rows="8"
                                          placeholder="Enter comprehensive course description including objectives, target audience, and key topics..."
                                          required
                                          style="min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">{{ course.description }}</textarea>
                                <div class="invalid-feedback">
                                    Please provide a comprehensive course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Learning Outcomes <span class="text-danger">*</span>
                                </label>

                                <div id="learning-outcomes-container">
                                    {% if course.learningOutcomes and course.learningOutcomes|length > 0 %}
                                        {% for outcome in course.learningOutcomes %}
                                            <div class="input-group mb-2 learning-outcome-item">
                                                <input type="text"
                                                       class="form-control enhanced-field"
                                                       name="learning_outcomes[]"
                                                       value="{{ outcome }}"
                                                       placeholder="e.g., Analyze market trends and identify trading opportunities"
                                                       required
                                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                                <div class="input-group-append">
                                                    {% if loop.first %}
                                                        <button type="button" class="btn add-learning-outcome" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type="button" class="btn remove-learning-outcome" style="background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="input-group mb-2 learning-outcome-item">
                                            <input type="text"
                                                   class="form-control enhanced-field"
                                                   name="learning_outcomes[]"
                                                   placeholder="e.g., Analyze market trends and identify trading opportunities"
                                                   required
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                            <div class="input-group-append">
                                                <button type="button" class="btn add-learning-outcome" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="invalid-feedback">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Features <span class="text-danger">*</span>
                                </label>

                                <div id="features-container">
                                    {% if course.features and course.features|length > 0 %}
                                        {% for feature in course.features %}
                                            <div class="input-group mb-2 feature-item">
                                                <input type="text"
                                                       class="form-control enhanced-field"
                                                       name="features[]"
                                                       value="{{ feature }}"
                                                       placeholder="e.g., Live trading sessions, Real-time market analysis, Downloadable resources"
                                                       required
                                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                                <div class="input-group-append">
                                                    {% if loop.first %}
                                                        <button type="button" class="btn add-feature" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    {% else %}
                                                        <button type="button" class="btn remove-feature" style="background: #dc3545; color: white; border: 2px solid #dc3545; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="input-group mb-2 feature-item">
                                            <input type="text"
                                                   class="form-control enhanced-field"
                                                   name="features[]"
                                                   placeholder="e.g., Live trading sessions, Real-time market analysis, Downloadable resources"
                                                   required
                                                   style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                            <div class="input-group-append">
                                                <button type="button" class="btn add-feature" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="invalid-feedback">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            </div>

                            <!-- Course Images -->
                            <div class="form-group">
                                <!-- Thumbnail Image - Full Width -->
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="thumbnail_image" class="form-label">
                                            <i class="fas fa-image" style="color: #007bff; margin-right: 0.5rem;"></i>Thumbnail Image <span class="text-danger">*</span>
                                        </label>
                                        <input type="file"
                                               class="form-control enhanced-file-field"
                                               id="thumbnail_image"
                                               name="thumbnail_image"
                                               accept="image/jpeg,image/png,image/jpg"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">

                                        <!-- Centered Image Preview -->
                                        <div class="image-preview mt-4 text-center" id="thumbnail-preview" {% if course.thumbnailImage %}style="display: block;"{% else %}style="display: none;"{% endif %}>
                                            <div class="professional-image-container mx-auto" style="width: 300px; height: 200px; border: 2px solid #011a2d; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 4px 12px rgba(1,26,45,0.15);">
                                                <img src="{% if course.thumbnailImage %}{{ course.thumbnailUrl }}{% endif %}" alt="Thumbnail Preview" class="w-100 h-100" style="object-fit: cover;">
                                            </div>
                                            {% if course.thumbnailImage %}
                                                <small class="text-muted d-block mt-2">Current thumbnail (300x200px recommended)</small>
                                            {% else %}
                                                <small class="text-muted d-block mt-2">Thumbnail Preview (300x200px recommended)</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>


                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <div class="form-check form-switch" style="padding-left: 0; margin-left: 0;">
                                    <input type="checkbox"
                                           class="form-check-input"
                                           id="has_modules"
                                           name="has_modules"
                                           value="1"
                                           {% if course.hasModules %}checked{% endif %}
                                           style="transform: scale(1.2); margin-left: 0;">
                                    <label class="form-check-label" for="has_modules" style="margin-left: 2.5rem; padding-top: 0.125rem;">
                                        <i class="fas fa-list" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id="modules-section" class="form-group" style="display: {% if course.hasModules %}block{% else %}none{% endif %};">
                                <label class="form-label">
                                    <i class="fas fa-puzzle-piece" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Modules
                                </label>

                                <div id="modules-container">
                                    {% if course.modules and course.modules|length > 0 %}
                                        {% for module in course.modules %}
                                            <div class="module-item" style="border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                                                <div class="module-header d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="mb-0" style="color: #007bff; font-weight: 700;">
                                                        <i class="fas fa-cube" style="margin-right: 0.5rem;"></i>
                                                        Module <span class="module-number">{{ loop.index }}</span>
                                                    </h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-module" style="border-radius: 50%; width: 35px; height: 35px;">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>

                                                <input type="hidden" name="modules[{{ loop.index0 }}][id]" value="{{ module.id }}">

                                                <!-- Module Basic Info -->
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">
                                                                <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                                Module Code
                                                            </label>
                                                            <input type="text" class="form-control enhanced-field module-code" name="modules[{{ loop.index0 }}][code]" value="{{ module.code }}" placeholder="e.g., Module-{{ loop.index }}" style="height: calc(1.6em + 1.25rem + 4px)">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">
                                                                <i class="fas fa-tag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                                Module Title <span class="text-danger">*</span>
                                                            </label>
                                                            <input type="text" class="form-control enhanced-field module-title" name="modules[{{ loop.index0 }}][title]" value="{{ module.title }}" placeholder="e.g., Introduction to Trading Fundamentals" required style="height: calc(1.6em + 1.25rem + 4px)">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Description -->
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                        Module Description <span class="text-danger">*</span>
                                                    </label>
                                                    <textarea class="form-control enhanced-field module-description" name="modules[{{ loop.index0 }}][description]" rows="6" placeholder="Detailed description of what this module covers..." required style="min-height: 100px;">{{ module.description }}</textarea>
                                                </div>

                                                <!-- Module Status -->
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">
                                                                <i class="fas fa-toggle-on" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                                Module Status <span class="text-danger">*</span>
                                                            </label>
                                                            <select class="form-select enhanced-dropdown" name="modules[{{ loop.index0 }}][is_active]" required style="height: calc(1.6em + 1.25rem + 4px)">
                                                                <option value="1" {{ module.isActive ? 'selected' : '' }}>Active</option>
                                                                <option value="0" {{ not module.isActive ? 'selected' : '' }}>Inactive</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">
                                                                <i class="fas fa-sort-numeric-up" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                                Display Order
                                                            </label>
                                                            <input type="number" class="form-control enhanced-field" name="modules[{{ loop.index0 }}][sort_order]" value="{{ module.sortOrder|default(loop.index) }}" placeholder="e.g., 1" min="1" style="height: calc(1.6em + 1.25rem + 4px)">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Module Learning Outcomes -->
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                        Learning Outcomes <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="learning-outcomes-container">
                                                        {% if module.learningOutcomes and module.learningOutcomes|length > 0 %}
                                                            {% for outcome in module.learningOutcomes %}
                                                                <div class="input-group mb-2">
                                                                    <input type="text" class="form-control enhanced-field" name="modules[{{ loop.parent.loop.index0 }}][learning_outcomes][]" value="{{ outcome }}" placeholder="e.g., Master advanced chart analysis techniques" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                                    <div class="input-group-append">
                                                                        {% if loop.first %}
                                                                            <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                                <i class="fas fa-plus"></i>
                                                                            </button>
                                                                        {% else %}
                                                                            <button type="button" class="btn btn-danger remove-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                                <i class="fas fa-minus"></i>
                                                                            </button>
                                                                        {% endif %}
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                        {% else %}
                                                            <div class="input-group mb-2">
                                                                <input type="text" class="form-control enhanced-field" name="modules[{{ loop.index0 }}][learning_outcomes][]" placeholder="e.g., Master advanced chart analysis techniques" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                                <div class="input-group-append">
                                                                    <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                        <i class="fas fa-plus"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Module Features -->
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                        Features <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="features-container">
                                                        {% if module.features and module.features|length > 0 %}
                                                            {% for feature in module.features %}
                                                                <div class="input-group mb-2">
                                                                    <input type="text" class="form-control enhanced-field" name="modules[{{ loop.parent.loop.index0 }}][features][]" value="{{ feature }}" placeholder="e.g., Interactive trading simulator" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                                    <div class="input-group-append">
                                                                        {% if loop.first %}
                                                                            <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                                <i class="fas fa-plus"></i>
                                                                            </button>
                                                                        {% else %}
                                                                            <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                                <i class="fas fa-minus"></i>
                                                                            </button>
                                                                        {% endif %}
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                        {% else %}
                                                            <div class="input-group mb-2">
                                                                <input type="text" class="form-control enhanced-field" name="modules[{{ loop.index0 }}][features][]" placeholder="e.g., Interactive trading simulator" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                                <div class="input-group-append">
                                                                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                        <i class="fas fa-plus"></i>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Hidden fields -->
                                                <input type="hidden" name="modules[{{ loop.index0 }}][is_active]" value="1">
                                                <input type="hidden" name="modules[{{ loop.index0 }}][sort_order]" value="{{ loop.index }}">
                                            </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="module-item" style="border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                                            <div class="module-header d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0" style="color: #007bff; font-weight: 700;">
                                                    <i class="fas fa-cube" style="margin-right: 0.5rem;"></i>
                                                    Module <span class="module-number">1</span>
                                                </h6>
                                                <button type="button" class="btn btn-sm btn-outline-danger remove-module" style="border-radius: 50%; width: 35px; height: 35px;">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>

                                            <!-- Module Basic Info -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                            Module Code
                                                        </label>
                                                        <input type="text" class="form-control enhanced-field module-code" name="modules[0][code]" placeholder="e.g., Module-1" style="height: calc(1.6em + 1.25rem + 4px)">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            <i class="fas fa-tag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                            Module Title <span class="text-danger">*</span>
                                                        </label>
                                                        <input type="text" class="form-control enhanced-field module-title" name="modules[0][title]" placeholder="e.g., Introduction to Trading Fundamentals" required style="height: calc(1.6em + 1.25rem + 4px)">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Description -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                    Module Description <span class="text-danger">*</span>
                                                </label>
                                                <textarea class="form-control enhanced-field module-description" name="modules[0][description]" rows="6" placeholder="Detailed description of what this module covers..." required style="min-height: 100px;"></textarea>
                                            </div>

                                            <!-- Module Status -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            <i class="fas fa-toggle-on" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                            Module Status <span class="text-danger">*</span>
                                                        </label>
                                                        <select class="form-select enhanced-dropdown" name="modules[0][is_active]" required style="height: calc(1.6em + 1.25rem + 4px)">
                                                            <option value="1" selected>Active</option>
                                                            <option value="0">Inactive</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            <i class="fas fa-sort-numeric-up" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                            Display Order
                                                        </label>
                                                        <input type="number" class="form-control enhanced-field" name="modules[0][sort_order]" placeholder="e.g., 1" min="1" value="1" style="height: calc(1.6em + 1.25rem + 4px)">
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Learning Outcomes -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                    Learning Outcomes <span class="text-danger">*</span>
                                                </label>
                                                <div class="learning-outcomes-container">
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control enhanced-field" name="modules[0][learning_outcomes][]" placeholder="e.g., Master advanced chart analysis techniques" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Module Features -->
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                    Features <span class="text-danger">*</span>
                                                </label>
                                                <div class="features-container">
                                                    <div class="input-group mb-2">
                                                        <input type="text" class="form-control enhanced-field" name="modules[0][features][]" placeholder="e.g., Interactive trading simulator" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                                                        <div class="input-group-append">
                                                            <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                                <i class="fas fa-plus"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hidden fields -->
                                            <input type="hidden" name="modules[0][is_active]" value="1">
                                            <input type="hidden" name="modules[0][sort_order]" value="1">
                                        </div>
                                    {% endif %}
                                </div>

                                <button type="button" id="add-module" class="btn btn-outline-primary">
                                    <i class="fas fa-plus" style="margin-right: 0.5rem;"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class="form-footer" style="background: #f8f9fa; border-top: 2px solid #e9ecef; padding: 2rem; margin: 2rem -2rem -2rem -2rem; border-radius: 0 0 12px 12px;">
                            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                                <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; font-weight: 700; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px; margin-right: 1rem; margin-bottom: 1rem; box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);">
                                    <i class="fas fa-save mr-2"></i>
                                    Update Course
                                </button>
                                <a href="{{ path('admin_courses') }}" class="btn btn-lg" style="background: #6c757d; color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; margin-left: 1rem; margin-bottom: 1rem; transition: all 0.3s ease; text-decoration: none;">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class="modal fade" id="courseValidationModal" tabindex="-1" aria-labelledby="courseValidationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;">
                <h5 class="modal-title" id="courseValidationModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Cannot Remove Module
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-info-circle text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mt-3 mb-2">Module Required</h6>
                <p class="text-muted mb-3"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class="modal fade" id="moduleValidationModal" tabindex="-1" aria-labelledby="moduleValidationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;">
                    <h6 class="modal-title" id="moduleValidationModalLabel" style="font-weight: 600;">
                        <i class="fas fa-exclamation-triangle me-2"></i>Module Required
                    </h6>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 1rem; text-align: center;">
                    <p class="mb-3" style="color: #a90418;">
                        At least one module is required.
                    </p>
                    <small class="text-muted">Please add a module or disable the "Enable Course Modules" option.</small>
                </div>
                <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal" style="transition: all 0.3s ease;">
                        <i class="fas fa-check me-1"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Function to toggle module field validation based on "Course Has Modules" state
    function toggleModuleValidation() {
        var hasModules = $('#has_modules').is(':checked');
        var moduleInputs = $('#modules-section input[name*="modules"], #modules-section textarea[name*="modules"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                $(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                $(this).removeAttr('required');
                // Clear any validation state
                $(this).removeClass('is-invalid is-valid');
                $(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Course';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating Course...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    $(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion (disabled for edit mode)
    // Price formatting
    $('#price').on('blur', function() {
        var value = parseFloat($(this).val());
        if (!isNaN(value)) {
            $(this).val(value.toFixed(2));
        }
    });

    // Form enhancement animations
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    $(document).on('click', '.add-learning-outcome', function() {
        var container = $('#learning-outcomes-container');
        var newItem = `
            <div class="input-group mb-2 learning-outcome-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="learning_outcomes[]"
                       placeholder="Enter a learning outcome..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-learning-outcome">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-learning-outcome">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    $(document).on('click', '.remove-learning-outcome', function() {
        $(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    $(document).on('click', '.add-feature', function() {
        var container = $('#features-container');
        var newItem = `
            <div class="input-group mb-2 feature-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="features[]"
                       placeholder="e.g., Live instructor sessions, Downloadable resources..."
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-feature">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    $(document).on('click', '.remove-feature', function() {
        $(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    $('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });

    $('#banner_image').on('change', function() {
        previewImage(this, '#banner-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $(previewSelector).show();
                $(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            $(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = {{ course.modules ? course.modules|length : 0 }};

    // Toggle module section visibility
    $('#has_modules').on('change', function() {
        if ($(this).is(':checked')) {
            $('#modules-section').slideDown();
        } else {
            $('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    $('#add-module').on('click', function() {
        var newModule = `
            <div class="module-item" style="border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                <div class="module-header d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0" style="color: #007bff; font-weight: 700;">
                        <i class="fas fa-cube" style="margin-right: 0.5rem;"></i>
                        Module <span class="module-number">${moduleIndex + 1}</span>
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-module" style="border-radius: 50%; width: 35px; height: 35px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Module Code
                            </label>
                            <input type="text" class="form-control enhanced-field module-code" name="modules[${moduleIndex}][code]" placeholder="e.g., Module-${moduleIndex + 1}" style="height: calc(1.6em + 1.25rem + 4px)">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Module Title <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control enhanced-field module-title" name="modules[${moduleIndex}][title]" placeholder="e.g., Advanced Trading Strategies" required style="height: calc(1.6em + 1.25rem + 4px)">
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Module Description <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control enhanced-field module-description" name="modules[${moduleIndex}][description]" rows="6" placeholder="Detailed description of what this module covers..." required style="min-height: 150px;"></textarea>
                </div>

                <!-- Module Status -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-toggle-on" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Module Status <span class="text-danger">*</span>
                            </label>
                            <select class="form-select enhanced-dropdown" name="modules[${moduleIndex}][is_active]" required style="height: calc(1.6em + 1.25rem + 4px)">
                                <option value="1" selected>Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-sort-numeric-up" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Display Order
                            </label>
                            <input type="number" class="form-control enhanced-field" name="modules[${moduleIndex}][sort_order]" placeholder="e.g., 1" min="1" value="${moduleIndex + 1}" style="height: calc(1.6em + 1.25rem + 4px)">
                        </div>
                    </div>
                </div>

                <!-- Module Learning Outcomes -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Learning Outcomes <span class="text-danger">*</span>
                    </label>
                    <div class="learning-outcomes-container">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][learning_outcomes][]" placeholder="e.g., Master advanced chart analysis techniques" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Features <span class="text-danger">*</span>
                    </label>
                    <div class="features-container">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][features][]" placeholder="e.g., Interactive trading simulator" required style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0.375rem 0 0 0.375rem;">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type="hidden" name="modules[${moduleIndex}][is_active]" value="1">
                <input type="hidden" name="modules[${moduleIndex}][sort_order]" value="${moduleIndex + 1}">
            </div>
        `;
        $('#modules-container').append(newModule);
        moduleIndex++;
        toggleModuleValidation();
    });

    $(document).on('click', '.remove-module', function() {
        if ($('.module-item').length > 1) {
            $(this).closest('.module-item').remove();
            // Renumber modules
            $('#modules-container .module-item').each(function(index) {
                $(this).find('.module-number').text(index + 1);
            });
        } else {
            const modal = new bootstrap.Modal(document.getElementById('moduleValidationModal'));
            modal.show();
        }
        toggleModuleValidation();
    });

    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        $(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        $(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced field styling
    $('.enhanced-field, .enhanced-dropdown').on('focus', function() {
        $(this).css('border-color', '#007bff');
        $(this).css('box-shadow', '0 0 0 0.2rem rgba(0, 123, 255, 0.25)');
    }).on('blur', function() {
        $(this).css('border-color', '#ced4da');
        $(this).css('box-shadow', 'none');
    });

    // Initialize tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
