<?php

namespace App\Controller;

use App\Entity\Instructor;
use App\Form\InstructorType;
use App\Repository\InstructorRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/instructors')]
#[IsGranted('ROLE_ADMIN')]
class AdminInstructorController extends AbstractController
{
    #[Route('/', name: 'admin_instructor_index', methods: ['GET'])]
    public function index(InstructorRepository $instructorRepository, Request $request): Response
    {
        $search = $request->query->get('search', '');
        
        if ($search) {
            $instructors = $instructorRepository->findBySearch($search);
        } else {
            $instructors = $instructorRepository->findAllOrdered();
        }

        return $this->render('admin/instructor/index.html.twig', [
            'instructors' => $instructors,
            'search' => $search,
            'stats' => $instructorRepository->getInstructorStats(),
        ]);
    }

    #[Route('/new', name: 'admin_instructor_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager, InstructorRepository $instructorRepository): Response
    {
        $instructor = new Instructor();
        $instructor->setDisplayOrder($instructorRepository->getNextDisplayOrder());
        $instructor->setIsActive(true); // New instructors are active by default

        $form = $this->createForm(InstructorType::class, $instructor);

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('instructor_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid security token. Please try again.');
                return $this->render('admin/instructor/new.html.twig', [
                    'instructor' => $instructor,
                    'form' => $form,
                ]);
            }

            // Handle manual form submission
            $instructor->setName($request->request->get('name'));
            $instructor->setEmail($request->request->get('email'));
            $instructor->setBio($request->request->get('bio'));
            $instructor->setSpecialization($request->request->get('specialization'));
            $instructor->setPhone($request->request->get('phone'));
            $instructor->setLinkedinUrl($request->request->get('linkedinUrl'));
            $instructor->setDisplayOrder($request->request->getInt('displayOrder'));

            // Handle qualifications
            $qualifications = $request->request->all('qualifications');
            $qualifications = array_filter($qualifications, fn($qualification) => !empty(trim($qualification)));
            $instructor->setQualifications($qualifications);

            // Handle achievements
            $achievements = $request->request->all('achievements');
            $achievements = array_filter($achievements, fn($achievement) => !empty(trim($achievement)));
            $instructor->setAchievements($achievements);

            // Handle profile image upload
            $profileImageFile = $request->files->get('profileImageFile');
            if ($profileImageFile) {
                $instructor->setProfileImageFile($profileImageFile);
            }

            try {
                $entityManager->persist($instructor);
                $entityManager->flush();

                return $this->redirectToRoute('admin_instructor_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating instructor: ' . $e->getMessage());
            }
        }

        return $this->render('admin/instructor/new.html.twig', [
            'instructor' => $instructor,
            'form' => $form,
        ]);
    }

    #[Route('/{emailPrefix}', name: 'admin_instructor_show', methods: ['GET'])]
    public function show(string $emailPrefix, InstructorRepository $instructorRepository): Response
    {
        $instructor = $instructorRepository->findByEmailPrefix($emailPrefix);

        if (!$instructor) {
            throw $this->createNotFoundException('Instructor not found');
        }

        return $this->render('admin/instructor/show.html.twig', [
            'instructor' => $instructor,
        ]);
    }

    #[Route('/{emailPrefix}/edit', name: 'admin_instructor_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, string $emailPrefix, InstructorRepository $instructorRepository, EntityManagerInterface $entityManager): Response
    {
        $instructor = $instructorRepository->findByEmailPrefix($emailPrefix);

        if (!$instructor) {
            throw $this->createNotFoundException('Instructor not found');
        }

        if ($request->isMethod('POST')) {
            // Handle manual form submission to match create page exactly
            $instructor->setName($request->request->get('name'));
            $instructor->setEmail($request->request->get('email'));
            $instructor->setBio($request->request->get('bio'));
            $instructor->setSpecialization($request->request->get('specialization'));
            $instructor->setPhone($request->request->get('phone'));
            $instructor->setLinkedinUrl($request->request->get('linkedinUrl'));
            $instructor->setDisplayOrder($request->request->getInt('displayOrder'));

            // Handle qualifications
            $qualifications = $request->request->all('qualifications');
            $qualifications = array_filter($qualifications, fn($qualification) => !empty(trim($qualification)));
            $instructor->setQualifications($qualifications);

            // Handle achievements
            $achievements = $request->request->all('achievements');
            $achievements = array_filter($achievements, fn($achievement) => !empty(trim($achievement)));
            $instructor->setAchievements($achievements);

            // Handle profile image upload
            $profileImageFile = $request->files->get('profileImageFile');
            if ($profileImageFile) {
                $instructor->setProfileImageFile($profileImageFile);
            }

            try {
                $entityManager->flush();

                return $this->redirectToRoute('admin_instructor_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating instructor: ' . $e->getMessage());
            }
        }

        return $this->render('admin/instructor/edit.html.twig', [
            'instructor' => $instructor,
        ]);
    }



    #[Route('/{id}/delete', name: 'admin_instructor_delete', methods: ['POST'])]
    public function delete(Request $request, Instructor $instructor, EntityManagerInterface $entityManager): Response
    {
        $instructorName = $instructor->getName();

        // Handle AJAX requests without CSRF for standardized modal system
        if ($request->isXmlHttpRequest()) {
            try {
                $entityManager->remove($instructor);
                $entityManager->flush();

                return $this->json(['success' => true, 'message' => "Instructor '{$instructorName}' deleted successfully!"]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'Cannot delete instructor: ' . $e->getMessage()
                ], 400);
            }
        }

        // Handle regular form submissions with CSRF
        if ($this->isCsrfTokenValid('delete'.$instructor->getId(), $request->request->get('_token'))) {
            try {
                $entityManager->remove($instructor);
                $entityManager->flush();
                $this->addFlash('success', 'Instructor deleted successfully.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Cannot delete instructor: ' . $e->getMessage());
            }
        } else {
            $this->addFlash('error', 'Invalid security token.');
        }

        return $this->redirectToRoute('admin_instructor_index');
    }

    #[Route('/{id}/toggle-status', name: 'admin_instructor_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, Instructor $instructor, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('toggle'.$instructor->getId(), $request->request->get('_token'))) {
            $instructor->setIsActive(!$instructor->isActive());
            $entityManager->flush();

            $status = $instructor->isActive() ? 'activated' : 'deactivated';
            $message = "Instructor {$status} successfully.";

            // Handle AJAX requests
            if ($request->isXmlHttpRequest()) {
                return $this->json([
                    'success' => true,
                    'message' => $message,
                    'newStatus' => $instructor->isActive() ? 'active' : 'inactive'
                ]);
            }

            $this->addFlash('success', $message);
        } else {
            $errorMessage = 'Invalid security token.';

            // Handle AJAX requests
            if ($request->isXmlHttpRequest()) {
                return $this->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 400);
            }

            $this->addFlash('error', $errorMessage);
        }

        return $this->redirectToRoute('admin_instructor_index');
    }

    #[Route('/reorder', name: 'admin_instructor_reorder', methods: ['POST'])]
    public function reorder(Request $request, InstructorRepository $instructorRepository): Response
    {
        $data = json_decode($request->getContent(), true);
        
        if (isset($data['orders']) && is_array($data['orders'])) {
            $instructorRepository->updateDisplayOrders($data['orders']);
            return $this->json(['success' => true, 'message' => 'Order updated successfully']);
        }

        return $this->json(['success' => false, 'message' => 'Invalid data'], 400);
    }

    #[Route('/{id}/print', name: 'admin_instructor_print', methods: ['GET'])]
    public function print(Instructor $instructor): Response
    {
        return $this->render('admin/instructor/print.html.twig', [
            'instructor' => $instructor,
            'print_date' => new \DateTime(),
        ]);
    }
}
