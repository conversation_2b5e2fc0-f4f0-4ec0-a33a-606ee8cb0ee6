{% extends 'base.html.twig' %}

{% block title %}Professional Trading Courses - Capitol Academy{% endblock %}

{% block meta_description %}Master financial markets with Capitol Academy's comprehensive trading courses. Expert-led programs covering technical analysis, risk management, and professional trading strategies.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
    --ca-green: #99b75a;
    --ca-blue: #00233e;
}

/* Modern Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--ca-primary) 0%, var(--ca-blue) 100%);
    padding: 100px 0 80px;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-family: 'Montserrat', sans-serif;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    font-family: 'Calibri', sans-serif;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.course-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ca-accent);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Main Content */
.main-content {
    background: var(--ca-white);
    padding: 60px 0;
    min-height: 80vh;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    color: var(--ca-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.section-subtitle {
    color: var(--ca-medium-gray);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Course Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Course Card */
.course-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.course-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.course-card:hover .course-image {
    transform: scale(1.05);
}

.course-placeholder {
    width: 100%;
    height: 220px;
    background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3a52 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.course-placeholder i {
    margin-bottom: 1rem;
    opacity: 0.8;
}

.course-placeholder span {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 1px;
}

.course-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Course Info Rows */
.course-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* Modern Course Card Styles */
.course-title {
    color: var(--ca-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.3;
    font-family: 'Montserrat', sans-serif;
}

.course-code {
    background: linear-gradient(135deg, var(--ca-primary) 0%, var(--ca-blue) 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
    margin-bottom: 15px;
    display: inline-block;
}

.course-category {
    color: var(--ca-medium-gray);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 15px;
}

.course-description {
    color: var(--ca-medium-gray);
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 1rem;
    font-family: 'Calibri', sans-serif;
}

.course-level {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.level-beginner {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.level-intermediate {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.level-advanced {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.course-info {
    color: var(--ca-dark-gray);
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.course-info i {
    color: var(--ca-primary);
}

.course-price {
    font-size: 1.2rem;
    font-weight: 700;
}

.price-paid {
    color: var(--ca-accent);
}

.price-free {
    color: var(--ca-primary);
}

.course-description {
    color: var(--ca-dark-gray);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 1rem 0;
    flex-grow: 1;
}

.course-footer {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.learn-more-btn {
    background: var(--ca-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    border: 2px solid var(--ca-primary);
}

.learn-more-btn:hover {
    background: transparent;
    color: var(--ca-primary);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(1, 26, 45, 0.3);
}

/* Course Actions */
.course-actions {
    margin-top: auto;
    padding-top: 25px;
    border-top: 2px solid #f8f9fa;
}

.btn-course-contact {
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(169, 4, 24, 0.3);
    width: 100%;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-course-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(169, 4, 24, 0.4);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }

    .course-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .courses-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .course-content {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 40px 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .main-content {
        padding: 40px 0;
    }

    .course-content {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block body %}
<!-- Modern Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Professional Trading Courses</h1>
            <p class="hero-subtitle">
                Master the financial markets with our comprehensive course catalog. Expert-led programs designed to take you from beginner to professional trader.
            </p>
            <div class="course-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ courses|length > 0 ? courses|length : '12+' }}</span>
                    <span class="stat-label">Expert Courses</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5000+</span>
                    <span class="stat-label">Students Trained</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">95%</span>
                    <span class="stat-label">Positive Reviews</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Courses Section -->
<section class="courses-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Available Courses</h2>
            <p class="section-description">
                Choose from our comprehensive selection of professional trading courses designed to advance your financial market expertise and accelerate your trading success.
            </p>
        </div>

        <div class="courses-grid">
            {% if courses and courses|length > 0 %}
                {% for course in courses %}
                <div class="course-card">
                    <!-- Course Image -->
                    {% if course.thumbnailImage %}
                        <img src="{{ asset(course.thumbnailImage) }}" alt="{{ course.title }}" class="course-image">
                    {% elseif course.bannerImage %}
                        <img src="{{ asset(course.bannerImage) }}" alt="{{ course.title }}" class="course-image">
                    {% else %}
                        <div class="course-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <span>{{ course.code }}</span>
                        </div>
                    {% endif %}

                    <!-- Course Content -->
                    <div class="course-content">
                        <!-- Title and Code Row -->
                        <div class="course-row course-title-row">
                            <h3 class="course-title">{{ course.title }}</h3>
                            <span class="course-code">{{ course.code }}</span>
                        </div>

                        <!-- Category and Level Row -->
                        <div class="course-row">
                            <span class="course-category">{{ course.category|default('Trading') }}</span>
                            {% if course.level %}
                            <span class="course-level level-{{ course.level|lower }}">{{ course.level }}</span>
                            {% endif %}
                        </div>

                        <!-- Course Info Row -->
                        <div class="course-row">
                            <div class="course-info">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Course Available</span>
                            </div>
                        </div>

                        <!-- Course Description -->
                        {% if course.description %}
                        <p class="course-description">
                            {{ course.description|length > 150 ? course.description|slice(0, 150) ~ '...' : course.description }}
                        </p>
                        {% endif %}

                        <!-- Course Actions -->
                        <div class="course-actions">
                            <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-course-contact">
                                <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                            </a>
                            <a href="{{ path('app_course_show', {code: course.code}) }}" class="btn-course-view">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Default Course Cards when no courses in database -->
                {% set defaultCourses = [
                    {
                        'code': 'FMA',
                        'title': 'Global Financial Markets',
                        'description': 'Learn about different financial markets, their instruments, and how to choose the right market for your trading needs.',
                        'category': 'Markets',
                        'level': 'Beginner',
                        'price': null,
                        'icon': 'fas fa-globe',
                        'route': 'app_course_fma'
                    },
                    {
                        'code': 'TEC',
                        'title': 'Technical Analysis',
                        'description': 'Master chart patterns, indicators, and technical analysis tools to make informed trading decisions.',
                        'category': 'Analysis',
                        'level': 'Intermediate',
                        'price': 299,
                        'icon': 'fas fa-chart-bar',
                        'route': 'app_course_tec'
                    },
                    {
                        'code': 'TRS',
                        'title': 'Trading Strategies',
                        'description': 'Learn professional trading strategies and how to implement them effectively in different market conditions.',
                        'category': 'Strategy',
                        'level': 'Advanced',
                        'price': 499,
                        'icon': 'fas fa-chess',
                        'route': 'app_course_trs'
                    },
                    {
                        'code': 'FUN',
                        'title': 'Fundamental Analysis',
                        'description': 'Understand economic indicators, news events, and fundamental factors that drive market movements.',
                        'category': 'Analysis',
                        'level': 'Intermediate',
                        'price': 199,
                        'icon': 'fas fa-newspaper',
                        'route': 'app_course_fun'
                    },
                    {
                        'code': 'SSA',
                        'title': 'Psychological Analysis',
                        'description': 'Master the psychological aspects of trading and develop the mental discipline required for success.',
                        'category': 'Psychology',
                        'level': 'Advanced',
                        'price': null,
                        'icon': 'fas fa-brain',
                        'route': 'app_course_ssa'
                    },
                    {
                        'code': 'MMA',
                        'title': 'Capital Management',
                        'description': 'Learn essential money management techniques and position sizing strategies for long-term success.',
                        'category': 'Management',
                        'level': 'Intermediate',
                        'price': 149,
                        'icon': 'fas fa-wallet',
                        'route': 'app_course_mma'
                    }
                ] %}

                {% for course in defaultCourses %}
                <div class="course-card">
                    <!-- Course Image -->
                    <div class="course-placeholder">
                        <i class="{{ course.icon }}"></i>
                        <span>{{ course.code }}</span>
                    </div>

                    <!-- Course Content -->
                    <div class="course-content">
                        <!-- Title and Code Row -->
                        <div class="course-row course-title-row">
                            <h3 class="course-title">{{ course.title }}</h3>
                            <span class="course-code">{{ course.code }}</span>
                        </div>

                        <!-- Category and Level Row -->
                        <div class="course-row">
                            <span class="course-category">{{ course.category }}</span>
                            <span class="course-level level-{{ course.level|lower }}">{{ course.level }}</span>
                        </div>

                        <!-- Course Info Row -->
                        <div class="course-row">
                            <div class="course-info">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Course Available</span>
                            </div>

                            {% if course.price %}
                            <div class="course-price price-paid">${{ course.price|number_format(2) }}</div>
                            {% else %}
                            <div class="course-price price-free">Free</div>
                            {% endif %}
                        </div>

                        <!-- Course Description -->
                        <p class="course-description">{{ course.description }}</p>

                        <!-- Course Actions -->
                        <div class="course-actions">
                            <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-course-contact">
                                <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                            </a>
                            <a href="{{ path(course.route) }}" class="btn-course-view">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</section>

{% endblock %}

