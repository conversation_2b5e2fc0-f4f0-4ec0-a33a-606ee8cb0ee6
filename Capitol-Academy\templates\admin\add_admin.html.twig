{% extends 'admin/base.html.twig' %}

{% block title %}Add Admin - Capitol Academy Admin{% endblock %}

{% block page_title %}Add New Admin{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Add Admin</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create New Admin Account
                </h3>
            </div>
            
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form_label(form.firstName, null, {'label_attr': {'class': 'form-label'}}) }}
                            {{ form_widget(form.firstName, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.firstName) }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form_label(form.lastName, null, {'label_attr': {'class': 'form-label'}}) }}
                            {{ form_widget(form.lastName, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.lastName) }}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form_label(form.email, null, {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.email, {'attr': {'class': 'form-control'}}) }}
                    {{ form_errors(form.email) }}
                    <small class="form-text text-muted">
                        This email will be used for admin login and must be unique across all accounts.
                    </small>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form_label(form.plainPassword.first, null, {'label_attr': {'class': 'form-label'}}) }}
                            {{ form_widget(form.plainPassword.first, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.plainPassword.first) }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form_label(form.plainPassword.second, null, {'label_attr': {'class': 'form-label'}}) }}
                            {{ form_widget(form.plainPassword.second, {'attr': {'class': 'form-control'}}) }}
                            {{ form_errors(form.plainPassword.second) }}
                        </div>
                    </div>
                </div>
                
                {{ form_errors(form.plainPassword) }}
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle mr-2"></i>
                    <strong>Admin Account Information:</strong>
                    <ul class="mb-0 mt-2">
                        <li>The new admin will have full access to the admin panel</li>
                        <li>They can manage users, courses, and contacts</li>
                        <li>The password must be at least 6 characters long</li>
                        <li>The email address must be unique across all accounts</li>
                    </ul>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="row">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus mr-2"></i>
                            Create Admin Account
                        </button>
                    </div>
                    <div class="col-md-6 text-right">
                        <a href="{{ path('admin_dashboard') }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
            {{ form_end(form) }}
        </div>
        
        <!-- Admin Management Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-lightbulb mr-2"></i>
                    Admin Management Tips
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-primary mr-2"></i>Security Best Practices</h6>
                        <ul class="small">
                            <li>Use strong, unique passwords for admin accounts</li>
                            <li>Regularly review admin access and permissions</li>
                            <li>Monitor admin login activity</li>
                            <li>Disable inactive admin accounts</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-users text-success mr-2"></i>Admin Responsibilities</h6>
                        <ul class="small">
                            <li>Manage user accounts and registrations</li>
                            <li>Oversee course content and availability</li>
                            <li>Handle contact form submissions</li>
                            <li>Monitor system performance and security</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Form validation
    $('form.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });
    
    // Password strength indicator
    $('#admin_form_plainPassword_first').on('input', function() {
        const password = $(this).val();
        const strength = getPasswordStrength(password);
        updatePasswordStrengthIndicator(strength);
    });
    
    function getPasswordStrength(password) {
        let strength = 0;
        if (password.length >= 6) strength++;
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    }
    
    function updatePasswordStrengthIndicator(strength) {
        const indicator = $('#password-strength');
        if (indicator.length === 0) {
            $('#admin_form_plainPassword_first').after('<div id="password-strength" class="mt-1"></div>');
        }
        
        const colors = ['danger', 'danger', 'warning', 'info', 'success', 'success'];
        const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong', 'Very Strong'];
        
        $('#password-strength').html(
            '<small class="text-' + colors[strength] + '">Password Strength: ' + texts[strength] + '</small>'
        );
    }
});
</script>
{% endblock %}
