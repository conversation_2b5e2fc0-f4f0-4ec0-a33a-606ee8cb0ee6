/**
 * AdminTableUtils - Standardized utilities for Capitol Academy admin tables
 * Provides consistent search, modal, and table functionality across all admin pages
 */
class AdminTableUtils {
    /**
     * Initialize search functionality with consistent behavior
     * @param {string} searchInputSelector - CSS selector for search input
     * @param {string} rowSelector - CSS selector for table rows
     * @param {Array} searchFields - Array of CSS selectors for searchable fields
     */
    static initializeSearch(searchInputSelector, rowSelector, searchFields) {
        const searchInput = document.querySelector(searchInputSelector);
        const searchClearBtn = document.querySelector('#search-clear-btn');
        const resultsCount = document.querySelector('#search-results-count');
        
        if (!searchInput) return;
        
        let searchTimeout;
        
        // Search input handler with 300ms delay
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchTerm = this.value.toLowerCase().trim();
            
            searchTimeout = setTimeout(() => {
                AdminTableUtils.performSearch(searchTerm, rowSelector, searchFields, resultsCount);
            }, 300);
        });
        
        // Clear button functionality
        if (searchClearBtn) {
            searchClearBtn.addEventListener('click', function() {
                if (searchInput.value) {
                    searchInput.value = '';
                    AdminTableUtils.performSearch('', rowSelector, searchFields, resultsCount);
                    searchInput.focus();
                }
            });
        }
        
        // Search input focus effects
        searchInput.addEventListener('focus', function() {
            this.style.borderColor = '#1e3c72';
            this.style.boxShadow = '0 0 0 0.2rem rgba(30, 60, 114, 0.25)';
            this.style.transform = 'scale(1.02)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.style.transform = 'scale(1)';
        });
    }
    
    /**
     * Perform search across table rows
     * @param {string} searchTerm - Search term
     * @param {string} rowSelector - CSS selector for table rows
     * @param {Array} searchFields - Array of CSS selectors for searchable fields
     * @param {Element} resultsCount - Results count element
     */
    static performSearch(searchTerm, rowSelector, searchFields, resultsCount) {
        const rows = document.querySelectorAll(rowSelector);
        let visibleCount = 0;
        let totalCount = rows.length;
        
        rows.forEach(row => {
            let isMatch = false;
            
            if (searchTerm === '') {
                isMatch = true;
            } else {
                // Search across specified fields
                searchFields.forEach(fieldSelector => {
                    const field = row.querySelector(fieldSelector);
                    if (field && field.textContent.toLowerCase().includes(searchTerm)) {
                        isMatch = true;
                    }
                });
            }
            
            if (isMatch) {
                row.style.display = '';
                row.classList.add('search-match');
                visibleCount++;
            } else {
                row.style.display = 'none';
                row.classList.remove('search-match');
            }
        });
        
        // Update search UI
        AdminTableUtils.updateSearchUI(searchTerm, visibleCount, totalCount, resultsCount);
        
        // Handle no results
        AdminTableUtils.handleNoResults(searchTerm, visibleCount, rowSelector);
    }
    
    /**
     * Update search UI elements
     */
    static updateSearchUI(searchTerm, visibleCount, totalCount, resultsCount) {
        const searchClearBtn = document.querySelector('#search-clear-btn');
        
        if (searchTerm !== '') {
            // Update clear button
            if (searchClearBtn) {
                searchClearBtn.innerHTML = '<i class="fas fa-times"></i>';
                searchClearBtn.title = 'Clear search';
            }
        } else {
            // Reset search button
            if (searchClearBtn) {
                searchClearBtn.innerHTML = '<i class="fas fa-search"></i>';
                searchClearBtn.title = 'Search';
            }
            
            // Hide results count
            if (resultsCount) {
                resultsCount.style.display = 'none';
            }
        }
    }
    
    /**
     * Handle no search results
     */
    static handleNoResults(searchTerm, visibleCount, rowSelector) {
        const existingNoResults = document.getElementById('no-results-message');
        
        if (visibleCount === 0 && searchTerm !== '') {
            if (!existingNoResults) {
                const firstRow = document.querySelector(rowSelector);
                if (firstRow) {
                    const table = firstRow.closest('table');
                    const tbody = table.querySelector('tbody');
                    const colCount = table.querySelectorAll('thead th').length;
                    
                    const noResultsRow = document.createElement('tr');
                    noResultsRow.id = 'no-results-message';
                    noResultsRow.innerHTML = `
                        <td colspan="${colCount}" class="text-center py-5">
                            <div>
                                <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                                <h5>No results found</h5>
                                <p class="text-muted">No items match your search criteria for "${searchTerm}"</p>
                                <button class="btn btn-outline-primary" onclick="document.querySelector('#professional-search').value = ''; document.querySelector('#professional-search').dispatchEvent(new Event('input'));">
                                    <i class="fas fa-times me-2"></i>Clear Search
                                </button>
                            </div>
                        </td>
                    `;
                    tbody.appendChild(noResultsRow);
                }
            }
        } else {
            if (existingNoResults) {
                existingNoResults.remove();
            }
        }
    }
    
    /**
     * Show standardized status toggle modal
     * @param {string} itemName - Name of the item being toggled
     * @param {boolean} currentStatus - Current status (true = active)
     * @param {Function} onConfirm - Callback function when confirmed
     */
    static showStatusModal(itemName, currentStatus, onConfirm) {
        const action = currentStatus ? 'deactivate' : 'activate';
        const actionColor = currentStatus ? 'warning' : 'success';
        const actionIcon = currentStatus ? 'pause' : 'play';
        
        const modalHtml = `
            <div class="modal fade admin-modal" id="statusToggleModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                        <div class="modal-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 1rem;">
                            <h6 class="modal-title" style="font-weight: 600;">
                                <i class="fas fa-${actionIcon} me-2"></i>Confirm ${action.charAt(0).toUpperCase() + action.slice(1)}
                            </h6>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" style="padding: 1rem; text-align: center;">
                            <p class="mb-3" style="color: #011a2d;">Are you sure you want to ${action} "${itemName}"?</p>
                            <small class="text-muted">This will ${currentStatus ? 'disable' : 'enable'} this item.</small>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-sm" id="confirmStatusBtn" style="background: #011a2d; color: white; border: none;">
                                <i class="fas fa-${actionIcon} me-1"></i>${action.charAt(0).toUpperCase() + action.slice(1)}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        AdminTableUtils.showModal(modalHtml, 'statusToggleModal', onConfirm);
    }
    
    /**
     * Show standardized delete confirmation modal
     * @param {string} itemName - Name of the item being deleted
     * @param {string} description - Additional description
     * @param {Function} onConfirm - Callback function when confirmed
     */
    static showDeleteModal(itemName, description, onConfirm) {
        const modalHtml = `
            <div class="modal fade admin-modal" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                        <div class="modal-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 1rem;">
                            <h6 class="modal-title" style="font-weight: 600;">
                                <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                            </h6>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" style="padding: 1rem; text-align: center;">
                            <p class="mb-3" style="color: #011a2d;">Are you sure you want to delete "${itemName}"?</p>
                            <small class="text-muted">${description}</small>
                        </div>
                        <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                            <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-sm" id="confirmDeleteBtn" style="background: #a90418; color: white; border: none;">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        AdminTableUtils.showModal(modalHtml, 'deleteConfirmModal', onConfirm);
    }
    
    /**
     * Generic modal display function
     */
    static showModal(modalHtml, modalId, onConfirm) {
        // Remove existing modal
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        // Handle confirm button with proper event handling
        const confirmBtn = document.getElementById(modalId === 'deleteConfirmModal' ? 'confirmDeleteBtn' : 'confirmStatusBtn');
        if (confirmBtn && onConfirm) {
            // Remove any existing event listeners
            confirmBtn.replaceWith(confirmBtn.cloneNode(true));
            const newConfirmBtn = document.getElementById(modalId === 'deleteConfirmModal' ? 'confirmDeleteBtn' : 'confirmStatusBtn');

            newConfirmBtn.addEventListener('click', function(e) {
                e.preventDefault();
                modal.hide();
                // Execute callback after modal is hidden
                setTimeout(() => {
                    try {
                        onConfirm();
                    } catch (error) {
                        console.error('Error executing modal callback:', error);
                    }
                }, 100);
            });
        }

        // Clean up on hide
        modalElement.addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    /**
     * Form validation utilities
     */
    static validateForm(formSelector) {
        const form = document.querySelector(formSelector);
        if (!form) return false;

        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                AdminTableUtils.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                AdminTableUtils.clearFieldError(field);
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !AdminTableUtils.isValidEmail(field.value)) {
                AdminTableUtils.showFieldError(field, 'Please enter a valid email address');
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * Show field error
     */
    static showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');

        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    /**
     * Clear field error
     */
    static clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');

        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Email validation
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Show form notification
     */
    static showFormNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of content area
        const contentArea = document.querySelector('.content') || document.querySelector('main') || document.body;
        contentArea.insertBefore(notification, contentArea.firstChild);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
}

// Make AdminTableUtils globally available
window.AdminTableUtils = AdminTableUtils;
