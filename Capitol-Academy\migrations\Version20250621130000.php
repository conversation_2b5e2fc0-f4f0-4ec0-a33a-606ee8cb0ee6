<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250621130000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update User entity to use Country relationship instead of string';
    }

    public function up(Schema $schema): void
    {
        // Add country_id column to user table
        $this->addSql('ALTER TABLE user ADD country_id INT DEFAULT NULL');
        
        // Add foreign key constraint
        $this->addSql('ALTER TABLE user ADD CONSTRAINT FK_8D93D649F92F3E70 FOREIGN KEY (country_id) REFERENCES countries (id)');
        
        // Create index on country_id
        $this->addSql('CREATE INDEX IDX_8D93D649F92F3E70 ON user (country_id)');
        
        // Migrate existing country data
        $this->addSql("UPDATE user u 
                       JOIN countries c ON c.country_name = u.country 
                       SET u.country_id = c.id 
                       WHERE u.country IS NOT NULL");
        
        // Drop the old country column
        $this->addSql('ALTER TABLE user DROP country');
    }

    public function down(Schema $schema): void
    {
        // Add back the country column
        $this->addSql('ALTER TABLE user ADD country VARCHAR(100) DEFAULT NULL');
        
        // Migrate data back
        $this->addSql("UPDATE user u 
                       JOIN countries c ON c.id = u.country_id 
                       SET u.country = c.country_name 
                       WHERE u.country_id IS NOT NULL");
        
        // Drop foreign key and index
        $this->addSql('ALTER TABLE user DROP FOREIGN KEY FK_8D93D649F92F3E70');
        $this->addSql('DROP INDEX IDX_8D93D649F92F3E70 ON user');
        
        // Drop country_id column
        $this->addSql('ALTER TABLE user DROP country_id');
    }
}
