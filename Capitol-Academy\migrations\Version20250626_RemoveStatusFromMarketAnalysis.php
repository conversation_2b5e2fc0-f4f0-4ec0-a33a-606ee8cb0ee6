<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove status column from market_analysis table
 */
final class Version20250626_RemoveStatusFromMarketAnalysis extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove status column from market_analysis table';
    }

    public function up(Schema $schema): void
    {
        // Drop the status column
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN status');
    }

    public function down(Schema $schema): void
    {
        // Add the status column back
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT "published"');
    }
}
