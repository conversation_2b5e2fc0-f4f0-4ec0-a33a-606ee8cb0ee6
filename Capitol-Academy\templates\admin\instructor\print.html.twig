<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instructor Details - {{ instructor.name }} - Capitol Academy</title>
    
    <!-- Bootstrap 5 for print styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12pt; }
            .page-break { page-break-before: always; }
        }
        
        :root {
            --primary-navy: #011a2d;
            --accent-red: #a90418;
            --light-gray: #f8f9fa;
            --text-dark: #343a40;
            --text-muted: #6c757d;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            color: var(--text-dark);
            background: white;
        }
        
        .print-header {
            border-bottom: 3px solid var(--primary-navy);
            margin-bottom: 2rem;
            padding-bottom: 1rem;
        }
        
        .print-logo {
            max-height: 60px;
            width: auto;
        }
        
        .instructor-photo {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid var(--light-gray);
        }
        
        .section-title {
            color: var(--primary-navy);
            border-bottom: 2px solid var(--accent-red);
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--primary-navy);
        }
        
        .print-footer {
            border-top: 1px solid var(--light-gray);
            margin-top: 2rem;
            padding-top: 1rem;
            font-size: 0.9rem;
            color: var(--text-muted);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Header -->
        <div class="print-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-1" style="color: var(--primary-navy);">Capitol Academy</h1>
                    <p class="mb-0 text-muted">Instructor Details Report</p>
                </div>
                <div class="col-md-4 text-end">
                    <img src="{{ asset('images/logos/logo-horizontal.png') }}" alt="Capitol Academy" class="print-logo">
                </div>
            </div>
        </div>

        <!-- Instructor Information -->
        <div class="row">
            <div class="col-md-8">
                <h2 class="section-title">Instructor Information</h2>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <span class="info-label">Full Name:</span>
                    </div>
                    <div class="col-sm-9">
                        {{ instructor.name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <span class="info-label">Email:</span>
                    </div>
                    <div class="col-sm-9">
                        {{ instructor.email }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <span class="info-label">Phone:</span>
                    </div>
                    <div class="col-sm-9">
                        {{ instructor.phone ?? 'Not provided' }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <span class="info-label">Status:</span>
                    </div>
                    <div class="col-sm-9">
                        <span class="status-badge {{ instructor.isActive ? 'status-active' : 'status-inactive' }}">
                            {{ instructor.isActive ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <span class="info-label">Joined Date:</span>
                    </div>
                    <div class="col-sm-9">
                        {{ instructor.createdAt|date('F d, Y') }}
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 text-center">
                {% if instructor.profilePicture %}
                    <img src="{{ asset('uploads/instructors/' ~ instructor.profilePicture) }}" 
                         alt="{{ instructor.name }}" class="instructor-photo">
                {% else %}
                    <img src="{{ asset('images/instructors/instructor-default-pp.png') }}" 
                         alt="Default Instructor" class="instructor-photo">
                {% endif %}
            </div>
        </div>

        <!-- Biography Section -->
        {% if instructor.biography %}
        <div class="row mt-4">
            <div class="col-12">
                <h3 class="section-title">Biography</h3>
                <div class="p-3" style="background-color: var(--light-gray); border-radius: 8px;">
                    {{ instructor.biography|nl2br }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Specializations Section -->
        {% if instructor.specializations %}
        <div class="row mt-4">
            <div class="col-12">
                <h3 class="section-title">Specializations</h3>
                <div class="p-3" style="background-color: var(--light-gray); border-radius: 8px;">
                    {{ instructor.specializations|nl2br }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Print Footer -->
        <div class="print-footer">
            <div class="row">
                <div class="col-md-6">
                    <strong>Capitol Academy</strong><br>
                    Professional Trading Education Platform
                </div>
                <div class="col-md-6 text-end">
                    Generated on: {{ "now"|date('F d, Y \\a\\t g:i A') }}<br>
                    Report ID: INS-{{ instructor.id }}-{{ "now"|date('Ymd') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Print Button (hidden when printing) -->
    <div class="no-print position-fixed bottom-0 end-0 m-3">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Print Report
        </button>
        <button onclick="window.close()" class="btn btn-secondary ms-2">
            <i class="fas fa-times"></i> Close
        </button>
    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
