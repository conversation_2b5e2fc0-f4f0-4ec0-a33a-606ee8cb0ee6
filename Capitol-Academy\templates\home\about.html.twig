{% extends 'base.html.twig' %}

{% block title %}About Us - Capitol Academy{% endblock %}

{% block meta_description %}Learn about Capitol Academy, a leading financial markets education institution offering exceptional training programs accessible worldwide.{% endblock %}

{% block body %}
<div class="container-fluid p-0">
    <!-- Section 1: Hero Section -->
    <section class="hero-section" style="background: url('{{ asset('images/backgrounds/Background About Capitol Academy .png') }}') center/cover; height: 70vh; padding: 4rem 0;">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-12 text-center">
                    <div class="hero-content" style="margin-left: 0; margin-right: 0;">
                        <h1 class="display-3 fw-bold" style="font-family: 'Montserrat', sans-serif; margin-top: 2rem; margin-bottom: 2rem;">
                            <span style="color: #00233e;">About Capitol Academy</span>
                        </h1>
                        <p class="lead" style="font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif; color: #45403f; line-height: 1.8; font-size: 1.5rem; margin-bottom: 2rem; max-width: 100%;">
                            CA is a leading academy that offers an exceptional education program in financial markets. name has been inspired by the Capitol building in Washington, which is at the origin of the city plan and reflects a combination of power and good performance.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Teaching Experience (Two-Column Layout) -->
    <section style="background-color: #011a2d; padding: 5rem 0;">
        <div class="container">
            <!-- Centered Title -->
            <div class="row">
                <div class="col-12 text-center" style="margin-bottom: 4rem;">
                    <h2 class="h1 fw-bold" style="color: white; font-family: 'Montserrat', sans-serif; margin-top: 2rem;">Over than 10 years of Teaching</h2>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6" style="margin-bottom: 2rem;">
                    <!-- Left Column: Image placeholder -->
                    <div class="position-relative text-center">
                        <img src="{{ asset('images/certificates/course.png') }}" alt="Teaching Experience"
                             class="img-fluid rounded-3 shadow-lg"
                             style="max-width: 70%; height: auto;"
                             onerror="this.src='{{ asset('images/placeholders/image-placeholder.png') }}'">
                    </div>
                </div>
                <div class="col-lg-6">
                    <!-- Right Column: Text -->
                    <div class="ps-lg-5" style="margin-left: 2rem;">
                        <p class="lead" style="color: white; line-height: 1.8; font-size: 1.2rem; font-family: 'Calibri', Arial, sans-serif;">
                            We offers you the opportunity to grow for personal and professional growth as a trader.
                            Our programs prepare beginner to seasoned traders to understand and master the different
                            trading tools ranging from Technical Analysis, Fundamental Analysis to Psychology of the Trader.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 3: Services Overview -->
    <section class="py-5" style="background: url('{{ asset('images/backgrounds/Background What we can Offer.png') }}') center/cover;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="h1 fw-bold mb-4" style="color: #011a2d; font-family: 'Montserrat', sans-serif;">What we can Offer for you</h2>
                    <p class="lead" style="color: #495057; line-height: 1.8; font-size: 1.2rem; font-family: 'Calibri', Arial, sans-serif;">
                        We offers you the opportunity to grow for personal and professional growth as a trader.
                        Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                    </p>
                </div>
            </div>

            <!-- Four Service Cards -->
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 h-100 shadow-lg service-card" style="transition: all 0.3s ease; border-radius: 15px;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-start mb-3"
                                 style="width: 80px; height: 80px;">
                                <img src="{{ asset('images/misc/analysis-icon.png') }}" alt="Analysis Icon"
                                     class="img-fluid" style="max-width: 60px; max-height: 60px;"
                                     onerror="this.src='{{ asset('images/placeholders/image-placeholder.png') }}'">
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #1e3c72; text-align: left; font-family: 'Montserrat', sans-serif;">Free Market Analysis</h5>
                            <p class="text-muted mb-4" style="font-size: 0.9rem; text-align: left; font-family: 'Calibri', Arial, sans-serif;">
                                We offers you the opportunity to grow for personal and professional growth as a trader.
                                Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                            </p>
                            <a href="#" class="btn btn-outline-primary">Learn More →</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 h-100 shadow-lg service-card" style="transition: all 0.3s ease; border-radius: 15px;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-start mb-3"
                                 style="width: 80px; height: 80px;">
                                <img src="{{ asset('images/misc/webinar-icon.png') }}" alt="Webinar Icon"
                                     class="img-fluid" style="max-width: 60px; max-height: 60px;"
                                     onerror="this.src='{{ asset('images/placeholders/image-placeholder.png') }}'">
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #1e3c72; text-align: left; font-family: 'Montserrat', sans-serif;">Webinar's Sessions</h5>
                            <p class="text-muted mb-4" style="font-size: 0.9rem; text-align: left; font-family: 'Calibri', Arial, sans-serif;">
                                We offers you the opportunity to grow for personal and professional growth as a trader.
                                Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                            </p>
                            <a href="#" class="btn btn-outline-primary">Start Now →</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 h-100 shadow-lg service-card" style="transition: all 0.3s ease; border-radius: 15px;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-start mb-3"
                                 style="width: 80px; height: 80px;">
                                <img src="{{ asset('images/misc/buy-sell-signals-icon.png') }}" alt="Buy/Sell Signals Icon"
                                     class="img-fluid" style="max-width: 60px; max-height: 60px;"
                                     onerror="this.src='{{ asset('images/placeholders/image-placeholder.png') }}'">
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #1e3c72; text-align: left; font-family: 'Montserrat', sans-serif;">Buy/Sell Signals and Analysis</h5>
                            <p class="text-muted mb-4" style="font-size: 0.9rem; text-align: left; font-family: 'Calibri', Arial, sans-serif;">
                                We offers you the opportunity to grow for personal and professional growth as a trader.
                                Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                            </p>
                            <a href="#" class="btn btn-outline-primary">Register →</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 h-100 shadow-lg service-card" style="transition: all 0.3s ease; border-radius: 15px;">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center justify-content-start mb-3"
                                 style="width: 80px; height: 80px;">
                                <img src="{{ asset('images/misc/partners-icon.png') }}" alt="Partners Icon"
                                     class="img-fluid" style="max-width: 60px; max-height: 60px;"
                                     onerror="this.src='{{ asset('images/placeholders/image-placeholder.png') }}'">
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #1e3c72; text-align: left; font-family: 'Montserrat', sans-serif;">Partnership Program</h5>
                            <p class="text-muted mb-4" style="font-size: 0.9rem; text-align: left; font-family: 'Calibri', Arial, sans-serif;">
                                We offers you the opportunity to grow for personal and professional growth as a trader.
                                Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                            </p>
                            <a href="{{ path('app_partnership') }}" class="btn btn-outline-primary">Find How →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Section 4: Contact Form (Two-Column Layout with Background Image) -->
    <section class="py-5 position-relative" style="background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-6">
                    <!-- Left Column: Title and Text -->
                    <div class="text-white pe-lg-5" style="margin-right: 3rem;">
                        <h2 class="h1 fw-bold mb-4" style="color: white; font-family: 'Montserrat', sans-serif;">Any Question ? Contact us</h2>
                        <p class="lead mb-4" style="font-size: 1.2rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class="col-lg-5 offset-lg-1">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class="card border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);">
                        <div class="card-body p-4">
                            <form action="{{ path('app_contact_unified') }}" method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="source_page" value="about">

                                <div class="mb-3">
                                    <input type="text" name="name" class="form-control glassmorphism-input"
                                           placeholder="Name ..." required
                                           style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide your name.</div>
                                </div>

                                <div class="mb-3">
                                    <input type="email" name="email" class="form-control glassmorphism-input"
                                           placeholder="Email ..." required
                                           style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    <textarea name="message" class="form-control glassmorphism-input" rows="4"
                                              placeholder="Message ..." required
                                              style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;"></textarea>
                                    <div class="invalid-feedback">Please provide your message.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" checked required>
                                        <label class="form-check-label" for="privacyConsent" style="font-size: 0.85rem; line-height: 1.4; color: #6c757d;">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn"
                                            style="background: #28a745; border: 2px solid white; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;"
                                            onmouseover="this.style.background='#218838'; this.style.transform='translateY(-2px)'"
                                            onmouseout="this.style.background='#28a745'; this.style.transform='translateY(0)'">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>

<style>
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .display-6 {
        font-size: 2rem;
    }
}
</style>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced About Us Page Styles */
.promotional-banner {
    border-bottom: 2px solid #e9ecef;
}

/* Service Card Hover Effects (similar to instructor cards) */
.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.2) !important;
}

/* Professional Section Spacing */
section {
    margin-bottom: 0 !important;
}

/* Ensure consistent text readability */
.hero-content h1 {
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.hero-content p {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Fix topbar alignment on About page - Remove any interfering styles */
.navbar {
    position: sticky !important;
    top: 0 !important;
    z-index: 1050 !important;
    width: 100% !important;
    background-color: #ffffff !important;
    border-bottom: 2px solid rgba(8, 28, 44, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 15px rgba(8, 28, 44, 0.1) !important;
}

.navbar .container {
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.navbar .search-container {
    position: relative !important;
    z-index: 1050 !important;
}

.navbar .search-container .form-control {
    transform: none !important;
    position: relative !important;
}

.navbar .search-btn-enhanced {
    transform: none !important;
    position: relative !important;
}

/* Ensure navbar brand alignment */
.navbar-brand {
    margin-left: 0 !important;
}

/* Fix any potential navbar collapse issues */
.navbar-collapse {
    flex-grow: 1 !important;
    align-items: center !important;
}

/* Ensure footer social media icons are circular on About page */
footer .footer-social-btn {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid #2c3e50 !important;
    color: #2c3e50 !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
}

footer .footer-social-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
    background: #2c3e50 !important;
    border-color: #2c3e50 !important;
    color: white !important;
}

.countdown-timer .time-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown-timer .separator {
    align-self: center;
    margin: 0 5px;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced Contact Form Styling */
.card .form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

.card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Service Cards Hover Effect */
.card:hover .rounded-circle {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* Background Image Overlay */
.position-relative::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    z-index: 1;
}

.position-relative > * {
    position: relative;
    z-index: 2;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .card-body {
        padding: 2rem !important;
    }

    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Adjust contact section height for mobile */
    section[style*="calc(100vh - 80px)"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}

/* Contact Form Specific Styles - More specific selectors */
.glassmorphism-input {
    color: white !important;
}

.glassmorphism-input:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none !important;
    color: white !important;
}

.glassmorphism-input::placeholder {
    color: rgba(255,255,255,0.7) !important;
    opacity: 1 !important;
}

section .card .form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

section .card .form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(2) { animation-delay: 0.1s; }
.card:nth-child(3) { animation-delay: 0.2s; }
.card:nth-child(4) { animation-delay: 0.3s; }
.card:nth-child(5) { animation-delay: 0.4s; }
</style>
{% endblock %}

{% block javascripts %}
<script>
// About page specific functionality - countdown timer removed to avoid conflicts

// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderBottomColor = '#1e3c72';
            this.style.boxShadow = '0 2px 0 0 #1e3c72';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderBottomColor = '#e9ecef';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*="message"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
