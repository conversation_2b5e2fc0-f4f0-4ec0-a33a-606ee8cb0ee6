<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Contact Form Submission - Capitol Academy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .footer {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            text-align: center;
            border-radius: 0 0 5px 5px;
            font-size: 12px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            margin-top: 5px;
            padding: 8px;
            background-color: white;
            border: 1px solid #ced4da;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Form Submission</h1>
        <p>Capitol Academy - Financial Markets Education</p>
    </div>
    
    <div class="content">
        <div class="field">
            <div class="field-label">Full Name:</div>
            <div class="field-value">{{ contact.fullName }}</div>
        </div>
        
        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">{{ contact.email }}</div>
        </div>
        
        {% if contact.phone %}
        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{ contact.phone }}</div>
        </div>
        {% endif %}
        
        {% if contact.country %}
        <div class="field">
            <div class="field-label">Country:</div>
            <div class="field-value">{{ contact.country }}</div>
        </div>
        {% endif %}
        
        {% if contact.subject %}
        <div class="field">
            <div class="field-label">Subject:</div>
            <div class="field-value">{{ contact.subject }}</div>
        </div>
        {% endif %}
        
        {% if contact.message %}
        <div class="field">
            <div class="field-label">Message:</div>
            <div class="field-value">{{ contact.message|nl2br }}</div>
        </div>
        {% endif %}
        
        <div class="field">
            <div class="field-label">Submitted:</div>
            <div class="field-value">{{ contact.createdAt|date('F j, Y \\a\\t g:i A') }}</div>
        </div>
    </div>
    
    <div class="footer">
        <p>This email was automatically generated by the Capitol Academy website contact form.</p>
        <p>Please respond to the customer at: {{ contact.email }}</p>
    </div>
</body>
</html>
