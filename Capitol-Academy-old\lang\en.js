formcheckLanguage = {
	required: "This field is required.",
	alpha: "This field accepts alphabetic characters only.",
	alphanum: "This field accepts alphanumeric characters only.",
	nodigit: "No digits are accepted.",
	digit: "Please enter a valid integer.",
	digitmin: "The number must be at least %0",
	digitltd: "The value must be between %0 and %1",
	number: "Please enter a valid number.",
	email: "Please enter a valid email: <br /><span>E.g. <EMAIL></span>",
	phone: "Please enter a valid phone.",
	url: "Please enter a valid url: <br /><span>E.g. http://www.domain.com</span>",
	
	confirm: "This field is different from %0",
	differs: "This value must be different of %0",
	length_str: "The length is incorrect, it must be between %0 and %1",
	length_fix: "The length is incorrect, it must be exactly %0 characters",
	lengthmax: "The length is incorrect, it must be at max %0",
	lengthmin: "The length is incorrect, it must be at least %0",
	checkbox: "Please check the box",
	radios: "Please select a radio",
	select: "Please choose a value"
}