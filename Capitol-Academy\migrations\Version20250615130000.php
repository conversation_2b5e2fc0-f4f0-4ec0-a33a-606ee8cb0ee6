<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add thumbnail_image, learning_outcomes, and features fields to course table
 */
final class Version20250615130000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add thumbnail_image, learning_outcomes, and features fields to course table for enhanced course management';
    }

    public function up(Schema $schema): void
    {
        // Add thumbnail_image field
        $this->addSql('ALTER TABLE course ADD thumbnail_image VARCHAR(255) DEFAULT NULL');
        
        // Add learning_outcomes field (JSON)
        $this->addSql('ALTER TABLE course ADD learning_outcomes JSON DEFAULT NULL');
        
        // Add features field (JSON)
        $this->addSql('ALTER TABLE course ADD features JSON DEFAULT NULL');
        
        // Set default empty arrays for existing courses
        $this->addSql('UPDATE course SET learning_outcomes = JSON_ARRAY() WHERE learning_outcomes IS NULL');
        $this->addSql('UPDATE course SET features = JSON_ARRAY() WHERE features IS NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove the added fields
        $this->addSql('ALTER TABLE course DROP thumbnail_image');
        $this->addSql('ALTER TABLE course DROP learning_outcomes');
        $this->addSql('ALTER TABLE course DROP features');
    }
}
