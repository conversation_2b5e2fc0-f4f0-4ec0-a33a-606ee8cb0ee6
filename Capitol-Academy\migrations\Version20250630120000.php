<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add views column to market_analysis table
 */
final class Version20250630120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add views column to market_analysis table to track article view counts';
    }

    public function up(Schema $schema): void
    {
        // Add views column with default value of 0
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN views INT NOT NULL DEFAULT 0');
        
        // Add index for performance if needed
        $this->addSql('CREATE INDEX idx_views ON market_analysis (views)');
    }

    public function down(Schema $schema): void
    {
        // Remove the views column and its index
        $this->addSql('DROP INDEX idx_views ON market_analysis');
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN views');
    }
}
