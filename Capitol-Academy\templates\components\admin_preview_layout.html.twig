{# 
    Standardized Admin Preview Layout Component
    Based on Course Preview Template Design
    
    Usage:
    {% embed 'components/admin_preview_layout.html.twig' with {
        'entity_name': 'User',
        'entity_title': user.fullName,
        'entity_code': user.id,
        'breadcrumb_items': [
            {'path': 'admin_dashboard', 'title': 'Home'},
            {'path': 'admin_users', 'title': 'Users'},
            {'title': user.fullName, 'active': true}
        ],
        'edit_path': path('admin_user_edit', {'id': user.id}),
        'back_path': path('admin_users'),
        'print_function': 'printUserDetails'
    } %}
        {% block preview_content %}
            <!-- Entity-specific content goes here -->
        {% endblock %}
    {% endembed %}
#}

{% extends 'admin/base.html.twig' %}

{% block title %}{{ entity_title }} - {{ entity_name }} Details - Capitol Academy Admin{% endblock %}

{% block page_title %}{{ entity_name }} Details{% endblock %}

{% block breadcrumbs %}
{% for item in breadcrumb_items %}
    {% if item.active is defined and item.active %}
        <li class="breadcrumb-item active">{{ item.title }}</li>
    {% else %}
        <li class="breadcrumb-item"><a href="{{ path(item.path) }}">{{ item.title }}</a></li>
    {% endif %}
{% endfor %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="{{ entity_icon|default('fas fa-file-alt') }} mr-3" style="font-size: 2rem;"></i>
                        {{ entity_name }} Details: {{ entity_code|default(entity_title) }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Button (Icon Only) -->
                        {% if edit_path is defined %}
                        <a href="{{ edit_path }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit {{ entity_name }}">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>
                        {% endif %}

                        <!-- Print Button (Icon Only) -->
                        <button type="button"
                                class="btn me-2 mb-2 mb-md-0"
                                style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                                onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                                onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                                onclick="{{ print_function|default('printDetails') }}()"
                                title="Print {{ entity_name }} Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </button>

                        <!-- Back Button -->
                        {% if back_path is defined %}
                        <a href="{{ back_path }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; text-decoration: none;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to {{ entity_name }}s
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body p-4" style="background: white; border-radius: 0 0 15px 15px;">
            {% block preview_content %}
                <!-- Entity-specific content will be inserted here -->
            {% endblock %}
        </div>
    </div>
</div>

<!-- Professional Print Document (Hidden on Screen) -->
<div class="print-document">
    {% block print_content %}
        <!-- Print Header -->
        <div class="print-header">
            <div class="print-logo">CAPITOL ACADEMY</div>
            <div class="print-subtitle">{{ entity_name }} Details Report</div>
            <div class="print-date">Generated on {{ 'now'|date('F j, Y \\a\\t g:i A') }}</div>
            <div class="print-confidential">Confidential Document - For Internal Use Only</div>
        </div>

        {% block print_body %}
            <!-- Entity-specific print content will be inserted here -->
        {% endblock %}

        <!-- Print Footer -->
        <div class="print-footer">
            <div class="print-footer-line">Capitol Academy - {{ entity_name }} Management System</div>
            <div class="print-footer-line">This document contains confidential information and is intended for authorized personnel only.</div>
            <div class="print-page-number"></div>
        </div>
    {% endblock %}
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: normal;
    color: #011a2d;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Professional Button Styling */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Standard Admin Button Styling */
.admin-btn-create {
    transition: all 0.3s ease !important;
}

.admin-btn-create:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(1, 26, 45, 0.2) !important;
}

/* Card Header Animation */
.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Professional Print Styles */
@media print {
    /* Reset and base styles */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        margin: 0;
        padding: 0;
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.6;
        color: #000;
        background: white;
    }

    /* Hide all web interface elements */
    .navbar, .sidebar, .breadcrumb, .btn, .modal, .alert, .dropdown,
    .card-header, .quick-actions, .technical-info, .back-button,
    nav, header, footer, .container-fluid, .card, .row, .col-md-8, .col-md-4 {
        display: none !important;
    }

    /* Show only print content */
    .print-document {
        display: block !important;
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    /* Professional header with Capitol Academy branding */
    .print-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 3px solid #1e3c72;
        page-break-inside: avoid;
    }

    .print-logo {
        font-size: 28pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 8px;
        letter-spacing: 2px;
    }

    .print-subtitle {
        font-size: 16pt;
        color: #2a5298;
        margin-bottom: 12px;
        font-style: italic;
    }

    .print-date {
        font-size: 11pt;
        color: #666;
        margin-bottom: 5px;
    }

    .print-confidential {
        font-size: 10pt;
        color: #dc3545;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Information sections */
    .print-section {
        margin-bottom: 30px;
        page-break-inside: avoid;
    }

    .print-section-title {
        font-size: 16pt;
        font-weight: bold;
        color: #1e3c72;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #2a5298;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .print-info-grid {
        display: table;
        width: 100%;
        border-collapse: collapse;
    }

    .print-info-row {
        display: table-row;
        border-bottom: 1px solid #eee;
    }

    .print-info-label {
        display: table-cell;
        font-weight: bold;
        color: #333;
        width: 180px;
        padding: 12px 15px 12px 0;
        vertical-align: top;
    }

    .print-info-value {
        display: table-cell;
        color: #000;
        padding: 12px 0;
        vertical-align: top;
    }

    /* Footer */
    .print-footer {
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 9pt;
        color: #666;
        border-top: 1px solid #ccc;
        padding-top: 15px;
        background: white;
    }

    .print-footer-line {
        margin-bottom: 5px;
    }

    /* Page numbering */
    .print-page-number:after {
        content: "Page " counter(page) " of " counter(pages);
    }

    /* Enhanced Page numbering and margins */
    @page {
        margin: 1in 0.75in;
        @top-center {
            content: "Capitol Academy - Details Report";
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
        @bottom-center {
            content: "Page " counter(page);
            font-size: 8pt;
            color: #7f8c8d;
            font-family: 'Georgia', serif;
        }
    }
}

/* Screen styles for print preview */
.print-document {
    display: none;
}
</style>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
function printDetails() {
    window.print();
}
</script>
{% endblock %}
