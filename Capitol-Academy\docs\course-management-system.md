# Capitol Academy Course Management System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Course Entity Structure](#course-entity-structure)
3. [Database Schema](#database-schema)
4. [Course Attributes](#course-attributes)
5. [Validation Rules](#validation-rules)
6. [Course Management Workflows](#course-management-workflows)
7. [Module Management](#module-management)
8. [Image Management](#image-management)
9. [Rating and Review System](#rating-and-review-system)
10. [API Endpoints](#api-endpoints)
11. [Troubleshooting Guide](#troubleshooting-guide)

## Overview

The Capitol Academy Course Management System is a comprehensive e-learning platform designed specifically for trading education. It provides full CRUD operations for courses, modules, ratings, and reviews with a professional AdminLTE-style interface.

### Key Features
- **Professional Course Creation**: Enhanced form fields with validation
- **Module Management**: Dynamic course modules with ordering
- **Image Management**: Thumbnail and banner image support
- **Rating System**: Student reviews and ratings
- **Enrollment Tracking**: Complete enrollment statistics
- **Code-based Routing**: Uses course codes instead of slugs

## Course Entity Structure

### Primary Course Entity (`App\Entity\Course`)

```php
class Course
{
    private ?int $id = null;
    private ?string $code = null;           // Unique course identifier
    private ?string $title = null;          // Course title
    private ?string $description = null;    // Detailed description
    private ?string $category = null;       // Course category
    private ?string $level = null;          // Difficulty level
    private ?int $duration = null;          // Duration in minutes
    private ?float $price = null;           // Price in USD
    private ?string $thumbnailImage = null; // Thumbnail image path
    private ?string $bannerImage = null;    // Banner image path
    private array $learningOutcomes = [];   // JSON array of outcomes
    private array $features = [];           // JSON array of features
    private bool $hasModules = false;       // Enable/disable modules
    private bool $isActive = true;          // Course status
    private int $enrolledCount = 0;         // Total enrollments
    private int $activeEnrollments = 0;     // Active students
    private int $completedCount = 0;        // Completed students
    private int $certifiedCount = 0;        // Certified students
    private float $averageRating = 0.00;    // Average rating (1-5)
    private int $totalReviews = 0;          // Total review count
    private int $viewCount = 0;             // Page view count
    private \DateTimeImmutable $createdAt;
    private \DateTimeImmutable $updatedAt;
}
```

## Database Schema

### Course Table (`course`)

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| `code` | VARCHAR(10) | UNIQUE, NOT NULL | Course code (e.g., FMA, CS101) |
| `title` | VARCHAR(255) | NOT NULL | Course title |
| `description` | TEXT | NULL | Detailed course description |
| `category` | VARCHAR(100) | NULL | Course category |
| `level` | VARCHAR(50) | NULL | Difficulty level |
| `duration` | INT | NULL | Duration in minutes |
| `price` | DECIMAL(10,2) | NOT NULL, DEFAULT 0.00 | Price in USD |
| `thumbnail_image` | VARCHAR(255) | NULL | Thumbnail image path |
| `banner_image` | VARCHAR(255) | NULL | Banner image path |
| `learning_outcomes` | JSON | NULL | Array of learning outcomes |
| `features` | JSON | NULL | Array of course features |
| `has_modules` | BOOLEAN | DEFAULT FALSE | Module enablement flag |
| `is_active` | BOOLEAN | DEFAULT TRUE | Course status |
| `enrolled_count` | INT | DEFAULT 0 | Total enrollment count |
| `active_enrollments` | INT | DEFAULT 0 | Active student count |
| `completed_count` | INT | DEFAULT 0 | Completion count |
| `certified_count` | INT | DEFAULT 0 | Certification count |
| `average_rating` | DECIMAL(3,2) | DEFAULT 0.00 | Average rating (1-5) |
| `total_reviews` | INT | DEFAULT 0 | Total review count |
| `view_count` | INT | DEFAULT 0 | Page view count |
| `created_at` | DATETIME | NOT NULL | Creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

### Course Module Table (`course_module`)

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| `course_id` | INT | FOREIGN KEY, NOT NULL | Reference to course |
| `title` | VARCHAR(255) | NOT NULL | Module title |
| `description` | TEXT | NULL | Module description |
| `display_order` | INT | NOT NULL, DEFAULT 1 | Display order |
| `is_active` | BOOLEAN | DEFAULT TRUE | Module status |
| `created_at` | DATETIME | NOT NULL | Creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

### Course Review Table (`course_review`)

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique identifier |
| `course_id` | INT | FOREIGN KEY, NOT NULL | Reference to course |
| `user_id` | INT | FOREIGN KEY, NOT NULL | Reference to user |
| `rating` | INT | NOT NULL, CHECK (1-5) | Rating (1-5 stars) |
| `comment` | TEXT | NULL | Review comment |
| `is_certified` | BOOLEAN | DEFAULT FALSE | Certified student flag |
| `is_approved` | BOOLEAN | DEFAULT FALSE | Admin approval flag |
| `is_featured` | BOOLEAN | DEFAULT FALSE | Featured review flag |
| `created_at` | DATETIME | NOT NULL | Creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

## Course Attributes

### Core Attributes

#### Course Code (`code`)
- **Purpose**: Unique identifier for routing and references
- **Format**: 2-4 letters followed by 1-4 numbers (e.g., FMA, CS101, TRAD1001)
- **Validation**: Pattern `[A-Za-z]{2,4}[0-9]{1,4}`
- **Usage**: Used in URLs, references, and course identification

#### Title (`title`)
- **Purpose**: Human-readable course name
- **Max Length**: 255 characters
- **Required**: Yes
- **Example**: "Financial Markets Analysis"

#### Description (`description`)
- **Purpose**: Detailed course information
- **Type**: TEXT (unlimited length)
- **Required**: Yes
- **Usage**: Course overview, marketing content

#### Category (`category`)
- **Purpose**: Course classification
- **Options**: 
  - Trading Fundamentals
  - Technical Analysis
  - Risk Management
  - Market Psychology
  - Advanced Strategies
  - Forex Trading
  - Cryptocurrency
  - Options Trading
  - Portfolio Management
  - Other
- **Required**: Yes

#### Level (`level`)
- **Purpose**: Difficulty classification
- **Options**: Beginner, Intermediate, Advanced
- **Required**: Yes
- **Usage**: Student filtering and recommendations

#### Duration (`duration`)
- **Purpose**: Course length in minutes
- **Type**: Integer
- **Range**: 1-10,000 minutes
- **Required**: Yes
- **Display**: Converted to hours/minutes for display

#### Price (`price`)
- **Purpose**: Course cost in USD
- **Type**: DECIMAL(10,2)
- **Range**: 0.00 and above
- **Required**: Yes
- **Format**: Always displayed with 2 decimal places

### Media Attributes

#### Thumbnail Image (`thumbnailImage`)
- **Purpose**: Course card display image
- **Dimensions**: 300x200px (recommended)
- **Formats**: JPEG, PNG, JPG
- **Required**: Yes
- **Storage**: `/public/images/uploads/courses/thumbnails/`

#### Banner Image (`bannerImage`)
- **Purpose**: Course header/hero image
- **Dimensions**: 1200x400px (recommended)
- **Formats**: JPEG, PNG, JPG
- **Required**: Yes
- **Storage**: `/public/images/uploads/courses/banners/`

### Educational Content

#### Learning Outcomes (`learningOutcomes`)
- **Purpose**: What students will learn
- **Type**: JSON array of strings
- **Required**: Yes (minimum 1 outcome)
- **Example**: `["Understand market fundamentals", "Analyze price charts"]`

#### Features (`features`)
- **Purpose**: Course highlights and benefits
- **Type**: JSON array of strings
- **Required**: Yes (minimum 1 feature)
- **Example**: `["Live instructor sessions", "Downloadable resources"]`

### Module System

#### Has Modules (`hasModules`)
- **Purpose**: Enable/disable modular course structure
- **Type**: Boolean
- **Default**: false
- **Effect**: When enabled, course content is organized in modules

### Statistics and Tracking

#### Enrollment Statistics
- **Enrolled Count**: Total number of enrollments
- **Active Enrollments**: Currently active students
- **Completed Count**: Students who finished the course
- **Certified Count**: Students who received certification

#### Rating System
- **Average Rating**: Calculated from all approved reviews (1-5 scale)
- **Total Reviews**: Count of all approved reviews
- **View Count**: Number of times course page was viewed

### Status Management

#### Is Active (`isActive`)
- **Purpose**: Course availability status
- **Type**: Boolean
- **Default**: true
- **Effect**: Inactive courses are hidden from public listings

## Validation Rules

### Form Validation

#### Course Code
```php
// Pattern validation
pattern="[A-Za-z]{2,4}[0-9]{1,4}"
maxlength="10"
required
```

#### Title
```php
maxlength="255"
required
```

#### Description
```php
required
// No length limit (TEXT field)
```

#### Category and Level
```php
required
// Must select from predefined options
```

#### Duration
```php
type="number"
min="1"
max="10000"
required
```

#### Price
```php
type="number"
step="0.01"
min="0"
required
```

#### Learning Outcomes and Features
```php
// Minimum 1 item required
// Each item must be non-empty string
required
```

#### Images
```php
accept="image/jpeg,image/png,image/jpg"
required // For creation, optional for editing
```

### Business Rules

1. **Course Code Uniqueness**: Each course must have a unique code
2. **Module Requirements**: If modules are enabled, at least one module is required
3. **Image Requirements**: Both thumbnail and banner images are required
4. **Content Requirements**: At least one learning outcome and one feature required
5. **Price Validation**: Price must be non-negative
6. **Rating Constraints**: Reviews must be 1-5 stars
7. **Approval Workflow**: Reviews require admin approval before display

## Course Management Workflows

### Course Creation Workflow

1. **Access Creation Form**
   - Navigate to `/admin/courses/create`
   - Professional form with enhanced styling

2. **Fill Required Information**
   - Course Code and Title (same row layout)
   - Category, Level, Duration, Price (4-column row)
   - Description (full-width textarea)

3. **Add Educational Content**
   - Learning Outcomes (dynamic list)
   - Course Features (dynamic list)

4. **Upload Images**
   - Thumbnail Image (300x200px)
   - Banner Image (1200x400px)
   - Real-time preview functionality

5. **Configure Modules** (Optional)
   - Enable modules toggle
   - Add module details (title, order, description)
   - Dynamic module management

6. **Submit and Validate**
   - Client-side validation
   - Server-side validation
   - Professional error handling

### Course Editing Workflow

1. **Access Edit Form**
   - Navigate to `/admin/courses/{code}/edit`
   - Pre-populated with existing data

2. **Modify Information**
   - All fields editable
   - Existing images displayed
   - Current modules loaded

3. **Update Content**
   - Add/remove learning outcomes
   - Add/remove features
   - Modify module structure

4. **Save Changes**
   - Validation applied
   - Database update
   - Redirect to course list

### Course Viewing Workflow

1. **Course Details Page**
   - Navigate to `/admin/courses/{code}/preview`
   - Professional showcase layout
   - All course information displayed

2. **Available Actions**
   - Edit Course (icon button)
   - Print Course Details (icon button)
   - Back to Courses (text button)

## Module Management

### Module Structure

```php
class CourseModule
{
    private ?int $id = null;
    private ?Course $course = null;
    private ?string $title = null;
    private ?string $description = null;
    private int $displayOrder = 1;
    private bool $isActive = true;
    private \DateTimeImmutable $createdAt;
    private \DateTimeImmutable $updatedAt;
}
```

### Module Operations

#### Adding Modules
- Dynamic JavaScript-based addition
- Automatic ordering
- Validation requirements
- Professional styling

#### Removing Modules
- Confirmation modal for last module
- Prevents removal of final module
- Smooth animations

#### Reordering Modules
- Manual order specification
- Automatic reordering on save
- Visual feedback

## Image Management

### Image Upload Process

1. **File Selection**
   - Enhanced file input styling
   - Professional "Choose File" buttons
   - Hover effects and transitions

2. **Preview Generation**
   - Real-time image preview
   - Proper aspect ratio display
   - Professional containers

3. **Validation**
   - File type checking (JPEG, PNG, JPG)
   - Size recommendations
   - Error handling

4. **Storage**
   - Organized folder structure
   - Descriptive file naming
   - Path storage in database

### Image Requirements

#### Thumbnail Images
- **Recommended Size**: 300x200px
- **Aspect Ratio**: 3:2
- **Usage**: Course cards, listings
- **Storage**: `/public/images/uploads/courses/thumbnails/`

#### Banner Images
- **Recommended Size**: 1200x400px
- **Aspect Ratio**: 3:1
- **Usage**: Course headers, hero sections
- **Storage**: `/public/images/uploads/courses/banners/`

## Rating and Review System

### Review Structure

```php
class CourseReview
{
    private ?int $id = null;
    private ?Course $course = null;
    private ?User $user = null;
    private int $rating;           // 1-5 stars
    private ?string $comment = null;
    private bool $isCertified = false;
    private bool $isApproved = false;
    private bool $isFeatured = false;
    private \DateTimeImmutable $createdAt;
    private \DateTimeImmutable $updatedAt;
}
```

### Review Workflow

1. **Student Submission**
   - Rating (1-5 stars)
   - Optional comment
   - Automatic certification check

2. **Admin Approval**
   - Review moderation
   - Approval/rejection
   - Featured review selection

3. **Display**
   - Approved reviews only
   - Featured reviews highlighted
   - Certified student badges

### Rating Calculation

```php
// Automatic calculation on review approval
$averageRating = $approvedReviews->avg('rating');
$totalReviews = $approvedReviews->count();
```

## API Endpoints

### Course Management Endpoints

#### List Courses
```
GET /admin/courses
- Returns paginated course list
- Includes basic course information
- Professional table layout
```

#### Create Course
```
POST /admin/courses/create
- Accepts multipart form data
- Validates all required fields
- Handles image uploads
- Returns success/error response
```

#### View Course
```
GET /admin/courses/{code}/preview
- Returns detailed course information
- Includes statistics and reviews
- Professional showcase layout
```

#### Edit Course
```
GET /admin/courses/{code}/edit
- Returns pre-populated edit form
- Includes existing images and modules
- Professional form layout

POST /admin/courses/{code}/edit
- Updates course information
- Handles image replacements
- Validates changes
```

#### Delete Course
```
DELETE /admin/courses/{code}
- Soft delete with confirmation
- Maintains data integrity
- Professional confirmation modal
```

### Public Course Endpoints

#### Course Listing
```
GET /courses
- Public course catalog
- Active courses only
- Filtered by category/level
```

#### Course Details
```
GET /courses/{code}
- Public course information
- Enrollment functionality
- Student reviews display
```

#### Course Enrollment
```
POST /courses/{code}/enroll
- Requires authentication
- Enrollment validation
- Payment processing integration
```

## Troubleshooting Guide

### Common Issues

#### 1. Course Code Conflicts
**Problem**: "Course code already exists" error
**Solution**: 
- Check existing course codes
- Use unique identifier
- Follow naming convention

#### 2. Image Upload Failures
**Problem**: Images not uploading or displaying
**Solution**:
- Check file permissions on upload directory
- Verify file size limits
- Ensure correct image formats (JPEG, PNG, JPG)
- Check storage path configuration

#### 3. Module Validation Errors
**Problem**: Module fields not validating correctly
**Solution**:
- Ensure modules toggle is enabled
- Check required field validation
- Verify JavaScript functionality
- Clear browser cache

#### 4. Form Submission Issues
**Problem**: Form not submitting or validation failing
**Solution**:
- Check CSRF token validity
- Verify all required fields
- Check JavaScript console for errors
- Validate form structure

#### 5. Routing Problems
**Problem**: Course pages not loading (404 errors)
**Solution**:
- Verify course code format
- Check route configuration
- Ensure course exists and is active
- Clear route cache

### Database Issues

#### 1. Migration Problems
**Problem**: Database schema out of sync
**Solution**:
```bash
php bin/console doctrine:migrations:migrate
php bin/console doctrine:schema:update --force
```

#### 2. Data Integrity Issues
**Problem**: Orphaned modules or reviews
**Solution**:
```sql
-- Check for orphaned modules
SELECT * FROM course_module WHERE course_id NOT IN (SELECT id FROM course);

-- Check for orphaned reviews
SELECT * FROM course_review WHERE course_id NOT IN (SELECT id FROM course);
```

#### 3. Performance Issues
**Problem**: Slow course loading
**Solution**:
- Add database indexes
- Optimize queries
- Implement caching
- Review N+1 query problems

### Frontend Issues

#### 1. Styling Problems
**Problem**: Form fields not displaying correctly
**Solution**:
- Check CSS class names
- Verify Bootstrap version compatibility
- Clear browser cache
- Check for CSS conflicts

#### 2. JavaScript Errors
**Problem**: Dynamic functionality not working
**Solution**:
- Check browser console for errors
- Verify jQuery loading
- Check for JavaScript conflicts
- Validate HTML structure

#### 3. Image Preview Issues
**Problem**: Image previews not showing
**Solution**:
- Check file reader API support
- Verify image file validity
- Check JavaScript event handlers
- Test with different browsers

### Security Considerations

#### 1. File Upload Security
- Validate file types server-side
- Limit file sizes
- Scan for malicious content
- Use secure storage locations

#### 2. Input Validation
- Sanitize all user inputs
- Use parameterized queries
- Validate on both client and server
- Implement CSRF protection

#### 3. Access Control
- Verify admin permissions
- Implement role-based access
- Secure sensitive endpoints
- Log administrative actions

### Performance Optimization

#### 1. Database Optimization
- Index frequently queried columns
- Optimize complex queries
- Implement query caching
- Monitor slow queries

#### 2. Image Optimization
- Compress uploaded images
- Generate multiple sizes
- Implement lazy loading
- Use CDN for static assets

#### 3. Caching Strategy
- Cache course listings
- Cache computed statistics
- Implement Redis/Memcached
- Use HTTP caching headers

---

## Conclusion

The Capitol Academy Course Management System provides a comprehensive solution for managing trading education courses. With its professional interface, robust validation, and extensive feature set, it supports the full lifecycle of course management from creation to student enrollment and feedback.

For additional support or feature requests, please refer to the development team or create an issue in the project repository.

**Last Updated**: December 2024
**Version**: 2.0
**Author**: Capitol Academy Development Team
