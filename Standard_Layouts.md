# Capitol Academy Admin Panel - Standard Layouts Documentation

## Table of Contents
1. [Layout Foundations](#layout-foundations)
2. [Page Headers](#page-headers)
3. [Form Components](#form-components)
4. [Dynamic Form Elements](#dynamic-form-elements)
5. [Media Components](#media-components)
6. [Action Buttons](#action-buttons)
7. [Index/Listing Pages](#indexlisting-pages)

---

## 1. LAYOUT FOUNDATIONS

### Container Specifications
- **Main Container**: `container-fluid` with full width
- **Content Wrapper**: 
  - Margin-left: `250px` (desktop)
  - Margin-top: `60px` (fixed topbar height)
  - Width: `calc(100% - 250px)` (desktop)
  - Min-height: `calc(100vh - 60px)`
  - Background: `#f8f9fa` (light gray)

### Grid System
- **Bootstrap 5** grid system
- **Responsive Breakpoints**:
  - Mobile: `max-width: 576px`
  - Tablet: `max-width: 768px`
  - Desktop: `min-width: 769px`

### Color Scheme (CSS Variables)
```css
:root {
    --primary-navy: #011a2d;
    --primary-navy-light: #1a3461;
    --accent-red: #a90418;
    --accent-red-dark: #8b0314;
    --accent-red-light: #c82333;
    --success-green: #28a745;
    --warning-orange: #ffc107;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
    --text-dark: #343a40;
    --text-muted: #6c757d;
    --white: #ffffff;
}
```

### Typography Hierarchy
- **Page Titles**: `font-size: 1.8rem; font-weight: 600;`
- **Section Headers**: `font-size: 1.5rem; font-weight: 600;`
- **Form Labels**: `font-size: 1rem; font-weight: 600;`
- **Body Text**: `font-size: 1rem; font-weight: 400;`
- **Small Text**: `font-size: 0.875rem; color: #6c757d;`

### Spacing Standards
- **Section Margins**: `margin-bottom: 2rem;`
- **Form Group Margins**: `margin-bottom: 2rem;`
- **Card Padding**: `padding: 1.5rem 2rem;`
- **Button Padding**: `padding: 0.75rem 1.5rem;`

---

## 2. PAGE HEADERS

### Create Page Headers
**Container Structure**:
```html
<div class="card border-0 shadow-lg mb-4">
    <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
```

**Specifications**:
- **Background**: `linear-gradient(135deg, #011a2d 0%, #1a3461 100%)`
- **Color**: `white`
- **Padding**: `1.5rem`
- **Border-radius**: `15px 15px 0 0`
- **Box-shadow**: `0 4px 20px rgba(0,0,0,0.1)`

**Title Styling**:
- **Font-size**: `1.8rem`
- **Font-weight**: `600`
- **Margin-bottom**: `0`
- **Icon size**: `2rem` with `margin-right: 0.75rem`

**Button Positioning**: Right-aligned with `justify-content-end`

### Preview Page Headers
**Back Button**:
- **Style**: Circular icon-only button
- **Dimensions**: `45px` diameter
- **Border-radius**: `50%`
- **Background**: `white` with `#011a2e` border
- **Hover**: `#011a2e` background with `white` text

**Edit Button**:
- **Style**: Same as back button
- **Icon**: `fas fa-edit`
- **Positioning**: `margin-left: 0.5rem`

**Print Button**:
- **Style**: Same styling as edit button
- **Icon**: `fas fa-print`

### Edit Page Headers
**Structure**: Same as create page headers
**Additional Elements**: Status indicators and last modified information

---

## 3. FORM COMPONENTS

### Input Fields
**Standard Specifications**:
- **Height**: `calc(1.6em + 1.25rem + 4px)`
- **Border**: `2px solid #ced4da`
- **Border-radius**: `8px`
- **Padding**: `0.75rem 1rem`
- **Font-size**: `1rem`
- **Transition**: `all 0.3s ease`

**Focus State**:
- **Border-color**: `#011a2d`
- **Box-shadow**: `0 0 0 0.2rem rgba(1, 26, 45, 0.25)`
- **Transform**: `translateY(-1px)`

**Hover State**:
- **Border-color**: `#2a5298`

### Label Specifications
- **Font-weight**: `600`
- **Color**: `#011a2d`
- **Margin-bottom**: `0.75rem`
- **Display**: `flex` with `align-items: center`
- **Icon color**: `#1e3c72` with `margin-right: 0.5rem`

### Combobox/Select Fields
**Select2 Integration**:
- **Height**: `calc(1.6em + 1.25rem + 4px)`
- **Border**: `2px solid #ced4da`
- **Border-radius**: `8px`
- **Theme**: `bootstrap4`

**Dropdown Options**:
- **Padding**: `0.75rem 1rem`
- **Background**: `#fff`
- **Color**: `#1e3c72`
- **Font-weight**: `500`
- **Border-bottom**: `1px solid #f1f3f4`

### Numerical Inputs
**Additional Specifications**:
- **Text-align**: `right` for currency fields
- **Input validation**: Real-time formatting
- **Placeholder styling**: `color: #6c757d; font-style: italic;`

### Textarea Fields
**Specifications**:
- **Min-height**: `120px`
- **Resize**: `vertical`
- **Max-length**: Character counter display
- **Line-height**: `1.5`

### Rich Text Content Editor
**Container Styling**:
- **Border**: `2px solid #ced4da`
- **Border-radius**: `8px`
- **Min-height**: `300px`

**Toolbar Specifications**:
- **Background**: `#f8f9fa`
- **Border-bottom**: `1px solid #dee2e6`
- **Padding**: `0.5rem 1rem`
- **Button styling**: Icon-only with hover effects

---

## 4. DYNAMIC FORM ELEMENTS

### Multi-value Fields (Add/Remove)
**Container Structure**:
```html
<div class="collection-items">
    <div class="collection-item mb-2">
        <div class="input-group">
            <!-- Input field -->
            <button type="button" class="btn btn-outline-danger remove-collection-item">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>
```

**Add Button**:
- **Class**: `btn btn-outline-primary`
- **Icon**: `fas fa-plus` with `margin-right: 0.5rem`
- **Text**: "Add [Item Name]"

**Remove Button**:
- **Class**: `btn btn-outline-danger`
- **Icon**: `fas fa-times`
- **Dimensions**: Square button matching input height

### Toggle Switches
**Specifications**:
- **Width**: `60px`
- **Height**: `34px`
- **Border-radius**: `17px`
- **Background**: `#ccc` (inactive), `#011a2d` (active)
- **Transition**: `all 0.3s ease`
- **Slider**: `26px` diameter circle

### Checkbox Containers
**Layout**:
- **Display**: `flex` with `align-items: center`
- **Gap**: `0.5rem`
- **Padding**: `0.75rem 1rem`
- **Border**: `1px solid #dee2e6`
- **Border-radius**: `8px`

**Checkbox Styling**:
- **Size**: `18px`
- **Border-radius**: `4px`
- **Checked color**: `#011a2d`

---

## 5. MEDIA COMPONENTS

### Profile Picture Preview
**Specifications**:
- **Dimensions**: `150px x 150px`
- **Border-radius**: `50%` (circular)
- **Border**: `3px solid #dee2e6`
- **Object-fit**: `cover`
- **Box-shadow**: `0 4px 12px rgba(0,0,0,0.15)`

**Placeholder Styling**:
- **Background**: `#f8f9fa`
- **Color**: `#6c757d`
- **Display**: `flex` with center alignment
- **Icon**: `fas fa-user` at `3rem` size

### Upload Components
**Drop Zone Styling**:
- **Min-height**: `200px`
- **Border**: `2px dashed #dee2e6`
- **Border-radius**: `8px`
- **Background**: `#f8f9fa`
- **Display**: `flex` with center alignment
- **Transition**: `all 0.3s ease`

**Hover State**:
- **Border-color**: `#011a2d`
- **Background**: `rgba(1, 26, 45, 0.05)`

### Thumbnail Previews
**Standard Dimensions**: `300px x 200px`
**Aspect Ratio**: `3:2`
**Styling**:
- **Border**: `2px solid #1e3c72`
- **Border-radius**: `8px`
- **Object-fit**: `cover`
- **Box-shadow**: `0 4px 12px rgba(0,0,0,0.15)`

### Banner Previews
**Standard Dimensions**: `1200px x 400px` (displayed at `400px x 133px`)
**Aspect Ratio**: `3:1`
**Styling**: Same as thumbnail with adjusted dimensions

### Video Previews
**Container**:
- **Aspect-ratio**: `16:9`
- **Border-radius**: `8px`
- **Overflow**: `hidden`
- **Box-shadow**: `0 4px 20px rgba(0,0,0,0.1)`

---

## 6. ACTION BUTTONS

### Create and Cancel Buttons
**Create Button**:
- **Background**: `linear-gradient(135deg, #28a745 0%, #20c997 100%)`
- **Color**: `white`
- **Border**: `none`
- **Border-radius**: `8px`
- **Padding**: `0.75rem 2rem`
- **Font-weight**: `600`
- **Box-shadow**: `0 2px 8px rgba(40, 167, 69, 0.3)`

**Cancel Button**:
- **Background**: `#6c757d`
- **Color**: `white`
- **Border**: `none`
- **Border-radius**: `8px`
- **Padding**: `0.75rem 2rem`
- **Font-weight**: `500`

**Button Container**:
- **Background**: `#f8f9fa`
- **Border-top**: `1px solid #dee2e6`
- **Padding**: `1.5rem 2rem`
- **Display**: `flex` with `justify-content: space-between`

### Edit and Cancel Buttons
**Same styling as Create/Cancel with positioning adjustments**

**Hover Effects**:
- **Transform**: `translateY(-1px)`
- **Box-shadow**: Enhanced shadow with `0.2` opacity increase
- **Transition**: `all 0.3s ease`

---

## 7. INDEX/LISTING PAGES

### Container Specifications
**Main Card**:
- **Border**: `none`
- **Box-shadow**: `0 4px 20px rgba(0,0,0,0.1)`
- **Border-radius**: `15px`
- **Margin-bottom**: `2rem`

### Header with Search
**Search Box**:
- **Width**: `320px` (desktop)
- **Height**: `calc(1.6em + 1.25rem + 4px)`
- **Border**: `2px solid #dee2e6`
- **Border-radius**: `8px 0 0 8px`
- **Padding**: `0.75rem 1rem`

**Search Button**:
- **Background**: `#011a2d`
- **Border**: `2px solid #011a2d`
- **Border-radius**: `0 8px 8px 0`
- **Color**: `white`
- **Padding**: `0.75rem 1rem`

### Card Layouts for Grid Views
**Grid Container**:
- **Display**: `grid`
- **Grid-template-columns**: `repeat(auto-fill, minmax(380px, 1fr))`
- **Gap**: `2rem`
- **Margin-top**: `2rem`

**Individual Cards**:
- **Background**: `white`
- **Border-radius**: `16px`
- **Box-shadow**: `0 4px 20px rgba(0,0,0,0.08)`
- **Transition**: `all 0.4s cubic-bezier(0.4, 0, 0.2, 1)`
- **Overflow**: `hidden`

**Card Hover Effect**:
- **Transform**: `translateY(-8px)`
- **Box-shadow**: `0 20px 40px rgba(0,0,0,0.15)`

### Table Specifications
**Table Container**:
- **Class**: `table-responsive`
- **Border-radius**: `8px`
- **Overflow**: `hidden`

**Table Styling**:
- **Class**: `table table-hover admin-table`
- **Background**: `white`
- **Border**: `none`

**Header Styling**:
- **Background**: `#f8f9fa`
- **Color**: `#495057`
- **Font-weight**: `600`
- **Padding**: `1rem 0.75rem`
- **Border-bottom**: `2px solid #dee2e6`

**Row Styling**:
- **Padding**: `0.75rem`
- **Border-bottom**: `1px solid #f1f3f4`
- **Transition**: `all 0.3s ease`

**Row Hover**:
- **Background**: `rgba(1, 26, 45, 0.05)`

### Action Buttons within Listings
**Button Group Container**:
- **Display**: `flex`
- **Gap**: `0.25rem`
- **Justify-content**: `center`

**Individual Action Buttons**:
- **Size**: `btn-sm`
- **Border-radius**: `6px`
- **Padding**: `0.5rem 0.75rem`
- **Border**: `none`
- **Box-shadow**: `0 2px 4px rgba(0,0,0,0.1)`

**Button Colors**:
- **View**: `linear-gradient(135deg, #17a2b8 0%, #138496 100%)`
- **Edit**: `linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)`
- **Delete**: `linear-gradient(135deg, #dc3545 0%, #c82333 100%)`

### Modal Popups and Confirmation Dialogs
**Modal Container**:
- **Border**: `none`
- **Border-radius**: `8px`
- **Box-shadow**: `0 10px 30px rgba(0, 0, 0, 0.2)`

**Modal Header**:
- **Background**: `linear-gradient(135deg, #011a2d 0%, #1a3461 100%)`
- **Color**: `white`
- **Border**: `none`
- **Padding**: `1rem`

**Modal Body**:
- **Padding**: `1.5rem`
- **Text-align**: `center`

**Modal Footer**:
- **Border**: `none`
- **Padding**: `1rem 1.5rem`
- **Justify-content**: `space-between`

**Confirmation Buttons**:
- **Confirm**: `background: #a90418; color: white;`
- **Cancel**: `background: #6c757d; color: white;`

---

## Responsive Behavior Guidelines

### Mobile (max-width: 768px)
- **Sidebar**: Transform to overlay with `translateX(-250px)`
- **Content wrapper**: `margin-left: 0; width: 100%;`
- **Search container**: `width: 100%`
- **Create buttons**: `width: 100%`
- **Table font-size**: `0.875rem`

### Tablet (max-width: 576px)
- **Page titles**: `font-size: 1.5rem`
- **Icons**: `font-size: 1.5rem`
- **Button padding**: `0.375rem 0.75rem`
- **Card padding**: `1rem 0.75rem`

### Animation Standards
- **Transition duration**: `0.3s`
- **Easing function**: `ease` or `cubic-bezier(0.4, 0, 0.2, 1)`
- **Hover transforms**: `translateY(-1px)` to `translateY(-8px)`
- **Loading states**: Opacity `0.6` with `pointer-events: none`

---

*This documentation serves as the single source of truth for Capitol Academy's admin panel design system, ensuring visual consistency across all pages and components.*
