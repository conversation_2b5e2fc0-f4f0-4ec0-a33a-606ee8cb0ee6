<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add features column to video_plan table
 */
final class Version20250704130000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add features column to video_plan table';
    }

    public function up(Schema $schema): void
    {
        // Add features column to video_plan table
        $this->addSql('ALTER TABLE video_plan ADD features JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove features column from video_plan table
        $this->addSql('ALTER TABLE video_plan DROP features');
    }
}
