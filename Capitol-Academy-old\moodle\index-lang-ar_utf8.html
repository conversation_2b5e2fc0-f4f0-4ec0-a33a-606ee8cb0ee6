<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="rtl" lang="ar" xml:lang="ar">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/rtl.css"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/rtl.css"/>
<meta name="description" content="Capitol Academy is a leading academy that offers an exceptional education program, in global financial markets on how to trade: Forex, Stocks, Commodities, Futures, Bonds, and Indices… Through mastering the: Technical, Fundamental, Psychological analysis"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Capitol Academy "/>
    <title>Capitol Academy</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/index-lang-ar_utf8.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="course course-1 notloggedin dir-rtl lang-ar_utf8" id="site-index">
<div id="page">
<a class="skip" href="#maincontent">أذهب إلى المحتوى الرئيس</a>
    <div id="header-home" class=" clearfix">        <h1 class="headermain">Capitol Academy</h1>
        <div class="headermenu"><div class="logininfo">لم يتم دخولك. (<a href="../moodle/login/index.html">دخول</a>)</div><form action="/" method="get" id="chooselang" class="popupform"><div><label for="chooselang_jump"><span class="accesshide ">اللغة</span></label><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=ar_utf8" selected="selected">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=en_utf8">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=fr_utf8">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="Yfd9sV8Gko"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="اذهب"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div>
    </div>        <hr/>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix">
<table id="layout-table" summary="layout">
  <tr>
  <td id="middle-column"><span id="maincontent"></span><div><h2 class="headingblock header ">المقررات الدراسية المتاحة</h2><h2 class="main">لا توجد مقررات دراسية لهذه الفئة</h2><br/></div></td><td style="width: 210px;" id="right-column"><div><a href="#sb-1" class="skip-block">اقفز دخول</a><div id="inst9" class="block_login sideblock"><div class="header"><div class="title"><input type="image" src="http://www.capitol-academy.com/moodle/pix/t/switch_minus.gif" id="togglehide_inst9" onclick="elementToggleHide(this, true, function(el) {return findParentNode(el, '/', '/'); }, '/', '/'); return false;" alt="Hide دخول block" title="Hide دخول block" class="hide-show-image"/><h2>دخول</h2></div></div><div class="content">
<form class="loginform" id="login" method="post" action="../moodle/login/index.html"><div class="c1 fld username"><label for="login_username">اسم المستخدم</label><input type="text" name="username" id="login_username" value=""/></div><div class="c1 fld password"><label for="login_password">كلمة المرور</label><input type="password" name="password" id="login_password" value=""/></div><div class="c1 btn"><input type="submit" value="دخول"/></div></form>
<div class="footer"><div><a href="../moodle/login/forgot_password.html">هل فقدت كلمة المرر؟</a></div></div></div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst9","Show دخول block","Hide دخول block");
//]]>
</script><span id="sb-1" class="skip-block-to"></span><a href="#sb-2" class="skip-block">اقفز تقويم</a><div id="inst4" class="block_calendar_month sideblock"><div class="header"><div class="title"><input type="image" src="http://www.capitol-academy.com/moodle/pix/t/switch_minus.gif" id="togglehide_inst4" onclick="elementToggleHide(this, true, function(el) {return findParentNode(el, '/', '/'); }, '/', '/'); return false;" alt="Hide تقويم block" title="Hide تقويم block" class="hide-show-image"/><h2>تقويم</h2></div></div><div class="content"><div id="overDiv" style="position: absolute; visibility: hidden; z-index:1000;"></div><script type="text/javascript" src="../moodle/calendar/overlib.cfg_php.js"></script>
<div class="calendar-controls"><a class="previous" href="../moodle/index-cal_m-4-cal_y-2013.html" title="الشهر الماضي"><span class="arrow ">&#x25BA;</span><span class="accesshide ">&nbsp;الشهر الماضي</span></a><span class="hide"> | </span><span class="current"><a href="/">مايو 2013</a></span><span class="hide"> | </span><a class="next" href="/" title="الشهر القادم"><span class="accesshide ">الشهر القادم&nbsp;</span><span class="arrow ">&#x25C4;</span></a>
<span class="clearer"><!-- --></span></div>
<table class="minicalendar" summary="جدول البيانات، مايو 2013 تقويم"><tr class="weekdays"><th scope="col"><abbr title="يوم الاحد">الاحد</abbr></th>
<th scope="col"><abbr title="يوم الاثنين">الاثنين</abbr></th>
<th scope="col"><abbr title="يوم الثلاثاء">الثلاثاء</abbr></th>
<th scope="col"><abbr title="يوم الاربعاء">الاربعاء</abbr></th>
<th scope="col"><abbr title="يوم الخميس">الخميس</abbr></th>
<th scope="col"><abbr title="يوم الجمعة">الجمعة</abbr></th>
<th scope="col"><abbr title="يوم السبت">السبت</abbr></th>
</tr><tr><td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="dayblank">&nbsp;</td>
<td class="day">1</td>
<td class="day">2</td>
<td class="day">3</td>
<td class="weekend day">4</td>
</tr><tr><td class="weekend day">5</td>
<td class="day">6</td>
<td class="day">7</td>
<td class="day">8</td>
<td class="day">9</td>
<td class="day">10</td>
<td class="weekend day">11</td>
</tr><tr><td class="weekend day">12</td>
<td class="day">13</td>
<td class="day">14</td>
<td class="day">15</td>
<td class="day">16</td>
<td class="day">17</td>
<td class="weekend day">18</td>
</tr><tr><td class="weekend day">19</td>
<td class="day">20</td>
<td class="day">21</td>
<td class="day today eventnone"><span class="accesshide ">اليوم الاربعاء,  22 مايو </span><a href="#" onmouseover="return overlib('لا يوجد أحداث', CAPTION, 'اليوم الاربعاء,  22 مايو');" onmouseout="return nd();">22</a></td>
<td class="day">23</td>
<td class="day">24</td>
<td class="weekend day">25</td>
</tr><tr><td class="weekend day">26</td>
<td class="day">27</td>
<td class="day">28</td>
<td class="day">29</td>
<td class="day">30</td>
<td class="day">31</td>
<td class="dayblank">&nbsp;</td></tr></table></div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst4","Show تقويم block","Hide تقويم block");
//]]>
</script><span id="sb-2" class="skip-block-to"></span><div id="inst3" class="block_course_summary sideblock"><div class="content"><div style="text-align: left;"><span style="font-family: tahoma, arial, helvetica, sans-serif;"><font size="2"><b>Capitol Academy is a leading academy that offers an exceptional education program, in global financial markets on how to trade: Forex, Stocks, Commodities, Futures, Bonds, and Indices… Through mastering the: Technical, Fundamental, Psychological analysis</b></font></span></div></div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst3","Show  block","Hide  block");
//]]>
</script><span id="sb-3" class="skip-block-to"></span></div></td>
  </tr>
</table>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">لم يتم دخولك. (<a href="../moodle/login/index.html">دخول</a>)</div><div class="sitelink"><a title="Moodle" href="http://moodle.org/"></a></div></div>
</div>
</body>
</html>