{% extends 'base.html.twig' %}

{% block title %}Payment Cancelled - Capitol Academy{% endblock %}

{% block meta_description %}Your payment was cancelled. You can try again or contact us for assistance.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-warning: #ffc107;
    --ca-white: #ffffff;
}

.cancel-hero {
    background: linear-gradient(135deg, var(--ca-warning) 0%, #e0a800 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.cancel-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.cancel-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cancel-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.info-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.info-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 1rem;
}

.info-text {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn-primary-custom {
    background: var(--ca-primary);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-primary-custom:hover {
    background: var(--ca-accent);
    border-color: var(--ca-accent);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.btn-secondary-custom {
    background: white;
    color: var(--ca-primary);
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--ca-primary);
}

.btn-secondary-custom:hover {
    background: var(--ca-primary);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.help-section {
    background: var(--ca-light-gray);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
}

.help-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 1rem;
}

.help-text {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ca-primary);
    font-weight: 500;
}

.reasons-list {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
}

.reasons-list h4 {
    color: #856404;
    margin-bottom: 1rem;
}

.reasons-list ul {
    color: #856404;
    margin: 0;
}

.reasons-list li {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block body %}
<!-- Cancel Hero Section -->
<section class="cancel-hero">
    <div class="container">
        <div class="cancel-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h1 class="cancel-title">Payment Cancelled</h1>
        <p class="cancel-subtitle">Your payment was not processed</p>
    </div>
</section>

<!-- Information Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Main Info Card -->
                <div class="info-card">
                    <h3 class="info-title">
                        <i class="fas fa-info-circle mr-2"></i>
                        What Happened?
                    </h3>
                    <p class="info-text">
                        Your payment was cancelled and no charges were made to your account. 
                        This could happen for several reasons, and you can try again at any time.
                    </p>
                </div>

                <!-- Common Reasons -->
                <div class="reasons-list">
                    <h4><i class="fas fa-list mr-2"></i>Common Reasons for Payment Cancellation:</h4>
                    <ul>
                        <li>You clicked the "Back" button during payment</li>
                        <li>Payment window was closed before completion</li>
                        <li>Session timeout due to inactivity</li>
                        <li>Network connection issues</li>
                        <li>You decided to review the course details again</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{{ app.request.headers.get('referer') ?: path('app_courses_list') }}" class="btn-primary-custom">
                        <i class="fas fa-redo mr-2"></i>
                        Try Again
                    </a>
                    
                    <a href="{{ path('app_courses_list') }}" class="btn-secondary-custom">
                        <i class="fas fa-graduation-cap mr-2"></i>
                        Browse Courses
                    </a>
                    
                    <a href="{{ path('app_home') }}" class="btn-secondary-custom">
                        <i class="fas fa-home mr-2"></i>
                        Back to Home
                    </a>
                </div>

                <!-- Help Section -->
                <div class="help-section">
                    <h3 class="help-title">
                        <i class="fas fa-question-circle mr-2"></i>
                        Need Help?
                    </h3>
                    <p class="help-text">
                        If you're experiencing issues with payment or have questions about our courses, 
                        our support team is here to help.
                    </p>
                    
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>1-800-CAPITOL</span>
                        </div>
                    </div>
                    
                    <div class="action-buttons" style="margin-top: 1rem;">
                        <a href="{{ path('app_contact') }}" class="btn-primary-custom">
                            <i class="fas fa-comments mr-2"></i>
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
