<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<meta name="description" content=""/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Capitol Academy "/>
    <title>Capitol Academy</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/index-cal_m-7-cal_y-2012.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="course course-1 notloggedin dir-ltr lang-en_utf8" id="site-index">
<div id="page">
<a class="skip" href="#maincontent">Skip to main content</a>
    <div id="header-home" class=" clearfix">        <h1 class="headermain">Capitol Academy</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a href="../moodle/login/index.html">Login</a>)</div><form action="/" method="get" id="chooselang" class="popupform"><div><label for="chooselang_jump"><span class="accesshide ">Language</span></label><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=ar_utf8">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=en_utf8" selected="selected">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/index.php?lang=fr_utf8">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="fzlz72tdX4"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="Go"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div>
    </div>        <hr/>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix">
<table id="layout-table" summary="layout">
  <tr>
  <td style="width: 210px;" id="left-column"><div><a href="#sb-1" class="skip-block">Skip Main Menu</a><div id="inst1" class="block_site_main_menu sideblock"><div class="header"><div class="title"><input type="image" src="http://www.capitol-academy.com/moodle/pix/t/switch_minus.gif" id="togglehide_inst1" onclick="elementToggleHide(this, true, function(el) {return findParentNode(el, '/', '/'); }, '/', '/'); return false;" alt="Hide Main Menu block" title="Hide Main Menu block" class="hide-show-image"/><h2>Main Menu</h2></div></div><div class="content">
<ul class="list">
<li class="r0"><div class="icon column c0"></div><div class="column c1"><a title="Quizzes" href="/">Test N1</a></div></li>
</ul>
</div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst1","Show Main Menu block","Hide Main Menu block");
//]]>
</script><span id="sb-1" class="skip-block-to"></span></div></td><td id="middle-column"><span id="maincontent"></span><div><h2 class="headingblock header ">Available Courses</h2><ul class="unlist"><li><div class="coursebox clearfix"><div class="info"><div class="name"><a title="Click to enter this course" href="/">Course ASP.net</a></div></div><div class="summary">If you select the custom download option, you will be presented with a series of options to include <br/>
</div></div></li>
</ul>
<br/></div></td><td style="width: 210px;" id="right-column"><div><a href="#sb-2" class="skip-block">Skip Calendar</a><div id="inst4" class="block_calendar_month sideblock"><div class="header"><div class="title"><input type="image" src="http://www.capitol-academy.com/moodle/pix/t/switch_minus.gif" id="togglehide_inst4" onclick="elementToggleHide(this, true, function(el) {return findParentNode(el, '/', '/'); }, '/', '/'); return false;" alt="Hide Calendar block" title="Hide Calendar block" class="hide-show-image"/><h2>Calendar</h2></div></div><div class="content"><div id="overDiv" style="position: absolute; visibility: hidden; z-index:1000;"></div><script type="text/javascript" src="../moodle/calendar/overlib.cfg_php.js"></script>
<div class="calendar-controls"><a class="previous" href="/" title="Previous month"><span class="arrow ">&#x25C4;</span><span class="accesshide ">&nbsp;Previous month</span></a><span class="hide"> | </span><span class="current"><a href="/">July 2012</a></span><span class="hide"> | </span><a class="next" href="/" title="Next month"><span class="accesshide ">Next month&nbsp;</span><span class="arrow ">&#x25BA;</span></a>
<span class="clearer"><!-- --></span></div>
<table class="minicalendar" summary="Data table, July 2012 Calendar"><tr class="weekdays"><th scope="col"><abbr title="Sunday">Sun</abbr></th>
<th scope="col"><abbr title="Monday">Mon</abbr></th>
<th scope="col"><abbr title="Tuesday">Tue</abbr></th>
<th scope="col"><abbr title="Wednesday">Wed</abbr></th>
<th scope="col"><abbr title="Thursday">Thu</abbr></th>
<th scope="col"><abbr title="Friday">Fri</abbr></th>
<th scope="col"><abbr title="Saturday">Sat</abbr></th>
</tr><tr><td class="weekend day">1</td>
<td class="day">2</td>
<td class="day">3</td>
<td class="day">4</td>
<td class="day">5</td>
<td class="day">6</td>
<td class="weekend day">7</td>
</tr><tr><td class="weekend day">8</td>
<td class="day">9</td>
<td class="day">10</td>
<td class="day">11</td>
<td class="day">12</td>
<td class="day">13</td>
<td class="weekend day">14</td>
</tr><tr><td class="weekend day">15</td>
<td class="day">16</td>
<td class="day">17</td>
<td class="day">18</td>
<td class="day">19</td>
<td class="day">20</td>
<td class="weekend day">21</td>
</tr><tr><td class="weekend day">22</td>
<td class="day">23</td>
<td class="day">24</td>
<td class="day">25</td>
<td class="day">26</td>
<td class="day">27</td>
<td class="weekend day">28</td>
</tr><tr><td class="weekend day">29</td>
<td class="day">30</td>
<td class="day">31</td>
<td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td><td class="dayblank">&nbsp;</td></tr></table></div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst4","Show Calendar block","Hide Calendar block");
//]]>
</script><span id="sb-2" class="skip-block-to"></span><a href="#sb-3" class="skip-block">Skip Activities</a><div id="inst15" class="block_activity_modules sideblock"><div class="header"><div class="title"><input type="image" src="http://www.capitol-academy.com/moodle/pix/t/switch_minus.gif" id="togglehide_inst15" onclick="elementToggleHide(this, true, function(el) {return findParentNode(el, '/', '/'); }, '/', '/'); return false;" alt="Hide Activities block" title="Hide Activities block" class="hide-show-image"/><h2>Activities</h2></div></div><div class="content">
<ul class="list">
<li class="r0"><div class="icon column c0"></div><div class="column c1"><a href="/">Quizzes</a></div></li>
</ul>
</div></div><script type="text/javascript">
//<![CDATA[
elementCookieHide("inst15","Show Activities block","Hide Activities block");
//]]>
</script><span id="sb-3" class="skip-block-to"></span></div></td>
  </tr>
</table>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">You are not logged in. (<a href="../moodle/login/index.html">Login</a>)</div><div class="sitelink"><a title="Moodle" href="http://moodle.org/"></a></div></div>
</div>
</body>
</html>