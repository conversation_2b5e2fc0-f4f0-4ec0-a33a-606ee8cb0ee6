# invoke rewrite engine
RewriteEngine On
RewriteBase /

Options -Indexes
<IfModule mod_negotiation.c>
	Options -MultiViews
</IfModule>

# Specific redirections
RewriteRule ^index\.html$ / [L,R=301]
# Redirect to www
RewriteCond %{HTTP_HOST} !^www\.
RewriteRule ^(.*)$ http://www.%{HTTP_HOST}/$1 [R=301,L]

# Redirect all extensions to html
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.shtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.phtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.jhtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.htm$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.php$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.aspx$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.asp$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.jsp$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.apk$ $1.html [L,NC,R=301]
# End: Redirect all extensions to html
