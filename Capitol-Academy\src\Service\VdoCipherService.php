<?php

namespace App\Service;

use App\Entity\Video;
use App\Entity\User;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

/**
 * Service for integrating with VdoCipher API for secure video streaming
 */
class VdoCipherService
{
    private const API_BASE_URL = 'https://dev.vdocipher.com/api';
    
    private HttpClientInterface $httpClient;
    private LoggerInterface $logger;
    private ParameterBagInterface $params;
    private string $apiSecret;

    public function __construct(
        HttpClientInterface $httpClient,
        LoggerInterface $logger,
        ParameterBagInterface $params
    ) {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->params = $params;
        
        // TODO: Add VdoCipher API secret to environment variables
        $this->apiSecret = $params->get('vdocipher.api_secret') ?? '';
    }

    /**
     * Generate OTP (One Time Password) for video playback
     */
    public function generateOTP(string $videoId, User $user): ?array
    {
        try {
            $response = $this->httpClient->request('POST', self::API_BASE_URL . '/videos/' . $videoId . '/otp', [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'ttl' => 300, // 5 minutes validity
                    'annotate' => json_encode([
                        'user_id' => $user->getId(),
                        'user_email' => $user->getEmail(),
                        'timestamp' => time()
                    ])
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $data = $response->toArray();
                
                $this->logger->info('VdoCipher OTP generated successfully', [
                    'video_id' => $videoId,
                    'user_id' => $user->getId(),
                    'otp' => $data['otp'] ?? null
                ]);

                return $data;
            }

            $this->logger->error('Failed to generate VdoCipher OTP', [
                'video_id' => $videoId,
                'user_id' => $user->getId(),
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher OTP generation error', [
                'video_id' => $videoId,
                'user_id' => $user->getId(),
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Get video metadata from VdoCipher
     */
    public function getVideoMetadata(string $videoId): ?array
    {
        try {
            $response = $this->httpClient->request('GET', self::API_BASE_URL . '/videos/' . $videoId, [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return $response->toArray();
            }

            $this->logger->error('Failed to get VdoCipher video metadata', [
                'video_id' => $videoId,
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher metadata retrieval error', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Upload video to VdoCipher (for admin use)
     */
    public function uploadVideo(string $title, string $filePath): ?array
    {
        try {
            // First, get upload credentials
            $response = $this->httpClient->request('PUT', self::API_BASE_URL . '/videos', [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'title' => $title,
                    'folderId' => null // Can be set to organize videos in folders
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $uploadData = $response->toArray();
                
                $this->logger->info('VdoCipher upload initiated', [
                    'title' => $title,
                    'video_id' => $uploadData['videoId'] ?? null
                ]);

                return $uploadData;
            }

            $this->logger->error('Failed to initiate VdoCipher upload', [
                'title' => $title,
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher upload initiation error', [
                'title' => $title,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Delete video from VdoCipher
     */
    public function deleteVideo(string $videoId): bool
    {
        try {
            $response = $this->httpClient->request('DELETE', self::API_BASE_URL . '/videos/' . $videoId, [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $this->logger->info('VdoCipher video deleted successfully', [
                    'video_id' => $videoId
                ]);

                return true;
            }

            $this->logger->error('Failed to delete VdoCipher video', [
                'video_id' => $videoId,
                'status_code' => $response->getStatusCode()
            ]);

            return false;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher video deletion error', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Generate embed code for video player
     */
    public function generateEmbedCode(string $videoId, ?string $otp = null, array $options = []): string
    {
        $defaultOptions = [
            'width' => '100%',
            'height' => '400px',
            'autoplay' => false,
            'controls' => true,
            'theme' => 'dark'
        ];

        $options = array_merge($defaultOptions, $options);

        if ($otp) {
            // Secure playback with OTP
            return sprintf(
                '<iframe src="https://player.vdocipher.com/v2/?otp=%s&playbackInfo=%s" 
                         style="border:0;width:%s;height:%s;" 
                         allowFullScreen="true" 
                         allow="encrypted-media"></iframe>',
                $otp,
                $videoId,
                $options['width'],
                $options['height']
            );
        } else {
            // Public playback (for free videos)
            return sprintf(
                '<iframe src="https://player.vdocipher.com/v2/?otp=public&playbackInfo=%s" 
                         style="border:0;width:%s;height:%s;" 
                         allowFullScreen="true" 
                         allow="encrypted-media"></iframe>',
                $videoId,
                $options['width'],
                $options['height']
            );
        }
    }

    /**
     * Get video analytics
     */
    public function getVideoAnalytics(string $videoId, \DateTimeInterface $startDate, \DateTimeInterface $endDate): ?array
    {
        try {
            $response = $this->httpClient->request('GET', self::API_BASE_URL . '/videos/' . $videoId . '/analytics', [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                ],
                'query' => [
                    'startDate' => $startDate->format('Y-m-d'),
                    'endDate' => $endDate->format('Y-m-d')
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return $response->toArray();
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher analytics retrieval error', [
                'video_id' => $videoId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Check if VdoCipher service is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiSecret);
    }

    /**
     * Test VdoCipher API connection
     */
    public function testConnection(): bool
    {
        if (!$this->isConfigured()) {
            return false;
        }

        try {
            $response = $this->httpClient->request('GET', self::API_BASE_URL . '/videos', [
                'headers' => [
                    'Authorization' => 'Apisecret ' . $this->apiSecret,
                ],
                'query' => ['limit' => 1]
            ]);

            return $response->getStatusCode() === 200;

        } catch (\Exception $e) {
            $this->logger->error('VdoCipher connection test failed', [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
