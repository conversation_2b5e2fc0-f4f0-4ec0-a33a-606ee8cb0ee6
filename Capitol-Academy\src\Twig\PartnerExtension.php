<?php

namespace App\Twig;

use App\Repository\PartnerRepository;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class PartnerExtension extends AbstractExtension
{
    public function __construct(
        private PartnerRepository $partnerRepository
    ) {}

    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_active_partners', [$this, 'getActivePartners']),
        ];
    }

    public function getActivePartners(): array
    {
        return $this->partnerRepository->findActivePartnersOrdered();
    }
}
