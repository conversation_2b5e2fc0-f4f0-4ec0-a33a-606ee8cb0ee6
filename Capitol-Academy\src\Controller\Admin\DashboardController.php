<?php

namespace App\Controller\Admin;

use App\Repository\CourseRepository;
use App\Repository\OrderRepository;
use App\Repository\UserRepository;
use App\Repository\VideoRepository;
use App\Repository\PlanRepository;
use App\Repository\UserVideoAccessRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class DashboardController extends AbstractController
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    #[Route('', name: 'admin_dashboard', methods: ['GET'])]
    #[Route('/dashboard', name: 'admin_dashboard_alt', methods: ['GET'])]
    public function index(
        UserRepository $userRepository,
        VideoRepository $videoRepository,
        PlanRepository $planRepository,
        CourseRepository $courseRepository,
        OrderRepository $orderRepository,
        UserVideoAccessRepository $accessRepository
    ): Response {
        // Get basic statistics
        $stats = [
            'users' => $this->getUserStats($userRepository),
            'content' => $this->getContentStats($videoRepository, $planRepository, $courseRepository),
            'orders' => $this->getOrderStats($orderRepository),
            'access' => $this->getAccessStats($accessRepository),
        ];

        // Get recent activity
        $recentActivity = [
            'recent_orders' => $orderRepository->findBy([], ['createdAt' => 'DESC'], 5),
            'recent_users' => $userRepository->findBy([], ['createdAt' => 'DESC'], 5),
            'recent_videos' => $videoRepository->findBy([], ['createdAt' => 'DESC'], 5),
        ];

        // Get revenue data for charts
        $revenueData = $this->getRevenueData($orderRepository);
        $popularContent = $this->getPopularContent($orderRepository);

        return $this->render('admin/dashboard/index.html.twig', [
            'stats' => $stats,
            'recent_activity' => $recentActivity,
            'revenue_data' => $revenueData,
            'popular_content' => $popularContent,
        ]);
    }

    private function getUserStats(UserRepository $userRepository): array
    {
        // Get total users - fresh query builder
        $totalUsers = $userRepository->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Get active users (logged in within 30 days) - fresh query builder
        $activeUsers = $userRepository->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->where('u.lastLoginAt > :date')
            ->setParameter('date', new \DateTime('-30 days'))
            ->getQuery()
            ->getSingleScalarResult();

        // Get new users this month - fresh query builder
        $newUsersThisMonth = $userRepository->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->where('u.createdAt > :date')
            ->setParameter('date', new \DateTime('first day of this month'))
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total' => $totalUsers,
            'active' => $activeUsers,
            'new_this_month' => $newUsersThisMonth,
            'growth_rate' => $totalUsers > 0 ? round(($newUsersThisMonth / $totalUsers) * 100, 1) : 0
        ];
    }

    private function getContentStats(VideoRepository $videoRepository, PlanRepository $planRepository, CourseRepository $courseRepository): array
    {
        $videos = $videoRepository->createQueryBuilder('v')
            ->select('COUNT(v.id) as total, SUM(CASE WHEN v.isActive = true THEN 1 ELSE 0 END) as active, SUM(CASE WHEN v.isFree = true THEN 1 ELSE 0 END) as free')
            ->getQuery()
            ->getSingleResult();

        $plans = $planRepository->createQueryBuilder('p')
            ->select('COUNT(p.id) as total, SUM(CASE WHEN p.is_active = true THEN 1 ELSE 0 END) as active')
            ->getQuery()
            ->getSingleResult();

        $courses = $courseRepository->createQueryBuilder('c')
            ->select('COUNT(c.id) as total, SUM(CASE WHEN c.is_active = true THEN 1 ELSE 0 END) as active')
            ->getQuery()
            ->getSingleResult();

        return [
            'videos' => [
                'total' => $videos['total'],
                'active' => $videos['active'],
                'free' => $videos['free'],
                'premium' => $videos['total'] - $videos['free']
            ],
            'plans' => [
                'total' => $plans['total'],
                'active' => $plans['active']
            ],
            'courses' => [
                'total' => $courses['total'],
                'active' => $courses['active']
            ]
        ];
    }

    private function getOrderStats(OrderRepository $orderRepository): array
    {
        // Get total orders
        $totalOrders = $orderRepository->createQueryBuilder('o')
            ->select('COUNT(o.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Get completed orders
        $completedOrders = $orderRepository->createQueryBuilder('o')
            ->select('COUNT(o.id)')
            ->where('o.paymentStatus = :status')
            ->setParameter('status', 'completed')
            ->getQuery()
            ->getSingleScalarResult();

        // Get total revenue
        $totalRevenue = $orderRepository->createQueryBuilder('o')
            ->select('SUM(o.totalPrice)')
            ->where('o.paymentStatus = :status')
            ->setParameter('status', 'completed')
            ->getQuery()
            ->getSingleScalarResult() ?: 0;

        // Get monthly revenue
        $monthlyRevenue = $orderRepository->createQueryBuilder('o')
            ->select('SUM(o.totalPrice)')
            ->where('o.paymentStatus = :status')
            ->andWhere('o.completedAt > :date')
            ->setParameter('status', 'completed')
            ->setParameter('date', new \DateTime('first day of this month'))
            ->getQuery()
            ->getSingleScalarResult() ?: 0;

        return [
            'total' => $totalOrders,
            'completed' => $completedOrders,
            'pending' => $totalOrders - $completedOrders,
            'total_revenue' => $totalRevenue,
            'monthly_revenue' => $monthlyRevenue,
            'average_order_value' => $completedOrders > 0 ? $totalRevenue / $completedOrders : 0,
            'conversion_rate' => $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 1) : 0
        ];
    }

    private function getAccessStats(UserVideoAccessRepository $accessRepository): array
    {
        // Get total access grants - fresh query builder
        $totalAccess = $accessRepository->createQueryBuilder('uva')
            ->select('COUNT(uva.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Get active access (not expired) - fresh query builder
        $activeAccess = $accessRepository->createQueryBuilder('uva')
            ->select('COUNT(uva.id)')
            ->where('uva.expiresAt IS NULL OR uva.expiresAt > :now')
            ->setParameter('now', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();

        // Get recent views (last 7 days) - fresh query builder
        $recentViews = $accessRepository->createQueryBuilder('uva')
            ->select('COUNT(uva.id)')
            ->where('uva.lastAccessedAt > :date')
            ->setParameter('date', new \DateTime('-7 days'))
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total_access_grants' => $totalAccess,
            'active_access' => $activeAccess,
            'recent_views' => $recentViews,
            'expired_access' => $totalAccess - $activeAccess
        ];
    }

    private function getRevenueData(OrderRepository $orderRepository): array
    {
        // Get revenue for last 12 months
        $monthlyRevenue = [];
        for ($i = 11; $i >= 0; $i--) {
            $startDate = new \DateTime("first day of -{$i} months");
            $endDate = new \DateTime("last day of -{$i} months");

            // Create a fresh query builder for each iteration
            $revenue = $orderRepository->createQueryBuilder('o')
                ->select('SUM(o.totalPrice)')
                ->where('o.paymentStatus = :status')
                ->andWhere('o.completedAt BETWEEN :start AND :end')
                ->setParameter('status', 'completed')
                ->setParameter('start', $startDate)
                ->setParameter('end', $endDate)
                ->getQuery()
                ->getSingleScalarResult() ?: 0;

            $monthlyRevenue[] = [
                'month' => $startDate->format('M Y'),
                'revenue' => $revenue
            ];
        }

        return $monthlyRevenue;
    }

    private function getPopularContent(OrderRepository $orderRepository): array
    {
        $orders = $orderRepository->findBy(['paymentStatus' => 'completed']);
        $itemCounts = [];

        foreach ($orders as $order) {
            foreach ($order->getItems() as $item) {
                $key = $item['type'] . ':' . $item['title'];
                if (!isset($itemCounts[$key])) {
                    $itemCounts[$key] = [
                        'title' => $item['title'],
                        'type' => $item['type'],
                        'count' => 0,
                        'revenue' => 0
                    ];
                }
                $itemCounts[$key]['count'] += $item['quantity'] ?? 1;
                $itemCounts[$key]['revenue'] += $item['price'] * ($item['quantity'] ?? 1);
            }
        }

        // Sort by count and get top 10
        uasort($itemCounts, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        return array_slice($itemCounts, 0, 10);
    }
}
