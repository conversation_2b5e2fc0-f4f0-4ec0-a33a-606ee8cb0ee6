<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add hasModules field to course table
 */
final class Version20250615150000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add hasModules field to course table for course modules management';
    }

    public function up(Schema $schema): void
    {
        // Add hasModules field
        $this->addSql('ALTER TABLE course ADD has_modules TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // Remove the hasModules field
        $this->addSql('ALTER TABLE course DROP has_modules');
    }
}
