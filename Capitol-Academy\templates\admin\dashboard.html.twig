{% extends 'admin/base.html.twig' %}

{% block title %}Dashboard - Capitol Academy Admin{% endblock %}

{% block page_title %}Dashboard{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-0" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
            <div class="card-body py-4">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="h3 mb-2 fw-bold">
                            <i class="fas fa-tachometer-alt me-3"></i>Capitol Academy Dashboard
                        </h1>
                        <p class="mb-0 opacity-75">Welcome to your administrative control center</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <div class="d-flex flex-wrap gap-2 justify-content-lg-end">
                            <span class="badge bg-light text-dark px-3 py-2 fs-6">
                                <i class="fas fa-calendar me-1"></i>{{ "now"|date("M d, Y") }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);">
                        <i class="fas fa-users text-white fs-4"></i>
                    </div>
                </div>
                <h3 class="fw-bold text-primary">{{ stats.total_users }}</h3>
                <p class="text-muted mb-2">Total Users</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>{{ stats.new_users_this_month }} this month
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <i class="fas fa-graduation-cap text-white fs-4"></i>
                    </div>
                </div>
                <h3 class="fw-bold text-success">{{ stats.active_courses }}</h3>
                <p class="text-muted mb-2">Active Courses</p>
                <small class="text-info">
                    <i class="fas fa-book me-1"></i>{{ stats.total_courses }} total
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);">
                        <i class="fas fa-envelope text-white fs-4"></i>
                    </div>
                </div>
                <h3 class="fw-bold text-danger">{{ stats.unprocessed_contacts }}</h3>
                <p class="text-muted mb-2">Pending Contacts</p>
                <small class="text-warning">
                    <i class="fas fa-clock me-1"></i>{{ stats.total_contacts }} total
                </small>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body text-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);">
                        <i class="fas fa-chart-line text-white fs-4"></i>
                    </div>
                </div>
                <h3 class="fw-bold text-warning">{{ ((stats.new_users_this_month / stats.total_users) * 100)|round(1) }}%</h3>
                <p class="text-muted mb-2">Growth Rate</p>
                <small class="text-primary">
                    <i class="fas fa-trending-up me-1"></i>This month
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-chart-line me-2 text-primary"></i>User Growth Trend
                </h5>
            </div>
            <div class="card-body">
                <div style="height: 300px;">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-chart-pie me-2 text-success"></i>Course Distribution
                </h5>
            </div>
            <div class="card-body">
                <div style="height: 300px;">
                    <canvas id="courseDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-user-plus me-2 text-primary"></i>Recent Users
                </h5>
            </div>
            <div class="card-body">
                {% if recent_users|length > 0 %}
                    <div class="list-group list-group-flush">
                        {% for user in recent_users %}
                            <div class="list-group-item border-0 px-0 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3"
                                         style="width: 40px; height: 40px; font-size: 14px; font-weight: bold;">
                                        {{ user.firstName|first|upper }}{{ user.lastName|first|upper }}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-bold">{{ user.firstName }} {{ user.lastName }}</h6>
                                        <small class="text-muted">{{ user.email }}</small>
                                    </div>
                                    <small class="text-muted">{{ user.createdAt|date('M d') }}</small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users text-muted fs-1 mb-3"></i>
                        <p class="text-muted">No recent users</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-envelope me-2 text-danger"></i>Recent Contacts
                </h5>
            </div>
            <div class="card-body">
                {% if recent_contacts|length > 0 %}
                    <div class="list-group list-group-flush">
                        {% for contact in recent_contacts %}
                            <div class="list-group-item border-0 px-0 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-danger text-white d-flex align-items-center justify-content-center me-3"
                                         style="width: 40px; height: 40px;">
                                        <i class="fas fa-envelope fs-6"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-bold">{{ contact.fullName }}</h6>
                                        <small class="text-muted">{{ contact.email }}</small>
                                    </div>
                                    <div class="text-end">
                                        {% if not contact.isProcessed %}
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        {% else %}
                                            <span class="badge bg-success">Processed</span>
                                        {% endif %}
                                        <br><small class="text-muted">{{ contact.createdAt|date('M d') }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-envelope text-muted fs-1 mb-3"></i>
                        <p class="text-muted">No recent contacts</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white border-0 pb-0">
                <h5 class="fw-bold text-dark mb-0">
                    <i class="fas fa-credit-card text-success me-2"></i>Recent Payments
                </h5>
            </div>
            <div class="card-body pt-3">
                {% if recent_payments|length > 0 %}
                    <div class="list-group list-group-flush">
                        {% for payment in recent_payments %}
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex align-items-center">
                                    <div class="payment-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 fw-bold">{{ payment.user.firstName }} {{ payment.user.lastName }}</h6>
                                        <small class="text-muted">{{ payment.course.title }} ({{ payment.course.code }})</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-success">${{ payment.amount|number_format(2) }}</span>
                                        <br><small class="text-muted">{{ payment.createdAt|date('M d') }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-credit-card text-muted fs-1 mb-3"></i>
                        <p class="text-muted">No recent payments</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'New Users',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: '#1e3c72',
                backgroundColor: 'rgba(30, 60, 114, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#1e3c72',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        color: '#6c757d'
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });

    // Course Distribution Chart
    const courseDistributionCtx = document.getElementById('courseDistributionChart').getContext('2d');
    new Chart(courseDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active Courses', 'Inactive Courses', 'Draft Courses'],
            datasets: [{
                data: [{{ stats.active_courses }}, {{ stats.total_courses - stats.active_courses }}, 3],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ],
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        color: '#6c757d'
                    }
                }
            },
            cutout: '60%'
        }
    });

    // Interactive Map Simulation
    $('#worldMap').on('click', function() {
        showAdminNotification('Interactive map feature coming soon!', 'info');
    });

    // Add hover effects to statistics cards
    $('.card').hover(
        function() {
            $(this).css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).css('transform', 'translateY(0)');
        }
    );

    // Real-time clock
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();

        if ($('#real-time-clock').length === 0) {
            $('.breadcrumb').append(`
                <li class="breadcrumb-item active" id="real-time-clock">
                    <i class="fas fa-clock me-1"></i>
                    ${timeString} - ${dateString}
                </li>
            `);
        } else {
            $('#real-time-clock').html(`
                <i class="fas fa-clock me-1"></i>
                ${timeString} - ${dateString}
            `);
        }
    }

    // Update clock every second
    updateClock();
    setInterval(updateClock, 1000);

    console.log('Capitol Academy Professional Dashboard Loaded');
});
</script>

<style>
/* Enhanced Dashboard Styles */
.card {
    transition: all 0.3s ease;
    border: none !important;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.btn {
    transition: all 0.3s ease;
    border: none;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.badge {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Animation for statistics cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* Professional scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
}
</style>
{% endblock %}
