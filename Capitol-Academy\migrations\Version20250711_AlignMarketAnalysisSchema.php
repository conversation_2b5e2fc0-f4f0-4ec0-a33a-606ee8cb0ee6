<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Align market_analysis table with target schema by removing extra columns
 */
final class Version20250711_AlignMarketAnalysisSchema extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove extra columns from market_analysis table to align with target schema';
    }

    public function up(Schema $schema): void
    {
        // Remove columns that don't exist in the target schema
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS summary');
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS tags');
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS thumbnail');
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS status');
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS is_featured');
        
        // Ensure the table structure matches the target schema exactly
        // The target schema should have these columns:
        // id, asset_type, title, excerpt, content, publish_date, created_at, updated_at, 
        // author, views, is_active, thumbnail_image, featured_image
    }

    public function down(Schema $schema): void
    {
        // Re-add the columns for rollback (with appropriate defaults)
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN summary TEXT NULL');
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN tags JSON NULL');
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN thumbnail VARCHAR(255) NULL');
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN status VARCHAR(20) NULL DEFAULT "published"');
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN is_featured TINYINT(1) NULL DEFAULT 0');
    }
}
