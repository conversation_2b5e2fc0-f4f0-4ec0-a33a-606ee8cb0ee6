<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><!-- InstanceBegin template="/Templates/master_fr.dwt" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"> 
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<!-- InstanceBeginEditable name="doctitle" -->
		<title>Untitled Document</title>
        <script language="JavaScript" src="http://www.capitol-academy.com/gen_validatorv4.js"
    type="text/javascript" xml:space="preserve"></script>
        <script type="text/javascript" src="js/jquery-1.2.6.js"></script>
	<script type="text/javascript" src="js/jquery.formvalidation.js"></script>
	<script type="text/javascript">
	$(document).ready(function(){
		$("#formulairecontact").formValidation({
			alias		: "name",
			required	: "accept",
			err_list	: true
		}); 
               
	});
	</script>
<script type="text/javascript" src="js/jquery-f.js"></script>
<script>
// This adds 'placeholder' to the items listed in the jQuery .support object. 
jQuery(function() {
   jQuery.support.placeholder = false;
   test = document.createElement('input');
   if('placeholder' in test) jQuery.support.placeholder = true;
});
// This adds placeholder support to browsers that wouldn't otherwise support it. 
$(function() {
   if(!$.support.placeholder) { 
      var active = document.activeElement;
      $(':text').focus(function () {
         if ($(this).attr('placeholder') != '' && $(this).val() == $(this).attr('placeholder')) {
            $(this).val('').removeClass('hasPlaceholder');
         }
      }).blur(function () {
         if ($(this).attr('placeholder') != '' && ($(this).val() == '' || $(this).val() == $(this).attr('placeholder'))) {
            $(this).val($(this).attr('placeholder')).addClass('hasPlaceholder');
         }
      });
      $(':text').blur();
      $(active).focus();
      $('form:eq(0)').submit(function () {
         $(':text.hasPlaceholder').val('');
      });
   }
});
</script>
		<title>Registration Form</title>
        <style>
.tabf{border:1px solid #999;
 -moz-border-radius: 5px;
  -webkit-border-radius:5px;
   border-radius: 5px; 
   
   padding:10px;
   /* Legacy browsers */
	background: #f3f3f3 url(tabf-bg.png) repeat-x top;
	-o-background-size: 100% 100%;
	-moz-background-size: 100% 100%;
	-webkit-background-size: 100% 100%;
	background-size: 100% 100%;
	/* Internet Explorer */
	*background: #f3f3f3;
	background: #f3f3f3\0/;
	filter: progid:DXImageTransform.Microsoft.gradient(gradientType=0, startColorstr=#FFffffff, endColorstr=#FFf3f3f3);
	 -moz-border-radius: 5px;
	   -webkit-border-radius: 5px;
	    border-radius: 5px;
		 -moz-box-shadow:0px 0px 4px 1px #CDCDCD;
-webkit-box-shadow:0px 0px 4px 1px #CDCDCD;
box-shadow:0px 0px 4px 1px #CDCDCD;
	}
	@media all and (min-width: 0px) {
		.tabf {
			/* Opera */
			background: #f3f3f3 url(tabf-bg.svg);
			/* Recent browsers */
			background-image: -webkit-gradient(
				linear,
				left top, left bottom,
				from(#ffffff),
				to(#f3f3f3)
			);
			background-image: -webkit-linear-gradient(
				top,
				#ffffff,
				#f3f3f3
			);
			background-image: -moz-linear-gradient(
				top,
				#ffffff,
				#f3f3f3
			);
			background-image: -o-linear-gradient(
				top,
				#ffffff,
				#f3f3f3
			);
			background-image: linear-gradient(
				top,
				#ffffff,
				#f3f3f3
			);
		}
	}
	}

</style>
		<!-- InstanceEndEditable -->
		<link href="images/Style.css" rel="stylesheet" type="text/css" />
<!--[if lt IE 7]>
<script src="js/IE7.js"></script>
<![endif]-->
<!--[if lt IE 8]>
<script src="js/IE8.js"></script>
<![endif]-->
<!--[if lt IE 9]>
<script src="js/IE9.js"></script>
<![endif]-->





<script type="text/javascript">
function noselection(target){
if (typeof target.onselectstart!="undefined")
    target.onselectstart=function(){return false}
else if (typeof target.style.MozUserSelect!="undefined")
    target.style.MozUserSelect="none"
else
    target.onmousedown=function(){return false}
target.style.cursor = "default"
}
</script>
<link rel="shortcut icon" type="image/png" href="images/logo17-18.png">
<!-- InstanceBeginEditable name="head" -->
<!-- InstanceEndEditable -->
</head>

<body>
<table align="center"><tr><td></td><td align="center" style="background-color: rgb(58,58,58);" width="100%"><ul class="nav">
	<li><a href="index.html">Accueil  </a></li>
	<li class="dropdown">
		<a href="#">Modules</a>
		<ul>
			<li><a href="executive-pro.html">Programme sur mesure</a></li>
			
			<li class="dropdown">
				<a href="list_courses.html">Liste des Modules</a>
				<ul>
					<li><a href="fma.html"> Marchés Financiers </a></li>
                        <li><a href="tec.html">Analyse Technique</a></li>
                        <li><a href="trs.html">Stratégies de trading</a></li>
                        <li><a href="fun.html">Analyse Fondamentale</a></li>
                        <li><a href="ssa.html">Indicateurs Psychologiques</a></li>
                        <li><a href="mma.html">Gestion de Portefeuille </a></li>
                        <li><a href="rsk.html">Gestion du Risque</a></li>
                        <li><a href="dtr.html">Day Trader</a></li>
                        <li><a href="pro.html">Trader Professionnel </a></li>
				</ul>
			</li>
			
		</ul>
	</li>
	<li class="dropdown"><a href="#">Bourses </a>
     <ul>
<li><a href="#">Tunis</a></li>
                        <li><a href="#">Algerie</a></li>
                        <li><a href="#">Maroc</a></li>
                       
                        
                       
		  </ul>
    
    </li>
    
     <li  class="dropdown"><a href="#">Daily News </a>
     <ul>
<li><a href="news_fx.html"> Analyse du Forex</a></li>
                        <li><a href="form_fx.html">Inscription à la News</a></li>
                        
                       
		  </ul>
    
    </li>
	<li  class="dropdown"><a href="#">Diplômes & Certificats </a>
     <ul>
<li><a href="diplome.html">Diplômes</a></li>
                        <li><a href="diplome.html">Certificats</a></li>
                        
                       
		  </ul>
    
    </li>
	 <li class="dropdown"><a href="#">Partenariats </a>
      <ul>
<li><a href="partnership.html">Apporteur D'affaires </a></li>
                        <li><a href="partnership.html">Supper Affiliate </a></li>
                        <li><a href="partnership.html">White Label</a></li>
                       
                       
		  </ul>
     </li>
     <li><a href="eucation.html">Education</a></li>
     <li class="dropdown" ><a href="#">Formateurs  </a>
      
     <ul>
                    	<li><a href="instructor.html">Formateurs</a></li>
                        <li><a href="instructor.html">Professeurs </a></li>
                      
                    </ul>
     </li>
      <li class="dropdown">
                	<a href="#">Capitol Academy</a>
                	<ul>
                    	<li><a href="about.html"> Nous Connaître</a></li>
                        <li><a href="president_letter.html">Lettre du Président </a></li>
                        <li><a href="contact.html">Nous Contacter</a></li>
                    </ul>
                </li>
             
                
</ul>
</td><td></td></tr><tr><td></td><td width="100%">

<table align="center" width="1000" border="0" cellpadding="0" cellspacing="0">
<tbody>
<tr id="bg" >
<td ><table  id="header" width="100%"><tr><td align="left" width="62%" style=" padding-left:18px" rowspan="2"><a href="#"><img src="images/Logo english.png" width="206" height="99" border="0" /></a></td><td style="padding-left:5px"   ><table align="right"><tr>
  <td valign="middle" id="flag"><a href="../en/index.html"><img src="images/UK flag.png" width="25" height="15" border="0" /></a></td><td valign="middle" id="flag"><a href="../ar/index.html"><img src="images/Arab flag.png" width="25" height="15" border="0" /></a></td><td valign="middle" id="flag"><a href="index.html"><img src="images/french flag.png" width="25" height="15" border="0" /></a></td><td valign="middle" id="flag"><a href="../ru/index.html"><img src="images/flag russe.png" width="25" height="15" border="0" /></a></td><td valign="middle" id="flag"><a href="../ch/index.html"><img src="images/china flag.png" width="25" height="15" border="0" /></a></td><td valign="middle" style="padding-right:5px" id="flag"><a href="#"><img src="images/spanish flag.png" width="25" height="15" border="0" /></a></td><td class="cc" valign="bottom" id="contact"><a  href="contact.html"><span  style=" color:#3a3a3a; font-size:11px; font-family: Tahoma, Geneva, sans-serif;  line-height:12px; font-weight:bold;">Contactez-nous</span></a></td></tr></table></td>
</tr>

<tr><td align="right" valign="bottom"  > <form  action="form_cont.php" method="get"><table><tr>
  <td style="padding-left:5px"><button id="b" name="subject" type="submit" value=" Student Registration Form"><img src="images/student sign in bouton.png" width="164" height="30" border="0" /></button></td><td><button id="b" name="subject" type="submit" value="Instructor Registartion Form"><img src="images/Instructor sign in.png" width="164" height="30" border="0" /></button></td></tr></table></form></td></tr>
  <tr>
    <td style="padding-left:3px" align="center" colspan="3"><img src="images/trait gris haut et bas.png" width="982"  height="2" /></td></tr>
    <tr><td colspan="5"><!-- InstanceBeginEditable name="pagination" -->
      <table style=" margin: -5px 0 -5px 0;">
        <tr>
          <td dir="ltr" id="his" align="left" style="padding-left:10px;" width="903"><a href="index.html">Accueil</a> <img src="images/fleche.png" width="3" height="5" border="0" /> <a href="#"> Formulaire d'Enregistrement</a> </td>
          <td width="18" height="20" align="left"  id="flag2" style="padding-left:6px"><a href="#"><img src="images/FB icon_g.png" name="f" width="18" height="18" border="0" id="f" onmouseover="f.src='images/FB icon.png'" onmouseout="f.src='images/FB icon_g.png'"  /></a></td>
          <td align="left" id="flag2" width="18"><a href="#"><img src="images/twitter icon_g.png" name="t" width="18" height="18" border="0" id="t" onmouseover="t.src='images/twitter icon.png'" onmouseout="t.src='images/twitter icon_g.png'" /></a></td>
          <td align="left" id="flag2" width="18"><a href="#"><img src="images/linked in_g.png" name="i" width="18" height="18" border="0" id="i" onmouseover="i.src='images/linked in.png'" onmouseout="i.src='images/linked in_g.png'" /></a></td>
        </tr>
      </table>
   <!-- InstanceEndEditable --></td></tr>
</table>
</tr>
<tr><td colspan="3">
  <table width="100%">
   
    <tr>
  <td align="center" colspan="4"><img src="images/trait gris fin.png" width="982" height="1" border="0" /></td></tr>
  <tr><td valign="top" colspan="4">
  <table>
  <tr valign="top">
  
<td style=" padding-left:6px;" dir="ltr" valign="top" rowspan="1"><!-- InstanceBeginEditable name="main" --><p class="soustitle">Votre demande de: <span style="font-size:16px"><?php echo $_GET['subject']; ?></span></p> <p> Pour nous contacter et interagir avec Capital Academy, notre équipe de soutien à la clientèle vous aider et répondre à toutes vos questions.</p>
<p>Veuillez remplir ce formulaire et l'équipe de soutien Capitol Académie prendra contact avec vous </p>
 <form action="insert.php" method="post" id="formulairecontact" name="myform" onSubmit="return verif()"> <table width="100%" >
    <tr>
      <td colspan="2" style="padding-top:12px;"><table   align="center" dir="ltr"  class="tabf" >
        <tbody>
          <tr ><td width="129" class="contf">Prénom </td><td width="210" class="inpbox"><input type="text"  placeholder="required"  name="first_name" class="inp"  required="true" /></td><td width="123" class="contf">Nom</td><td width="234" class="inpbox"> <input type="text" placeholder="required" name="last_name" class="inp"  required="true" /></td></tr>
<tr><td class="contf"> Pays</td><td class="inpbox">
  <select name="country" class="list"   >
    <option value="" selected>-Choisir votre Pays-</option>
    <option value="Afghanistan">Afghanistan</option>
    <option value="Albania">Albania</option>
    <option value="Algeria">Algeria</option>
    <option value="American Samoa">American Samoa</option>
    <option value="Andorra">Andorra</option>
    <option value="Angola">Angola</option>
    <option value="Anguilla">Anguilla</option>
    <option value="Antarctica">Antarctica</option>
    <option value="Antigua and Barbuda">Antigua and Barbuda</option>
    <option value="Argentina">Argentina</option>
    <option value="Armenia">Armenia</option>
    <option value="Aruba">Aruba</option>
    <option value="Ascension Island">Ascension Island</option>
    <option value="Australia">Australia</option>
    <option value="Austria">Austria</option>
    <option value="Azerbaijan">Azerbaijan</option>
    <option value="Bahamas">Bahamas</option>
    <option value="Bahrain">Bahrain</option>
    <option value="Bangladesh">Bangladesh</option>
    <option value="Barbados">Barbados</option>
    <option value="Belarus">Belarus</option>
    <option value="Belgium">Belgium</option>
    <option value="Belize">Belize</option>
    <option value="Belize">Benin</option>
    <option value="Bermuda">Bermuda</option>
    <option value="Bhutan">Bhutan</option>
    <option value="Bolivia">Bolivia</option>
    <option value="Bosnia and Herzegovina">Bosnia and Herzegovina</option>
    <option value="Botswana">Botswana</option>
    <option value="Bouvet Island">Bouvet Island</option>
    <option value="Brazil">Brazil</option>
    <option value="British Indian Ocean Territory">British Indian Ocean Territory</option>
    <option value="Brunei">Brunei</option>
    <option value="Bulgaria">Bulgaria</option>
    <option value="Burkina Faso">Burkina Faso</option>
    <option value="Burundi">Burundi</option>
    <option value="Cambodia">Cambodia</option>
    <option value="Cameroon">Cameroon</option>
    <option value="Cape Verde">Cape Verde</option>
    <option value="Cayman Islands">Cayman Islands</option>
    <option value="Central African Republic">Central African Republic</option>
    <option value="Chad">Chad</option>
    <option value="Chile">Chile</option>
    <option value="China">China</option>
    <option value="Christmas Island">Christmas Island</option>
    <option value="Cocos (Keeling) Islands">Cocos (Keeling) Islands</option>
    <option value="Colombia">Colombia</option>
    <option value="Comoros">Comoros</option>
    <option value="Congo">Congo</option>
    <option value="Congo (DRC)">Congo (DRC)</option>
    <option value="Cook Islands">Cook Islands</option>
    <option value="Costa Rica">Costa Rica</option>
    <option value="C&ocirc;te d'Ivoire">C&ocirc;te d'Ivoire</option>
    <option value="Croatia">Croatia</option>
    <option value="Cyprus">Cyprus</option>
    <option value="Czech Republic">Czech Republic</option>
    <option value="Denmark">Denmark</option>
    <option value="Djibouti">Djibouti</option>
    <option value="Dominica">Dominica</option>
    <option value="Dominican Republic">Dominican Republic</option>
    <option value="Ecuador">Ecuador</option>
    <option value="Egypt">Egypt</option>
    <option value="El Salvador">El Salvador</option>
    <option value="Equatorial Guinea">Equatorial Guinea</option>
    <option value="Eritrea">Eritrea</option>
    <option value="Estonia">Estonia</option>
    <option value="Ethiopia">Ethiopia</option>
    <option value="Falkland Islands (Islas Malvinas)">Falkland Islands (Islas Malvinas)</option>
    <option value="Faroe Islands">Faroe Islands</option>
    <option value="Fiji Islands">Fiji Islands</option>
    <option value="Finland">Finland</option>
    <option value="France">France</option>
    <option value="French Polynesia">French Polynesia</option>
    <option value="French Southern and Antarctic Lands">French Southern and Antarctic Lands</option>
    <option value="Gabon">Gabon</option>
    <option value="Gambia">Gambia</option>
    <option value="Georgia">Georgia</option>
    <option value="Germany">Germany</option>
    <option value="Ghana">Ghana</option>
    <option value="Gibraltar">Gibraltar</option>
    <option value="Greece">Greece</option>
    <option value="Greenland">Greenland</option>
    <option value="Grenada">Grenada</option>
    <option value="Guam">Guam</option>
    <option value="Guatemala">Guatemala</option>
    <option value="Guernsey">Guernsey</option>
    <option value="Guinea">Guinea</option>
    <option value="Guinea-Bissau">Guinea-Bissau</option>
    <option value="Guyana">Guyana</option>
    <option value="Haiti">Haiti</option>
    <option value="Heard Island and McDonald Islands">Heard Island and McDonald Islands</option>
    <option value="Honduras">Honduras</option>
    <option value="Hong Kong SAR">Hong Kong SAR</option>
    <option value="Hungary">Hungary</option>
    <option value="Iceland">Iceland</option>
    <option value="India">India</option>
    <option value="Indonesia">Indonesia</option>
    <option value="Iraq">Iraq</option>
    <option value="Ireland">Ireland</option>
    <option value="Isle of Man">Isle of Man</option>
    <option value="Italy">Italy</option>
    <option value="Jamaica">Jamaica</option>
    <option value="Japan">Japan</option>
    <option value="Jersey">Jersey</option>
    <option value="Jordan">Jordan</option>
    <option value="Kazakhstan">Kazakhstan</option>
    <option value="Kenya">Kenya</option>
    <option value="Kiribati">Kiribati</option>
    <option value="Korea">Korea</option>
    <option value="Kuwait">Kuwait</option>
    <option value="Kyrgyzstan">Kyrgyzstan</option>
    <option value="Laos">Laos</option>
    <option value="Latvia">Latvia</option>
    <option value="Lebanon">Lebanon</option>
    <option value="Lesotho">Lesotho</option>
    <option value="Liberia">Liberia</option>
    <option value="Libya">Libya</option>
    <option value="Liechtenstein">Liechtenstein</option>
    <option value="Lithuania">Lithuania</option>
    <option value="Luxembourg">Luxembourg</option>
    <option value="Macao SAR">Macao SAR</option>
    <option value="Macedonia">Macedonia</option>
    <option value="Madagascar">Madagascar</option>
    <option value="Malawi">Malawi</option>
    <option value="Malaysia">Malaysia</option>
    <option value="Maldives">Maldives</option>
    <option value="Mali">Mali</option>
    <option value="Malta">Malta</option>
    <option value="Marshall Islands">Marshall Islands</option>
    <option value="Mauritania">Mauritania</option>
    <option value="Mauritius">Mauritius</option>
    <option value="Mexico">Mexico</option>
    <option value="Micronesia">Micronesia</option>
    <option value="Moldova">Moldova</option>
    <option value="Monaco">Monaco</option>
    <option value="Mongolia">Mongolia</option>
    <option value="Montenegro">Montenegro</option>
    <option value="Montserrat">Montserrat</option>
    <option value="Morocco">Morocco</option>
    <option value="Mozambique">Mozambique</option>
    <option value="Myanmar">Myanmar</option>
    <option value="Namibia">Namibia</option>
    <option value="Nauru">Nauru</option>
    <option value="Nepal">Nepal</option>
    <option value="Netherlands">Netherlands</option>
    <option value="Netherlands Antilles">Netherlands Antilles</option>
    <option value="New Caledonia">New Caledonia</option>
    <option value="New Zealand">New Zealand</option>
    <option value="Nicaragua">Nicaragua</option>
    <option value="Niger">Niger</option>
    <option value="Nigeria">Nigeria</option>
    <option value="Niue">Niue</option>
    <option value="Norfolk Island">Norfolk Island</option>
    <option value="Northern Mariana Islands">Northern Mariana Islands</option>
    <option value="Norway">Norway</option>
    <option value="Oman">Oman</option>
    <option value="Pakistan">Pakistan</option>
    <option value="Palau">Palau</option>
    <option value="Palestin">Palestin</option>
    <option value="Panama">Panama</option>
    <option value="Papua New Guinea">Papua New Guinea</option>
    <option value="Paraguay">Paraguay</option>
    <option value="Peru">Peru</option>
    <option value="Philippines">Philippines</option>
    <option value="Pitcairn Islands">Pitcairn Islands</option>
    <option value="Poland">Poland</option>
    <option value="Portugal">Portugal</option>
    <option value="Puerto Rico">Puerto Rico</option>
    <option value="Qatar">Qatar</option>
    <option value="Republic of Rwanda">Republic of Rwanda</option>
    <option value="Reunion">Reunion</option>
    <option value="Romania">Romania</option>
    <option value="Russia">Russia</option>
    <option value="Samoa">Samoa</option>
    <option value="San Marino">San Marino</option>
    <option value="S&atilde;o Tom&eacute; and Pr&iacute;ncipe">S&atilde;o Tom&eacute; and Pr&iacute;ncipe</option>
    <option value="Saudi Arabia">Saudi Arabia</option>
    <option value="Senegal">Senegal</option>
    <option value="Serbia">Serbia</option>
    <option value="Seychelles">Seychelles</option>
    <option value="Sierra Leone">Sierra Leone</option>
    <option value="Singapore">Singapore</option>
    <option value="Slovakia">Slovakia</option>
    <option value="Slovenia">Slovenia</option>
    <option value="Solomon Islands">Solomon Islands</option>
    <option value="Somalia">Somalia</option>
    <option value="South Africa">South Africa</option>
    <option value="South Georgia and the South Sandwich Islands">South Georgia and the South Sandwich Islands</option>
    <option value="Spain">Spain</option>
    <option value="Sri Lanka">Sri Lanka</option>
    <option value="St. Helena">St. Helena</option>
    <option value="St. Kitts and Nevis">St. Kitts and Nevis</option>
    <option value="St. Lucia">St. Lucia</option>
    <option value="St. Pierre and Miquelon">St. Pierre and Miquelon</option>
    <option value="St. Vincent and the Grenadines">St. Vincent and the Grenadines</option>
    <option value="Suriname">Suriname</option>
    <option value="Svalbard and Jan Mayen">Svalbard and Jan Mayen</option>
    <option value="Swaziland">Swaziland</option>
    <option value="Sweden">Sweden</option>
    <option value="Switzerland">Switzerland</option>
    <option value="Taiwan">Taiwan</option>
    <option value="Tajikistan">Tajikistan</option>
    <option value="Tanzania">Tanzania</option>
    <option value="Thailand">Thailand</option>
    <option value="Timor-Leste (East Timor)">Timor-Leste (East Timor)</option>
    <option value="Togo">Togo</option>
    <option value="Tokelau">Tokelau</option>
    <option value="Tokelau">Tonga</option>
    <option value="Trinidad and Tobago">Trinidad and Tobago</option>
    <option value="Tristan da Cunha">Tristan da Cunha</option>
    <option value="Tunisia">Tunisia</option>
    <option value="Turkey">Turkey</option>
    <option value="Turkmenistan">Turkmenistan</option>
    <option value="Turks and Caicos Islands">Turks and Caicos Islands</option>
    <option value="Tuvalu">Tuvalu</option>
    <option value="Uganda">Uganda</option>
    <option value="Ukraine">Ukraine</option>
    <option value="United Arab Emirates">United Arab Emirates</option>
    <option value="United Kingdom">United Kingdom</option>
    <option value="United States Minor Outlying Islands">United States Minor Outlying Islands</option>
    <option value="Uruguay">Uruguay</option>
    <option value="Uzbekistan">Uzbekistan</option>
    <option value="Vanuatu">Vanuatu</option>
    <option value="Vatican City">Vatican City</option>
    <option value="Venezuela">Venezuela</option>
    <option value="Vietnam">Vietnam</option>
    <option value="Virgin Islands">Virgin Islands</option>
    <option value="Virgin Islands, British">Virgin Islands, British</option>
    <option value="Wallis and Futuna">Wallis and Futuna</option>
    <option value="Yemen">Yemen</option>
    <option value="Zambia">Zambia</option>
    <option value="Zimbabwe">Zimbabwe</option>
  </select>
</td><td class="contf">E-mail</td><td class="inpbox"><input type="email" name="e_mail" class="inp" placeholder="<EMAIL>"  required="true" mask="email" /></td></tr>
<tr><td class="contf">Téléphone Mobile</td><td class="inpbox"><input type="text" name="phone" placeholder="required" required="true"  class="inp" /></td><td class="contf">Téléphone Fixe</td><td class="inpbox"><input type="text" name="phone_2" class="inp" placeholder="optional"  /></td></tr>
<tr><td colspan="4"><table width="100%"><tr  height="35" valign="middle" ><td class="contf" width="43%">Comment avez-vous entendu parler de nous? </td><td align="right" width="57%"><script>

function cambiarCapa(elSelect) {
    var idCapaAmostrar;
    //escogemos la que nos interesa
    //según el texto de la opcion seleccionada del select
    switch( elSelect.options[ elSelect.options.selectedIndex ].text ) {        
      case "Forum":
            idCapaAmostrar="capa1";
        break;
		 case "Friend":
            idCapaAmostrar="capa2";
        break;
		 case "Other...":
            idCapaAmostrar="capa3";
        break;
            }
    var lasCapas=["capa1","capa2","capa3"];
    for(var i in lasCapas)                                                //ocultamos todas
        document.getElementById(lasCapas[i]).style.display="none";
    if(idCapaAmostrar!=undefined)
        document.getElementById(idCapaAmostrar).style.display="block";        //mostramos la que nos interesa
}

</script><select name="Info" class="list" onChange="cambiarCapa(this)" style="width:400px" required="true"  >
    <option   selected>----Comment avez-vous entendu parler de nous?----</option>
  <option>Google</option>
    <option>Yahoo</option>
    <option>Facebook</option>
    <option>Twitter</option>
    <option>Blogger</option>
    <option>Youtube</option>
    <option>Forum</option>
    <option >Ami</option>
    <option>Autre...</option></select><br>
   

    </td></tr><tr><td colspan="2"> <div id="capa1" style="display:none;" >

	<table width="100%"><tr dir="rtl"  ><td align="right" class="contf">S'il vous plaît écrivez le nom du forum</td><td align="right"  width="436"><input type="text" name="forum_name"  placeholder="Forum name"    class="inp" style="width:395px" /></td></tr></table>
</div>
<div id="capa2" style="display:none;" >

	<table width="100%"><tr dir="rtl"  class="contf"><td style="padding-right:25px;" width="30%"><input type="text" name="Friend_name"     class="inp_s" placeholder="Friend Name" /></td><td style="padding-right:25px;" width="31%"> <input type="text" name="Friend_phone"  class="inp_s" placeholder="Friend Phone"   /></td><td style="padding-right:25px;" width="28%">   <input type="text" name="Friend_mail"  class="inp_s" placeholder="Friend Email"/></td></tr></table>
</div>
<div id="capa3" style="display:none;" >

	<table width="100%"><tr dir="rtl"  ><td align="right" class="contf">Autre source</td><td align="right"  width="436"><input type="text" name="other_info"  placeholder="Other Info"    class="inp" style="width:395px" /></td></tr></table>
</div>

</td></tr></table></td></tr>

          <tr>
            <td colspan="4"><table width="100%">
              <tr>
                <td class="contf" colspan="3"><p>Voulez-vous profiter d'un rabais jusqu'à <span style="color:#F93">15%</span>  sur les divers cours de formation?  </p>
                  <p>Entrez le nom d'un ami, un membre de la famille ou un collègue ... Et profiter de cette offre!</p></td>
              </tr>
              <tr>
                <td style="padding-right:25px;" width="30%"><input type="text" name="Friend_name_promo"     class="inp_s" placeholder="Nom" /></td>
                <td style="padding-right:25px;" width="31%"><input type="text" name="Friend_phone_promo"  class="inp_s" placeholder="Friend Phone"   /></td>
                <td style="padding-right:25px;" width="28%"><input type="text" name="Friend_mail_promo"  class="inp_s" placeholder="Friend Email"/></td>
              </tr>
            </table></td>
          </tr>
          <tr>
            <td class="contf" colspan="4">Demande d'Information :<br />
              <br />
              <textarea name="comments" cols="82" rows="5" placeholder="Demande d'Information"></textarea></td>
          </tr>
          <tr>
            <td align="center" colspan="4"><input type="hidden" name="subject" value="<?php echo $_GET['subject']; ?>"/>
              <p><span ><input type="image" src="images/valider.png" width="108" height="42" onclick="check()" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <button id="b"  type="reset"><img src="images/annuler.png" width="108" height="42" border="0" /></button></span></p></td>
          </tr>
        </tbody>
      </table></td>
    </tr>
  </table>
 
  </form>
 <script language="JavaScript" type="text/javascript"
    xml:space="preserve">//<![CDATA[
//You should create the validator only after the definition of the HTML form
  var frmvalidator  = new Validator("myform");

 frmvalidator.EnableMsgsTogether();
 frmvalidator.addValidation("first_name","req","Please enter your First Name");
  frmvalidator.addValidation("first_name","maxlen=20",	"Max length for FirstName is 20");
  frmvalidator.addValidation("first_name","alpha","Alphabetic chars only");
  
  frmvalidator.addValidation("last_name","req","Please enter your Last Name");
  frmvalidator.addValidation("last_name","maxlen=20","Max length is 20");
  
  frmvalidator.addValidation("e_mail","maxlen=50");
  frmvalidator.addValidation("e_mail","req");
  frmvalidator.addValidation("e_mail","email");
  
  frmvalidator.addValidation("phone","maxlen=50");
   frmvalidator.addValidation("phone","req");
  frmvalidator.addValidation("phone","numeric");
  
  
  frmvalidator.addValidation("country","dontselect=");
//]]></script>
    <!-- InstanceEndEditable --></td>
<!-- END contain -->
<td style="padding-left:5px;" valign="top" >
  <table dir="rtl" width="100%" height="146" id="cotac" ><tr><td height="30" colspan="2"></td><tr>
      <td align="left" width="84%"><a href="skype:capitol_academy" class="conta"> Parler à un conseiller </a></td><td style="padding-right:8px"  width="16%"><img src="images/skype.png" width="19" height="19" /></td></tr>
  <tr><td align="left"> 304-1062 (202) 1+</td><td style="padding-right:8px" ><img src="images/call.png" width="22" height="15" /></td></tr>

  <tr><td align="left"><a class="conta" href="mailto:<EMAIL>">Une réponse par mail</a></td><td style="padding-right:8px" ><img src="images/mail.png" width="17" height="15" /></td></tr></table>
  <!-- InstanceBeginEditable name="menu" -->
  <div style="padding-top:8px;"> <a href="promotion.html" ><img id="bu" src="images/Banner.png" width="223" height="154" /></a></div>
  <div style="padding-top:8px;"> <a href="bounus.html" ><img src="images/banner/BANNER-MBCFX.gif" width="221" height="450" /></a></div>
  <table style=" margin-top:8px" width="223"  height="278" id="cor2" class="link2" dir="ltr">
    <tr>
      <td   class="f">&nbsp;</td>
    </tr>
    <tr>
      <td ><a  href="fma_102.html" >Pourquoi Trader  le Marché du Forex?</a></td>
    </tr>
    <tr>
      <td><a  href="tec_204.html" >Reconnaissance des Formes de Trading</a></td>
    </tr>
    <tr>
      <td><a  href="trs.html" >Stratégies Professionnelles de Trading </a></td>
    </tr>
    <tr>
      <td><a   href="trs.html" >Stratégies d'Analyses Techniques</a></td>
    </tr>
    <tr>
      <td><a  href="fun_403.html" >La FED et son Impact sur les Marchés Financiers</a></td>
    </tr>
    <tr>
      <td class="f"><a  href="mma_603.html" >Règles de la Gestion de Capital</a></td>
    </tr>
  </table>
  <!-- InstanceEndEditable --></td>
</tr>




</table></td>
  </tr>
  
   <tr><td colspan="4"></td></tr>
  
  
  
  
  
  

</table></td></tr>
    
      <tr><td dir="rtl" colspan="4" width="1000">
   <iframe  src="banner.html"  align="center" height="245" width="995"  scrolling="no" frameborder="0"></iframe>
  </td></tr>
   <tr><td colspan="4"></td></tr>
  
    
    
		</tbody>


</table>
<div align="center" style="padding-left:6px; margin-top:-5px;" ><table id="sitmap" align="center" class="sitemap" width="984" style="padding:5px 10px; "><tr><td  style="border-bottom:1px solid #898989; "><table style="padding:3px 0 0 0; margin-right:-3px" align="left"><tr><td align="left"   id="flag" width="18"><a href="#"><img src="images/facebook gris.png" width="17" height="18" /></a></td><td align="left" id="flag" width="18"><a href="#"><img src="images/twitter gris.png" width="17" height="18" /></a></td><td align="left" id="flag" width="18"><a href="#"><img src="images/linked in gris.png" width="17" height="18" /></a></td></tr></table></td></tr><tr><td></td></tr><tr><td><table class="sitemap2" align="center" width="100%"><th width="23%" height="14"   ><a   href="#">Modules </a></th>
<th width="12%"><a href="#"> Bourse</a></th><th width="15%"><a href="#"> Diplômes & Certificats </a></th><th width="18%"><a href="#">Partenariat </a></th>
  <th width="10%"><a href="#">Formateurs  </a></th><th width="14%"><a href="#">Capitol Academy</a></th><th width="8%"><a href="#">Nous suivre</a></th><tr><td><a href="executive-pro.html">Executive Program</a></td><td><a href="#">Tunis</a></td><td><a href="diplome.html">Diplômes</a></td><td><a href="partnership.html">Apporteurs D'affaires </a></td><td><a href="instructor.html">Formateurs</a></td><td><a href="about.html">Nous Connaître</a></td>
<td><a href="#">Facebook</a></td></tr>
<tr><td><a href="fma.html"> Marchés Financiers </a></td><td><a  href="#">Algerie </a></td><td><a  href="diplome.html">Certificats </a></td><td><a href="partnership.html">Supper Affiliate </a></td>
<td><a href="instructor.html">Professeurs</a></td>
<td><a href="president_letter.html">Lettre du Président</a></td><td><a href="#">Twitter</a></td></tr>
<tr><td><a href="tec.html">Analyse Technique</a></td><td><a href="#">Maroc</a></td><td>&nbsp;</td><td><a href="partnership.html">White Label </a></td><td></td><td><a href="contact.html">Nous Contacter</a></td><td><a href="http://www.youtube.com/user/capitolacademy1">Youtube</a></td></tr>
<tr>
  <td><a href="trs.html">Stratégies de Trading</a></td><td>&nbsp;</td><td>&nbsp;</td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="fun.html">Analyse Fondamentale</a></td><td>&nbsp;</td><td>&nbsp;</td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="ssa.html"> Indicateurs Psychologiques </a></td><td>&nbsp;</td><td>&nbsp;</td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="mma.html"> Gestion de Portefeuille </a></td><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="rsk.html">Gestion du Risque</a></td><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="dtr.html">Day Trader</a></td><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
<tr><td><a href="pro.html">Trader Professionnel </a></td><td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr>
</table></td></tr><tr><td class="dis"> <table class="copy" align="center" ><tr><td><a href="copyright.html"> Capitol Academy 2012 &copy;</a></td>
<td><span id="sp">|</span>Licence # 1264639G </td><td><span id="sp">|</span><a href="disclaimer.html"> Disclaimer <span style="font-size:9px; " >(Avertissement)</span> </a></td>
<td><span id="sp">|</span><a href="terms.html">Termes et Conditions</a></td><td><span id="sp">|</span><a href="privacy_policy.html">Clause de confidentialité</a></td><td><span id="sp">|</span><a  href="contact.html"><span  style=" color:#ffffff; font-size:11px; font-family: Tahoma, Geneva, sans-serif;  line-height:12px; font-weight:bold;">Contactez-nous</span></a></td></tr></table></td></tr><tr>
  <td sty align="center" ><a href="#"><img src="images/logo sitemap.png" width="85" height="82"  title="Capitol academy" /></a></td></tr></table></div></td></tr></td><td></td></tr></table>
 <script type="text/javascript">
noselection(document.body)
</script>
</body>
<!-- InstanceEnd --></html>
