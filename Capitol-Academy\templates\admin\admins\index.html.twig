{% extends 'admin/base.html.twig' %}

{% block title %}Manage Admins - Capitol Academy Admin{% endblock %}

{% block page_title %}Manage Administrators{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_profile') }}">Profile</a></li>
<li class="breadcrumb-item active">Manage Admins</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Administrator Management',
    'page_icon': 'fas fa-users-cog',
    'search_placeholder': 'Search administrators by name, username, or role...',
    'create_button': {
        'url': path('admin_add_admin'),
        'text': 'Add New Admin',
        'icon': 'fas fa-user-plus'
    },
    'stats': [
        {
            'title': 'Total Admins',
            'value': stats.total,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': stats.active,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked',
            'value': stats.blocked,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': stats.recent,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Full Name'},
            {'text': 'Username'},
            {'text': 'Role/Permissions'},
            {'text': 'Created Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for adminUser in admins %}
            {% set row_cells = [
                {
                    'content': '<div class="d-flex align-items-center justify-content-center">
                        ' ~ (adminUser.profileImageName ?
                            '<img src="' ~ adminUser.profileImageUrl ~ '" alt="' ~ adminUser.fullName ~ '" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;">' :
                            '<div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;">' ~ adminUser.firstName|first ~ adminUser.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class="admin-name mb-0 font-weight-bold text-dark">' ~ adminUser.fullName ~ '</h6>
                    <small class="text-muted">
                        ' ~ (adminUser.isMasterAdmin ? 'Master Administrator' : 'Administrator') ~ '
                    </small>'
                },
                {
                    'content': '<code class="admin-username bg-light text-dark" style="padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;">' ~ adminUser.username ~ '</code>'
                },
                {
                    'content': adminUser.isMasterAdmin ?
                        '<span class="badge admin-role" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-crown mr-1"></i> Master Admin</span><br><small class="text-muted">Full Access</small>' :
                        '<span class="badge admin-role" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-user-shield mr-1"></i> Admin</span><br><small class="text-muted">' ~ adminUser.permissions|length ~ ' permissions</small>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium admin-date">' ~ adminUser.createdAt|date('M d, Y H:i') ~ '</span>'
                },
                {
                    'content': adminUser.isActive ?
                        '<span class="badge" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>' :
                        '<span class="badge" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-ban mr-1"></i> Blocked</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_admin_view', {'id': adminUser.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Details"><i class="fas fa-eye"></i></a>
                        ' ~ (not adminUser.isMasterAdmin ?
                            '<a href="' ~ path('admin_admin_edit', {'id': adminUser.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Admin"><i class="fas fa-edit"></i></a>
                            <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, ' ~ (adminUser.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (adminUser.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (adminUser.isActive ? 'Block' : 'Unblock') ~ ' Admin" onclick="showStatusModal(\'' ~ adminUser.fullName ~ '\', ' ~ (adminUser.isActive ? 'true' : 'false') ~ ', function() { toggleAdminStatus(' ~ adminUser.id ~ ', ' ~ (not adminUser.isActive) ~ '); })"><i class="fas fa-' ~ (adminUser.isActive ? 'lock' : 'unlock') ~ '"></i></button>
                            <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Admin" onclick="showDeleteModal(\'' ~ adminUser.fullName ~ '\', function() { deleteAdmin(' ~ adminUser.id ~ '); })"><i class="fas fa-trash"></i></button>' : ''
                        ) ~ '
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'admin-row',
            'empty_message': 'No administrators found',
            'empty_icon': 'fas fa-users-cog',
            'empty_description': 'No system administrators configured.',
            'search_config': {
                'fields': ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.admin-row',
        ['.admin-name', '.admin-username', '.admin-role', '.admin-date']
    );
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Admin management functions
function toggleAdminStatus(adminId, activate) {
    fetch(`/admin/admin/${adminId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the administrator status');
    });
}

function deleteAdmin(adminId) {
    fetch(`/admin/admin/${adminId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the administrator');
    });
}
</script>
{% endblock %}
