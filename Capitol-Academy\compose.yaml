
services:
###> doctrine/doctrine-bundle ###
  database:
    image: mysql:${MYSQL_VERSION:-8.0}
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE:-capitol_academy_db}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-}
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
    ports:
      - "3306:3306"
    volumes:
      - database_data:/var/lib/mysql:rw
###< doctrine/doctrine-bundle ###

volumes:
###> doctrine/doctrine-bundle ###
  database_data:
###< doctrine/doctrine-bundle ###
