{% extends 'admin/base.html.twig' %}

{% block title %}Email Test - Capitol Academy Admin{% endblock %}

{% block page_title %}Email System Test{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Email Test</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Email Test Card -->
    <div class="card border-0 shadow-lg">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-envelope-open mr-3" style="font-size: 2rem;"></i>
                        Email System Test
                    </h2>
                    <p class="mb-0 mt-2" style="opacity: 0.9;">Test the email functionality to ensure proper configuration</p>
                </div>
                <div class="col-md-4 text-right">
                    <i class="fas fa-cogs" style="font-size: 3rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>

        <div class="card-body" style="padding: 2rem;">
            <!-- Current Configuration Info -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="info-card" style="background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #011a2d;">
                        <h5 style="color: #011a2d; margin-bottom: 1rem;">
                            <i class="fas fa-server mr-2"></i>Current Configuration
                        </h5>
                        <ul class="list-unstyled mb-0">
                            <li><strong>From Email:</strong> <EMAIL></li>
                            <li><strong>Test Recipient:</strong> <EMAIL></li>
                            <li><strong>SMTP Server:</strong> Gmail SMTP</li>
                            <li><strong>Port:</strong> 587 (TLS)</li>
                            <li><strong>Authentication:</strong> App Password</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-card" style="background: #f6f7f9; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #a90418;">
                        <h5 style="color: #a90418; margin-bottom: 1rem;">
                            <i class="fas fa-info-circle mr-2"></i>Test Information
                        </h5>
                        <ul class="list-unstyled mb-0">
                            <li><strong>Test Type:</strong> HTML Email</li>
                            <li><strong>Template:</strong> emails/test_email.html.twig</li>
                            <li><strong>Subject:</strong> Capitol Academy - Email Test</li>
                            <li><strong>Delivery:</strong> Immediate</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Test Form -->
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="test-form-container" style="background: white; padding: 2rem; border-radius: 8px; border: 1px solid #e9ecef;">
                        <h4 style="color: #011a2d; margin-bottom: 1.5rem; text-align: center;">
                            <i class="fas fa-paper-plane mr-2"></i>Send Test Email
                        </h4>
                        
                        <form method="post">
                            <div class="text-center mb-4">
                                <p class="text-muted">
                                    Click the button below to send a test email to <strong><EMAIL></strong>
                                    to verify that the email system is working correctly.
                                </p>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; border: none; padding: 12px 40px; border-radius: 8px; font-weight: 600; transition: all 0.3s ease;">
                                    <i class="fas fa-envelope mr-2"></i>Send Test Email
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert" style="background: #e7f3ff; border: 1px solid #b3d9ff; color: #0066cc; border-radius: 8px;">
                        <h6><i class="fas fa-lightbulb mr-2"></i>Instructions:</h6>
                        <ol class="mb-0">
                            <li>Click the "Send Test Email" button above</li>
                            <li>Check the flash message for success/error status</li>
                            <li>Verify that the test email <NAME_EMAIL></li>
                            <li>If successful, the email system is properly configured</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Add loading state to form submission
    $('form').on('submit', function() {
        const button = $(this).find('button[type="submit"]');
        const originalHtml = button.html();
        
        button.prop('disabled', true);
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');
        
        // Re-enable button after 5 seconds in case of issues
        setTimeout(function() {
            button.prop('disabled', false);
            button.html(originalHtml);
        }, 5000);
    });
});
</script>
{% endblock %}
