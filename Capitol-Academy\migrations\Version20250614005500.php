<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove username column from user table and use email as primary identifier
 */
final class Version20250614005500 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove username column from user table - use email as primary identifier';
    }

    public function up(Schema $schema): void
    {
        // Check if username column exists before dropping it
        $this->addSql('ALTER TABLE `user` DROP COLUMN IF EXISTS username');
    }

    public function down(Schema $schema): void
    {
        // Add username column back if needed for rollback
        $this->addSql('ALTER TABLE `user` ADD username VARCHAR(180) NOT NULL UNIQUE AFTER id');
    }
}
