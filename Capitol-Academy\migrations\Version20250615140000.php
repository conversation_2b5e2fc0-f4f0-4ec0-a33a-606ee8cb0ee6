<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add profile_image field to admin table
 */
final class Version20250615140000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add profile_image field to admin table for admin profile management';
    }

    public function up(Schema $schema): void
    {
        // Add profile_image field
        $this->addSql('ALTER TABLE admin ADD profile_image VARCHAR(255) DEFAULT NULL');
        
        // Set default profile image for master admin
        $this->addSql('UPDATE admin SET profile_image = \'/images/admin-image.png\' WHERE username = \'admin1\'');
    }

    public function down(Schema $schema): void
    {
        // Remove the profile_image field
        $this->addSql('ALTER TABLE admin DROP profile_image');
    }
}
