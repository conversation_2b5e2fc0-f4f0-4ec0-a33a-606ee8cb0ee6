<?php

namespace App\DataFixtures;

use App\Entity\Instructor;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class InstructorFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Create Rabaa Khssibi
        $instructor1 = new Instructor();
        $instructor1->setName('Rabaa Khssibi');
        $instructor1->setEmail('<EMAIL>');
        $instructor1->setBio('Experienced trading professional with over 10 years in financial markets. Specializes in technical analysis, risk management, and forex trading strategies. Holds multiple certifications in financial analysis and has trained hundreds of successful traders.');
        $instructor1->setSpecialization('Forex Trading & Technical Analysis');
        $instructor1->setPhone('******-0101');
        $instructor1->setLinkedinUrl('https://linkedin.com/in/rabaa-khssibi');
        $instructor1->setDisplayOrder(1);
        $instructor1->setIsActive(true);
        $instructor1->setQualifications([
            'Certified Financial Analyst (CFA)',
            'Forex Trading Specialist Certification',
            'Risk Management Professional (RMP)',
            'Technical Analysis Certification'
        ]);
        $instructor1->setAchievements([
            'Trained over 500 successful traders',
            '15+ years in financial markets',
            'Published author on forex trading strategies',
            'Speaker at international trading conferences'
        ]);

        // Create Mourad Hamrouni
        $instructor2 = new Instructor();
        $instructor2->setName('Mourad Hamrouni');
        $instructor2->setEmail('<EMAIL>');
        $instructor2->setBio('Senior market analyst and trading strategist with extensive experience in commodities and cryptocurrency markets. Expert in fundamental analysis, market psychology, and algorithmic trading systems. Former investment banker with deep understanding of global financial markets.');
        $instructor2->setSpecialization('Commodities & Cryptocurrency Trading');
        $instructor2->setPhone('******-0102');
        $instructor2->setLinkedinUrl('https://linkedin.com/in/mourad-hamrouni');
        $instructor2->setDisplayOrder(2);
        $instructor2->setIsActive(true);
        $instructor2->setQualifications([
            'Master of Finance (MFin)',
            'Chartered Market Technician (CMT)',
            'Cryptocurrency Trading Certification',
            'Commodities Trading License'
        ]);
        $instructor2->setAchievements([
            'Former Investment Banking VP',
            'Expert in algorithmic trading systems',
            'Cryptocurrency market specialist since 2015',
            'Commodities trading expert with 12+ years experience'
        ]);

        // Create Christine Hamrouni
        $instructor3 = new Instructor();
        $instructor3->setName('Christine Hamrouni');
        $instructor3->setEmail('<EMAIL>');
        $instructor3->setBio('Professional equity analyst and portfolio manager with strong background in fundamental analysis and value investing. Specializes in stock market analysis, portfolio construction, and investment strategy development. Known for her systematic approach to market research and risk assessment.');
        $instructor3->setSpecialization('Stock Market Analysis & Portfolio Management');
        $instructor3->setPhone('******-0103');
        $instructor3->setLinkedinUrl('https://linkedin.com/in/christine-hamrouni');
        $instructor3->setDisplayOrder(3);
        $instructor3->setIsActive(true);
        $instructor3->setQualifications([
            'Chartered Financial Analyst (CFA)',
            'Portfolio Management Certification',
            'Equity Research Analyst License',
            'Investment Strategy Specialist'
        ]);
        $instructor3->setAchievements([
            'Portfolio Manager with $50M+ AUM',
            'Published equity research reports',
            'Stock market analysis expert',
            'Mentored 200+ investment professionals'
        ]);

        $manager->persist($instructor1);
        $manager->persist($instructor2);
        $manager->persist($instructor3);

        $manager->flush();
    }
}
