<?php

namespace App\DataFixtures;

use App\Entity\Course;
use App\Entity\CourseModule;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class CourseFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Financial Markets Analysis (FMA)
        $fma = new Course();
        $fma->setCode('FMA')
            ->setTitle('Global Financial Markets')
            ->setCategory('Markets')
            ->setBannerImage('FMA banner.png')
            ->setDescription('Recent years have witnessed an increased interest in the financial markets as well in developed countries and in developing countries, thanks to its important role in the economic field due to its high liquidity.');
        
        $manager->persist($fma);

        // FMA Modules
        $fmaModules = [
            ['FMA101', 'Financial Markets Overview', 1],
            ['FMA102', 'Forex Market', 2],
            ['FMA103', 'Stock Market', 3],
            ['FMA104', 'Bonds Market', 4],
            ['FMA105', 'Commodities Market', 5],
        ];

        foreach ($fmaModules as $moduleData) {
            $module = new CourseModule();
            $module->setCode($moduleData[0])
                ->setTitle($moduleData[1])
                ->setSortOrder($moduleData[2])
                ->setCourse($fma);
            $manager->persist($module);
        }

        // Technical Analysis (TEC)
        $tec = new Course();
        $tec->setCode('TEC')
            ->setTitle('Technical Analysis')
            ->setCategory('Analysis')
            ->setDescription('Master chart patterns, indicators, and technical analysis tools to make informed trading decisions.');
        
        $manager->persist($tec);

        // Trading Strategies (TRS)
        $trs = new Course();
        $trs->setCode('TRS')
            ->setTitle('Trading Strategies')
            ->setCategory('Strategy')
            ->setDescription('Learn professional trading strategies and how to implement them effectively in different market conditions.');

        $manager->persist($trs);

        // Fundamental Analysis (FUN)
        $fun = new Course();
        $fun->setCode('FUN')
            ->setTitle('Fundamental Analysis')
            ->setCategory('Analysis')
            ->setDescription('Understand economic indicators, news events, and fundamental factors that drive market movements.');

        $manager->persist($fun);

        // Psychological Analysis (SSA)
        $ssa = new Course();
        $ssa->setCode('SSA')
            ->setTitle('Psychological Analysis')
            ->setCategory('Psychology')
            ->setDescription('Master the psychological aspects of trading and develop the mental discipline required for success.');

        $manager->persist($ssa);

        // Capital Management (MMA)
        $mma = new Course();
        $mma->setCode('MMA')
            ->setTitle('Capital Management')
            ->setCategory('Management')
            ->setDescription('Learn essential money management techniques and position sizing strategies for long-term success.');

        $manager->persist($mma);

        // Risk Management (RSK)
        $rsk = new Course();
        $rsk->setCode('RSK')
            ->setTitle('Risk Management')
            ->setCategory('Management')
            ->setDescription('Understand and implement risk management strategies to protect your trading capital.');

        $manager->persist($rsk);

        // Day Trading (DTR)
        $dtr = new Course();
        $dtr->setCode('DTR')
            ->setTitle('Day Trading')
            ->setCategory('Strategy')
            ->setDescription('Learn the skills and strategies needed for successful day trading.');

        $manager->persist($dtr);

        // Professional Trader (PRO)
        $pro = new Course();
        $pro->setCode('PRO')
            ->setTitle('Professional Trader')
            ->setCategory('Advanced')
            ->setDescription('Advanced course for becoming a professional trader with institutional-level skills.');

        $manager->persist($pro);

        $manager->flush();
    }
}
