{% extends 'base.html.twig' %}

{% block title %}{{ video.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ video.description|slice(0, 160) }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
}

.video-header {
    background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3a52 100%);
    padding: 40px 0;
    color: white;
}

.video-player-section {
    background: #000;
    padding: 0;
    position: relative;
}

.video-player-container {
    max-width: 1200px;
    margin: 0 auto;
    aspect-ratio: 16/9;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.access-denied {
    text-align: center;
    color: white;
    padding: 4rem 2rem;
}

.access-denied i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.video-info {
    padding: 3rem 0;
    background: white;
}

.video-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--ca-medium-gray);
}

.meta-item i {
    color: var(--ca-primary);
}

.video-type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
}

.badge-free {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.badge-premium {
    background: rgba(169, 4, 24, 0.1);
    color: var(--ca-accent);
    border: 1px solid rgba(169, 4, 24, 0.3);
}

.related-videos {
    background: var(--ca-light-gray);
    padding: 3rem 0;
}

.related-video-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.related-video-card:hover {
    transform: translateY(-5px);
}

.related-video-thumbnail {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3a52 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.related-video-content {
    padding: 1rem;
}

.related-video-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--ca-primary);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.related-video-meta {
    font-size: 0.8rem;
    color: var(--ca-medium-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-primary {
    background: var(--ca-primary);
    border-color: var(--ca-primary);
}

.btn-primary:hover {
    background: var(--ca-accent);
    border-color: var(--ca-accent);
}

.btn-outline-primary {
    color: var(--ca-primary);
    border-color: var(--ca-primary);
}

.btn-outline-primary:hover {
    background: var(--ca-primary);
    border-color: var(--ca-primary);
}
</style>
{% endblock %}

{% block body %}
<!-- Video Header -->
<section class="video-header">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ path('app_home') }}" class="text-white-50">
                        <i class="fas fa-home me-1"></i>Home
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{{ path('app_videos') }}" class="text-white-50">Videos</a>
                </li>
                {% if video.category %}
                <li class="breadcrumb-item">
                    <span class="text-white-50">{{ video.category }}</span>
                </li>
                {% endif %}
                <li class="breadcrumb-item active text-white" aria-current="page">{{ video.title }}</li>
            </ol>
        </nav>
        
        <h1 class="h2 mb-3">{{ video.title }}</h1>
        <div class="video-meta">
            <div class="meta-item">
                <i class="fas fa-tag"></i>
                <span>{{ video.category|default('Trading') }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-clock"></i>
                <span>5-10 minutes</span>
            </div>
            <div class="meta-item">
                <span class="video-type-badge {{ video.isFree ? 'badge-free' : 'badge-premium' }}">
                    {{ video.isFree ? 'Free' : 'Premium' }}
                </span>
            </div>
            {% if not video.isFree and video.price %}
            <div class="meta-item">
                <i class="fas fa-dollar-sign"></i>
                <span>{{ video.formattedPrice }}</span>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Video Player Section -->
<section class="video-player-section">
    <div class="video-player-container">
        {% if has_access %}
            <div class="text-center text-white">
                <i class="fas fa-play-circle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                <h4>Video Player</h4>
                <p>Video player integration will be implemented here</p>
            </div>
        {% else %}
            <div class="access-denied">
                <i class="fas fa-lock"></i>
                <h3>Premium Content</h3>
                <p class="mb-4">This video requires access to view. Please log in or purchase to continue.</p>
                {% if not app.user %}
                    <a href="{{ path('app_login') }}" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                    <a href="{{ path('app_register') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Register
                    </a>
                {% else %}
                    <button class="btn btn-light btn-lg" onclick="alert('Purchase functionality will be implemented')">
                        <i class="fas fa-shopping-cart me-2"></i>Purchase Video
                    </button>
                {% endif %}
            </div>
        {% endif %}
    </div>
</section>

<!-- Video Info Section -->
<section class="video-info">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h3 class="mb-3">About This Video</h3>
                {% if video.description %}
                    <p class="lead">{{ video.description }}</p>
                {% else %}
                    <p class="lead">Professional trading content designed to enhance your market knowledge and trading skills.</p>
                {% endif %}
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Video Details</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>Category:</strong> {{ video.category|default('Trading') }}
                            </li>
                            <li class="mb-2">
                                <strong>Duration:</strong> 5-10 minutes
                            </li>
                            <li class="mb-2">
                                <strong>Type:</strong> {{ video.isFree ? 'Free' : 'Premium' }}
                            </li>
                            {% if not video.isFree and video.price %}
                            <li class="mb-2">
                                <strong>Price:</strong> {{ video.formattedPrice }}
                            </li>
                            {% endif %}
                        </ul>
                        
                        {% if not has_access and not video.isFree %}
                            <button class="btn btn-primary w-100" onclick="alert('Purchase functionality will be implemented')">
                                <i class="fas fa-shopping-cart me-2"></i>Purchase Video
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
{% if related_videos|length > 0 %}
<section class="related-videos">
    <div class="container">
        <h3 class="mb-4">Related Videos</h3>
        <div class="row">
            {% for related_video in related_videos %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="related-video-card">
                    <div class="related-video-thumbnail">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="related-video-content">
                        <h6 class="related-video-title">{{ related_video.title }}</h6>
                        <div class="related-video-meta">
                            <span>{{ related_video.category|default('Trading') }}</span>
                            <span class="text-{{ related_video.isFree ? 'success' : 'primary' }}">
                                {{ related_video.isFree ? 'Free' : 'Premium' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

{% endblock %}
