<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Create Admin entity and enhance CourseModule entity
 */
final class Version20250614020000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create Admin entity and enhance CourseModule entity';
    }

    public function up(Schema $schema): void
    {
        // Create admin table
        $this->addSql('CREATE TABLE `admin` (
            id INT AUTO_INCREMENT NOT NULL, 
            email VARCHAR(255) NOT NULL, 
            first_name VARCHAR(100) NOT NULL, 
            last_name VARCHAR(100) NOT NULL, 
            ip_address VARCHAR(45) DEFAULT NULL, 
            roles JSON NOT NULL, 
            password VARCHAR(255) NOT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            is_active TINYINT(1) NOT NULL, 
            last_login_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            UNIQUE INDEX UNIQ_880E0D76E7927C74 (email), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add description and duration columns to course_module table
        $this->addSql('ALTER TABLE course_module ADD description LONGTEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE course_module ADD duration INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Drop admin table
        $this->addSql('DROP TABLE `admin`');
        
        // Remove added columns from course_module table
        $this->addSql('ALTER TABLE course_module DROP COLUMN description');
        $this->addSql('ALTER TABLE course_module DROP COLUMN duration');
    }
}
