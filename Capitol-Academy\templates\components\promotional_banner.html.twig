{% set activeBanner = get_active_promotional_banner() %}
{% if activeBanner %}
    <div id="promotional-banner" class="promotional-banner" data-banner-id="{{ activeBanner.id }}" data-end-date="{{ activeBanner.endDate ? activeBanner.endDate.format('Y-m-d H:i:s') : '' }}" style="background-color: {{ activeBanner.backgroundColor|default('#011a2d') }}; color: white; position: relative; z-index: 1040; width: 100%; top: 0;">
        <div class="container">
            <div class="row align-items-center py-3">
                <!-- Banner Content - Left Aligned -->
                <div class="col-md-4">
                    <div class="promo-text text-start">
                        <h6 class="mb-1 text-white fw-bold">{{ activeBanner.title }}</h6>
                        {% if activeBanner.description %}
                            <p class="mb-0 text-light small">{{ activeBanner.description }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Countdown Timer - Centered -->
                <div class="col-md-4">
                    <div class="countdown-timer d-flex justify-content-center gap-2" id="countdown-timer"
                         data-end-timestamp="{{ activeBanner.endDate ? activeBanner.endDate.timestamp : '' }}">
                        <div class="time-unit text-center">
                            <div class="time-value" style="background: #000; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; font-size: 1.2rem; min-width: 50px;" id="promo-days">06</div>
                            <small class="text-light" style="font-size: 0.8rem;">Days</small>
                        </div>
                        <div class="time-separator text-white fw-bold align-self-center" style="font-size: 1.5rem; margin-top: -10px;">:</div>
                        <div class="time-unit text-center">
                            <div class="time-value" style="background: #000; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; font-size: 1.2rem; min-width: 50px;" id="promo-hours">20</div>
                            <small class="text-light" style="font-size: 0.8rem;">Hours</small>
                        </div>
                        <div class="time-separator text-white fw-bold align-self-center" style="font-size: 1.5rem; margin-top: -10px;">:</div>
                        <div class="time-unit text-center">
                            <div class="time-value" style="background: #000; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; font-size: 1.2rem; min-width: 50px;" id="promo-minutes">46</div>
                            <small class="text-light" style="font-size: 0.8rem;">Min</small>
                        </div>
                        <div class="time-separator text-white fw-bold align-self-center" style="font-size: 1.5rem; margin-top: -10px;">:</div>
                        <div class="time-unit text-center">
                            <div class="time-value" style="background: #000; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; font-size: 1.2rem; min-width: 50px;" id="promo-seconds">03</div>
                            <small class="text-light" style="font-size: 0.8rem;">Sec</small>
                        </div>
                    </div>
                </div>

                <!-- Call to Action - Right Aligned -->
                <div class="col-md-4">
                    <div class="d-flex align-items-center justify-content-end gap-2">
                        <a href="{{ path('app_register') }}" class="btn btn-sm fw-bold px-3 py-2"
                           style="background-color: #99b75a; border: none; color: white; border-radius: 6px; text-decoration: none; transition: all 0.3s ease; font-family: 'Montserrat', sans-serif;"
                           onmouseover="this.style.background='#8ba653'; this.style.borderRadius='8px'; this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.background='#99b75a'; this.style.borderRadius='6px'; this.style.transform='translateY(0)'">
                            Get Your Sale Now
                        </a>
                        <button class="btn btn-sm" onclick="dismissBanner()"
                                style="background: transparent; border: none; border-radius: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; color: white;"
                                title="Close banner"
                                onmouseover="this.style.background='white'; this.style.color='#011a2d'; this.style.borderRadius='6px'"
                                onmouseout="this.style.background='transparent'; this.style.color='white'; this.style.borderRadius='0'">
                            <i class="fas fa-times" style="font-size: 0.8rem;"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    .promotional-banner {
        /* Removed border-bottom and box-shadow for seamless integration */
    }

    .promotional-banner .time-unit {
        min-width: 50px;
    }

    .promotional-banner .btn-warning:hover {
        background-color: #e0a800;
        border-color: #d39e00;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    }

    .promotional-banner .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .promotional-banner .row {
            text-align: center;
        }
        
        .promotional-banner .col-md-4 {
            margin-bottom: 1rem;
        }
        
        .promotional-banner .col-md-4:last-child {
            margin-bottom: 0;
        }
        
        .promotional-banner .countdown-timer {
            gap: 1rem !important;
        }
        
        .promotional-banner .time-value {
            font-size: 1.2rem !important;
        }
        
        .promotional-banner .separator {
            font-size: 1.2rem !important;
        }
    }

    /* Animation for banner appearance */
    .promotional-banner {
        animation: slideDown 0.5s ease-out;
    }

    @keyframes slideDown {
        from {
            transform: translateY(-100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Animation for banner dismissal */
    .promotional-banner.dismissing {
        animation: slideUp 0.3s ease-in forwards;
    }

    @keyframes slideUp {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-100%);
            opacity: 0;
        }
    }
    </style>

    <script>
    // Countdown timer functionality
    function updateCountdown() {
        const countdownElement = document.getElementById('countdown-timer');
        if (!countdownElement) return;

        const endTimestamp = parseInt(countdownElement.dataset.endTimestamp);
        const now = Math.floor(Date.now() / 1000);
        const timeLeft = endTimestamp - now;

        if (timeLeft <= 0) {
            // Timer expired, hide banner
            dismissBanner();
            return;
        }

        const days = Math.floor(timeLeft / 86400);
        const hours = Math.floor((timeLeft % 86400) / 3600);
        const minutes = Math.floor((timeLeft % 3600) / 60);
        const seconds = timeLeft % 60;

        // Update display
        const daysElement = document.getElementById('promo-days');
        const hoursElement = document.getElementById('promo-hours');
        const minutesElement = document.getElementById('promo-minutes');
        const secondsElement = document.getElementById('promo-seconds');

        if (daysElement) daysElement.textContent = String(days).padStart(2, '0');
        if (hoursElement) hoursElement.textContent = String(hours).padStart(2, '0');
        if (minutesElement) minutesElement.textContent = String(minutes).padStart(2, '0');
        if (secondsElement) secondsElement.textContent = String(seconds).padStart(2, '0');
    }

    // Enhanced dismiss banner functionality
    function dismissBanner() {
        const banner = document.getElementById('promotional-banner');
        if (banner) {
            banner.classList.add('dismissing');

            // Smooth slide up animation
            banner.style.transition = 'all 0.3s ease-out';
            banner.style.transform = 'translateY(-100%)';
            banner.style.opacity = '0';

            setTimeout(() => {
                banner.style.display = 'none';
                // Store dismissal in session storage with banner ID to prevent showing same banner again
                const bannerId = banner.dataset.bannerId || 'default';
                sessionStorage.setItem(`promotional_banner_dismissed_${bannerId}`, 'true');
                sessionStorage.setItem('promotional_banner_dismissed', 'true');

                // Adjust body padding if needed
                document.body.style.paddingTop = '0';
            }, 300);
        }
    }

    // Initialize promotional banner system
    document.addEventListener('DOMContentLoaded', function() {
        const banner = document.getElementById('promotional-banner');
        if (!banner) return;

        const bannerId = banner.dataset.bannerId || 'default';

        // Check if this specific banner was already dismissed in this session
        if (sessionStorage.getItem(`promotional_banner_dismissed_${bannerId}`) === 'true' ||
            sessionStorage.getItem('promotional_banner_dismissed') === 'true') {
            banner.style.display = 'none';
            return;
        }

        // Show banner with smooth animation
        banner.style.opacity = '0';
        banner.style.transform = 'translateY(-100%)';
        setTimeout(() => {
            banner.style.transition = 'all 0.3s ease-out';
            banner.style.opacity = '1';
            banner.style.transform = 'translateY(0)';
        }, 100);

        // Start countdown timer if it exists
        const countdownElement = document.getElementById('countdown-timer');
        if (countdownElement) {
            updateCountdown(); // Initial update
            setInterval(updateCountdown, 1000); // Update every second
        }

        // Auto-dismiss banner after 30 seconds if no interaction (optional)
        // setTimeout(() => {
        //     if (banner.style.display !== 'none') {
        //         dismissBanner();
        //     }
        // }, 30000);
    });
    </script>
{% endif %}
