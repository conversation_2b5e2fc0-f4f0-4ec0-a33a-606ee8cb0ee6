{% extends 'admin/base.html.twig' %}

{% block title %}Video Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_video_index') }}">Videos</a></li>
<li class="breadcrumb-item active">{{ video.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-video mr-3" style="font-size: 2rem;"></i>
                        Video Details: {{ video.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href="{{ path('admin_video_edit', {'id': video.id}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Video">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Video Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href="{{ path('admin_video_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">

                        <!-- Video Title and Slug Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Video Title -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-video text-primary mr-1"></i>
                                        Video Title
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ video.title }}
                                    </div>
                                </div>
                            </div>

                            <!-- Video Slug -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-link text-primary mr-1"></i>
                                        URL Slug
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ video.slug ?? 'Not set' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Details Row -->
                        <div class="row print-four-column clearfix">
                            <!-- Video Category -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tags text-primary mr-1"></i>
                                        Category
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ video.category ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing Type -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                        Pricing
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {% if video.isFree %}
                                            Free
                                        {% else %}
                                            ${{ video.price ?? '0.00' }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {% if video.isActive %}
                                            <span class="badge bg-success" style="font-size: 0.75rem; padding: 0.4rem 0.6rem;">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary" style="font-size: 0.75rem; padding: 0.4rem 0.6rem;">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                        Created Date
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {{ video.createdAt|date('M d, Y h:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Video Description -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-alt text-primary mr-1"></i>
                                Description
                            </label>
                            <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;">
                                {{ video.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Media Files -->
                        {% if video.thumbnail or video.videoFile %}
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-photo-video text-primary mr-1"></i>
                                Media Files
                            </label>
                            <div class="row">
                                {% if video.thumbnail %}
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Thumbnail</label>
                                        <div class="image-container d-flex justify-content-center">
                                            <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                                 alt="Video Thumbnail"
                                                 class="enhanced-media-preview"
                                                 style="width: 450px; height: 300px; max-width: 100%; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                {% if video.videoFile %}
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Video File</label>
                                        <div class="video-container d-flex justify-content-center">
                                            <video controls
                                                   class="enhanced-media-preview"
                                                   style="width: 450px; height: 300px; max-width: 100%; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                                <source src="{{ asset('uploads/videos/files/' ~ video.videoFile) }}" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Media Preview Styling */
.enhanced-media-preview {
    transition: all 0.3s ease;
}

.enhanced-media-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2) !important;
}

/* Responsive Media Preview */
@media (max-width: 768px) {
    .enhanced-media-preview {
        width: 100% !important;
        height: auto !important;
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .enhanced-media-preview {
        max-height: 200px;
    }
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
{% endblock %}

{% block print_body %}
    <!-- Video Information Section -->
    <div class="print-section">
        <div class="print-section-title">Video Information</div>
        <div class="print-info-grid">
            <div class="print-info-row">
                <div class="print-info-label">Title:</div>
                <div class="print-info-value">{{ video.title }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Category:</div>
                <div class="print-info-value">{{ video.category ?: 'Not specified' }}</div>
            </div>

            <div class="print-info-row">
                <div class="print-info-label">Status:</div>
                <div class="print-info-value">{{ video.isActive ? 'Active' : 'Inactive' }}</div>
            </div>

            <div class="print-info-row">
                <div class="print-info-label">Created Date:</div>
                <div class="print-info-value">{{ video.createdAt|date('F j, Y \\a\\t g:i A') }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Last Updated:</div>
                <div class="print-info-value">{{ video.updatedAt|date('F j, Y \\a\\t g:i A') }}</div>
            </div>
        </div>
    </div>

    {% if video.description %}
    <!-- Description Section -->
    <div class="print-section">
        <div class="print-section-title">Description</div>
        <div class="print-message-content" style="padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;">{{ video.description }}</div>
    </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
<script>
function printVideoDetails() {
    window.print();
}
</script>
{% endblock %}
