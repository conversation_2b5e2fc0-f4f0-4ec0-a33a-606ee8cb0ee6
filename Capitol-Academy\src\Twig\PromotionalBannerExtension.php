<?php

namespace App\Twig;

use App\Repository\PromotionalBannerRepository;
use Symfony\Bundle\SecurityBundle\Security;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class PromotionalBannerExtension extends AbstractExtension
{
    public function __construct(
        private PromotionalBannerRepository $promotionalBannerRepository,
        private Security $security
    ) {}

    public function getFunctions(): array
    {
        return [
            new TwigFunction('get_active_promotional_banner', [$this, 'getActivePromotionalBanner']),
        ];
    }

    public function getActivePromotionalBanner()
    {
        // Only show promotional banners to anonymous/guest users
        if ($this->security->getUser()) {
            return null;
        }

        return $this->promotionalBannerRepository->findActiveBanner();
    }
}
