<?php 
$errors = '';
$myemail = '<EMAIL>';//<-----Put Your email address here.



$last_name=$_POST['last_name'];
$first_name=$_POST['first_name'];
$country=$_POST['country'];
$e_mail=$_POST['e_mail'];
$phone=$_POST['phone'];

if( empty($errors))
{
	$to = $myemail ;
	$email_subject = "student_fr";
	$email_body = "You have received a new message. ".
	" Here are the details:\n  \n Name: $last_name $first_name \n  Phone Number:  $phone  \n Email Address: $e_mail \n Country: $country "; 
	
	$headers = "From: $myemail\n"; 
	$headers = 'Bcc: <EMAIL>, <EMAIL>' . "\r\n";           
	$headers .= "Reply-To: $e_mail";
	
	mail($to,$email_subject,$email_body,$headers);
	//redirect to the 'thank you' page
	header('Location: thank_you.html');
} 
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Untitled Document</title>
</head>

<body>
<?php


$last_name=$_POST['last_name'];
$first_name=$_POST['first_name'];
$country=$_POST['country'];
$e_mail=$_POST['e_mail'];
$phone=$_POST['phone'];
$phone_2=$_POST['phone_2'];
$MSN=$_POST['MSN'];
$Skype=$_POST['Skype'];
$Yahoo_Messenger=$_POST['Yahoo_Messenger'];
$Info=$_POST['Info'];
$forum_name=$_POST['forum_name'];
$Friend_name=$_POST['Friend_name'];
$Friend_phone=$_POST['Friend_phone'];
$Friend_mail=$_POST['Friend_mail'];
$other_info=$_POST['other_info'];
$Friend_name_promo=$_POST['Friend_name_promo'];
$Friend_phone_promo=$_POST['Friend_phone_promo'];
$Friend_mail_promo=$_POST['Friend_mail_promo'];
$comments=$_POST['comments'];
$subject=$_POST['subject'];
$code=$_POST['code'];
if ( isset($_POST['code']) && is_array($_POST['code']) ) {
    // Arrivé ici $_POST['appel'] existe et est bien un tableau
    $code = implode(', ', $_POST['code']) ;
} else {
    $code = '' ;
}
$date_sys=date('Y-m-d');
$ip=$_SERVER['REMOTE_ADDR'];





 $dbhost = 'P3NWPLSK12SQL-v07.shr.prod.phx3.secureserver.net';
$dbuser = 'capitoldata2';
$dbpass = 'Dell@2020./';

$conn = mysqli_connect($dbhost, $dbuser, $dbpass) or die('Error connecting to mysqli:'.mysqli_error());
//connect to database  
 $dbname = 'ph18864721021_';
mysqli_select_db($dbname)or die('Error connecting to database'.mysqli_error());
  //insert into 
if (!$dbname)
{
  die('Echec de s&eacute;lection de base: ' . mysqli_error());
  }


$sql="INSERT INTO  leads(last_name,first_name,country,e_mail,phone,phone_2,MSN,Skype,Yahoo_Messenger,Info,forum_name,Friend_name,Friend_phone,Friend_mail,other_info,Friend_name_promo,Friend_phone_promo,Friend_mail_promo,comments,subject,code,date_sys,ip)
VALUES ('$last_name','$first_name', '$country','$e_mail', '$phone','$phone_2','$MSN','$Skype','$Yahoo_Messenger','$Info','$forum_name','$Friend_name','$Friend_phone','$Friend_mail','$other_info','$Friend_name_promo','$Friend_phone_promo','$Friend_mail_promo','$comments','$subject','$code','$date_sys','$ip')";


$requete = mysqli_query($sql,$conn) or die( mysqli_error() ) ;


if($requete)
  { 
include('thank_you.html');

  }
 
  

 
 ?>
</body>
</html>