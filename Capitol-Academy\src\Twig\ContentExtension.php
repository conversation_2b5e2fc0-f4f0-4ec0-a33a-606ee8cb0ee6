<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class ContentExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('clean_html', [$this, 'cleanHtml'], ['is_safe' => ['html']]),
            new TwigFilter('sanitize_content', [$this, 'sanitizeContent'], ['is_safe' => ['html']]),
        ];
    }

    /**
     * Clean HTML content by removing inline styles and unwanted attributes
     */
    public function cleanHtml(string $content): string
    {
        if (empty($content)) {
            return '';
        }

        // Remove inline styles
        $content = preg_replace('/style\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // Remove color attributes
        $content = preg_replace('/color\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // Remove font attributes
        $content = preg_replace('/font-[a-z-]+\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // Remove empty attributes
        $content = preg_replace('/\s+[a-z-]+\s*=\s*["\'][\s]*["\']/i', '', $content);
        
        // Clean up multiple spaces
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Remove empty tags
        $content = preg_replace('/<([^>]+)>\s*<\/\1>/', '', $content);
        
        return trim($content);
    }

    /**
     * Sanitize content for safe display
     */
    public function sanitizeContent(string $content): string
    {
        if (empty($content)) {
            return '';
        }

        // First clean the HTML
        $content = $this->cleanHtml($content);
        
        // Define allowed tags
        $allowedTags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><blockquote><a>';
        
        // Strip unwanted tags but keep allowed ones
        $content = strip_tags($content, $allowedTags);
        
        // Clean up links - remove javascript and unwanted protocols
        $content = preg_replace('/href\s*=\s*["\']javascript:[^"\']*["\']/i', 'href="#"', $content);
        $content = preg_replace('/href\s*=\s*["\'](?!https?:\/\/|\/)[^"\']*["\']/i', 'href="#"', $content);
        
        // Add target="_blank" to external links
        $content = preg_replace('/(<a[^>]+href\s*=\s*["\']https?:\/\/[^"\']*["\'][^>]*)>/i', '$1 target="_blank" rel="noopener noreferrer">', $content);
        
        return $content;
    }
}
