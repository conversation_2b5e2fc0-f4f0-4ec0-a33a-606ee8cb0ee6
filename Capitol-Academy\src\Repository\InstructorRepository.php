<?php

namespace App\Repository;

use App\Entity\Instructor;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Instructor>
 */
class InstructorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Instructor::class);
    }

    /**
     * Find all active instructors ordered by display order
     */
    public function findActiveInstructors(): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('i.displayOrder', 'ASC')
            ->addOrderBy('i.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all instructors ordered by display order
     */
    public function findAllOrdered(): array
    {
        return $this->createQueryBuilder('i')
            ->orderBy('i.displayOrder', 'ASC')
            ->addOrderBy('i.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find instructors with search functionality
     */
    public function findBySearch(string $search): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.name LIKE :search OR i.specialization LIKE :search OR i.bio LIKE :search')
            ->setParameter('search', '%' . $search . '%')
            ->orderBy('i.displayOrder', 'ASC')
            ->addOrderBy('i.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get instructor statistics
     */
    public function getInstructorStats(): array
    {
        $total = $this->count([]);
        $active = $this->count(['isActive' => true]);
        $inactive = $total - $active;

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $inactive
        ];
    }

    /**
     * Get next display order
     */
    public function getNextDisplayOrder(): int
    {
        $result = $this->createQueryBuilder('i')
            ->select('MAX(i.displayOrder)')
            ->getQuery()
            ->getSingleScalarResult();

        return ($result ?? 0) + 1;
    }

    /**
     * Update display orders
     */
    public function updateDisplayOrders(array $instructorOrders): void
    {
        foreach ($instructorOrders as $instructorId => $order) {
            $this->createQueryBuilder('i')
                ->update()
                ->set('i.displayOrder', ':order')
                ->set('i.updatedAt', ':now')
                ->where('i.id = :id')
                ->setParameter('order', $order)
                ->setParameter('now', new \DateTimeImmutable())
                ->setParameter('id', $instructorId)
                ->getQuery()
                ->execute();
        }
    }

    public function save(Instructor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Instructor $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find instructor by email prefix (email without @ and domain)
     */
    public function findByEmailPrefix(string $emailPrefix): ?Instructor
    {
        return $this->createQueryBuilder('i')
            ->where('i.email LIKE :emailPrefix')
            ->setParameter('emailPrefix', $emailPrefix . '@%')
            ->getQuery()
            ->getOneOrNullResult();
    }
}
