<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove external_link field from market_analysis table
 */
final class Version20250711_RemoveExternalLinkFromMarketAnalysis extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove external_link column from market_analysis table';
    }

    public function up(Schema $schema): void
    {
        // Remove the external_link column if it exists
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN IF EXISTS external_link');
    }

    public function down(Schema $schema): void
    {
        // Re-add the external_link column for rollback
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN external_link VARCHAR(500) NULL');
    }
}
