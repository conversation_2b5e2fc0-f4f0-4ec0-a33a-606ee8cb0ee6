<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250621000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update contact table: remove phone, firstName, lastName columns and add full_name column';
    }

    public function up(Schema $schema): void
    {
        // First, create the new full_name column
        $this->addSql('ALTER TABLE contact ADD full_name VARCHAR(200) NOT NULL DEFAULT ""');

        // Migrate existing data by combining first_name and last_name into full_name
        $this->addSql('UPDATE contact SET full_name = CONCAT(first_name, " ", last_name) WHERE first_name IS NOT NULL AND last_name IS NOT NULL');

        // Drop the old columns
        $this->addSql('ALTER TABLE contact DROP COLUMN phone');
        $this->addSql('ALTER TABLE contact DROP COLUMN first_name');
        $this->addSql('ALTER TABLE contact DROP COLUMN last_name');
    }

    public function down(Schema $schema): void
    {
        // Add back the old columns
        $this->addSql('ALTER TABLE contact ADD first_name VARCHAR(100) NOT NULL DEFAULT ""');
        $this->addSql('ALTER TABLE contact ADD last_name VARCHAR(100) NOT NULL DEFAULT ""');
        $this->addSql('ALTER TABLE contact ADD phone VARCHAR(20) DEFAULT NULL');
        
        // Try to split full_name back into first_name and last_name (basic approach)
        $this->addSql('UPDATE contact SET first_name = SUBSTRING_INDEX(full_name, " ", 1), last_name = SUBSTRING_INDEX(full_name, " ", -1) WHERE full_name IS NOT NULL');
        
        // Drop the full_name column
        $this->addSql('ALTER TABLE contact DROP COLUMN full_name');
    }
}
