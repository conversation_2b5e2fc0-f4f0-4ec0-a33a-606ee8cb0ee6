{% extends 'admin/base.html.twig' %}

{% block title %}Orders Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Orders Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Orders</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Orders Management',
    'page_icon': 'fas fa-shopping-cart',
    'search_placeholder': 'Search orders by ID, customer, or plan...',
    'create_button': {
        'url': path('admin_order_export'),
        'text': 'Export Orders',
        'icon': 'fas fa-download',
        'class': 'btn-success'
    },
    'stats': [
        {
            'title': 'Total Orders',
            'value': orders|length,
            'icon': 'fas fa-shopping-cart',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Completed Orders',
            'value': orders|filter(order => order.status == 'completed')|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Pending Orders',
            'value': orders|filter(order => order.status == 'pending')|length,
            'icon': 'fas fa-clock',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Total Revenue',
            'value': '$' ~ (orders|filter(order => order.status == 'completed')|reduce((carry, order) => carry + order.amount, 0)|number_format(2)),
            'icon': 'fas fa-dollar-sign',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Order ID'},
            {'text': 'Customer'},
            {'text': 'Plan'},
            {'text': 'Amount'},
            {'text': 'Payment Method'},
            {'text': 'Status'},
            {'text': 'Date'},
            {'text': 'Actions', 'style': 'width: 150px;'}
        ] %}

        {% set table_rows = [] %}
        {% for order in orders %}
            {% set row_cells = [
                {
                    'content': '<span class="badge bg-secondary">#' ~ order.id ~ '</span>'
                },
                {
                    'content': '<h6 class="order-customer mb-0 font-weight-bold text-dark">' ~ (order.user ? order.user.fullName : 'Guest') ~ '</h6>' ~
                               (order.user and order.user.email ? '<small class="text-muted d-block">' ~ order.user.email ~ '</small>' : '')
                },
                {
                    'content': order.course ?
                        '<span class="text-dark">' ~ order.course.title ~ '</span>' :
                        '<span class="text-muted">No course</span>'
                },
                {
                    'content': '<span class="text-success font-weight-bold">$' ~ order.amount|number_format(2) ~ '</span>'
                },
                {
                    'content': order.paymentMethod ?
                        '<span class="badge bg-info">' ~ order.paymentMethod|title ~ '</span>' :
                        '<span class="text-muted">Unknown</span>'
                },
                {
                    'content': '<span class="badge ' ~
                        (order.status == 'completed' ? 'bg-success' :
                         order.status == 'pending' ? 'bg-warning' :
                         order.status == 'failed' ? 'bg-danger' : 'bg-secondary') ~
                        '">' ~ order.status|title ~ '</span>'
                },
                {
                    'content': '<small class="text-muted">' ~ order.createdAt|date('M d, Y H:i') ~ '</small>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_order_show', {'id': order.id}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Order"><i class="fas fa-eye"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Refund Order" onclick="refundOrder(' ~ order.id ~ ', \'' ~ order.id ~ '\')"><i class="fas fa-undo"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'order-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'order-row',
            'empty_message': 'No orders found',
            'empty_icon': 'fas fa-shopping-cart',
            'empty_description': 'Orders will appear here when customers make purchases.',
            'search_config': {
                'fields': ['.order-customer']
            }
        } %}
    {% endblock %}
{% endembed %}

<!-- Order Refund Modal -->
<div class="modal fade" id="orderRefundModal" tabindex="-1" aria-labelledby="orderRefundModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderRefundModalLabel">Refund Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to refund this order?</p>
                <p><strong>Order ID:</strong> <span id="refundOrderId"></span></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This will process a refund through the payment gateway and revoke user access.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmRefundAction">Process Refund</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
let currentOrderId = null;

function refundOrder(orderId, orderNumber) {
    currentOrderId = orderId;

    document.getElementById('refundOrderId').textContent = '#' + orderNumber;

    const modal = new bootstrap.Modal(document.getElementById('orderRefundModal'));
    modal.show();
}

document.getElementById('confirmRefundAction').addEventListener('click', function() {
    if (currentOrderId) {
        // Create and submit form for refund
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/orders/${currentOrderId}/refund`;

        document.body.appendChild(form);
        form.submit();
    }
});
</script>
{% endblock %}
