{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Instructor',
    'entity_title': instructor.name,
    'entity_code': instructor.emailPrefix,
    'entity_icon': 'fas fa-chalkboard-teacher',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_instructor_index', 'title': 'Instructors'},
        {'title': instructor.name, 'active': true}
    ],

    'back_path': path('admin_instructor_index'),
    'edit_path': path('admin_instructor_edit', {'emailPrefix': instructor.emailPrefix}),
    'print_function': 'printInstructorDetails'
} %}

{% block preview_content %}

    <!-- Profile Picture Section -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <div class="profile-picture-display" style="display: inline-block;">
                {% if instructor.profileImage %}
                    <img src="{{ asset('uploads/instructors/' ~ instructor.profileImage) }}"
                         alt="{{ instructor.name }}"
                         class="rounded-circle shadow-lg"
                         style="width: 150px; height: 150px; object-fit: cover; border: 4px solid #011a2d;">
                {% else %}
                    <div class="rounded-circle shadow-lg d-flex align-items-center justify-content-center"
                         style="width: 150px; height: 150px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-size: 3rem; font-weight: bold;">
                        {{ instructor.name|split(' ')|first|first|upper }}{{ instructor.name|split(' ')|last|first|upper }}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Instructor Profile Information -->
    <div class="row">
        <!-- Name and Email -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-user text-primary mr-1"></i>
                    Full Name
                </label>
                <div class="enhanced-display-field">
                    {{ instructor.name }}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-envelope text-primary mr-1"></i>
                    Email Address
                </label>
                <div class="enhanced-display-field">
                    <a href="mailto:{{ instructor.email }}" class="text-decoration-none" style="color: #011a2d;">{{ instructor.email }}</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Specialization (Full Width) -->
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-star text-primary mr-1"></i>
            Specialization
        </label>
        <div class="enhanced-display-field">
            {{ instructor.specialization ?? 'Not specified' }}
        </div>
    </div>

    <!-- Phone and Display Order Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-phone text-primary mr-1"></i>
                    Phone
                </label>
                <div class="enhanced-display-field">
                    {{ instructor.phone ?? 'Not provided' }}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-sort-numeric-up text-primary mr-1"></i>
                    Display Order
                </label>
                <div class="enhanced-display-field">
                    {{ instructor.displayOrder ?? 'Not set' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Biography -->
    {% if instructor.bio %}
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-file-text text-primary mr-1"></i>
            Biography
        </label>
        <div class="enhanced-display-field" style="line-height: 1.6; min-height: 120px;">
            {{ instructor.bio|nl2br }}
        </div>
    </div>
    {% endif %}

    <!-- Qualifications -->
    {% if instructor.qualifications|length > 0 %}
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-graduation-cap text-primary mr-1"></i>
            Qualifications
        </label>
        <div class="enhanced-display-field">
            <ul class="list-unstyled mb-0">
                {% for qualification in instructor.qualifications %}
                    <li class="mb-2">
                        <i class="fas fa-certificate text-primary me-2"></i>{{ qualification }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Achievements -->
    {% if instructor.achievements|length > 0 %}
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-trophy text-primary mr-1"></i>
            Achievements
        </label>
        <div class="enhanced-display-field">
            <ul class="list-unstyled mb-0">
                {% for achievement in instructor.achievements %}
                    <li class="mb-2">
                        <i class="fas fa-award text-warning me-2"></i>{{ achievement }}
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- LinkedIn URL -->
    {% if instructor.linkedinUrl %}
    <div class="form-group">
        <label class="form-label">
            <i class="fab fa-linkedin text-primary mr-1"></i>
            LinkedIn Profile
        </label>
        <div class="enhanced-display-field">
            <a href="{{ instructor.linkedinUrl }}" target="_blank" class="text-decoration-none" style="color: #011a2d;">
                <i class="fas fa-external-link-alt me-2"></i>View LinkedIn Profile
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Status and Created Date Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-toggle-on text-primary mr-1"></i>
                    Status
                </label>
                <div class="enhanced-display-field">
                    {% if instructor.isActive %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar text-primary mr-1"></i>
                    Created Date
                </label>
                <div class="enhanced-display-field">
                    {% if instructor.createdAt %}
                        {{ instructor.createdAt|date('F j, Y \\a\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    display: flex !important;
    align-items: center !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printInstructorDetails() {
    window.print();
}
</script>
{% endblock %}
