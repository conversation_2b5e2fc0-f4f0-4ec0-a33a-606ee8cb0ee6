<?php

namespace App\Repository;

use App\Entity\Course;
use App\Entity\CourseModule;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CourseModule>
 */
class CourseModuleRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseModule::class);
    }

    /**
     * Find module by code
     */
    public function findByCode(string $code): ?CourseModule
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.code = :code')
            ->andWhere('cm.is_active = :active')
            ->setParameter('code', $code)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find modules by course, ordered by sort_order
     */
    public function findByCourseOrdered($course): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.course = :course')
            ->andWhere('cm.is_active = :active')
            ->setParameter('course', $course)
            ->setParameter('active', true)
            ->orderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all modules by course (including inactive)
     */
    public function findAllByCourse(Course $course): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.course = :course')
            ->setParameter('course', $course)
            ->orderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get next sort order for a course
     */
    public function getNextSortOrder(Course $course): int
    {
        $result = $this->createQueryBuilder('cm')
            ->select('MAX(cm.sort_order)')
            ->andWhere('cm.course = :course')
            ->setParameter('course', $course)
            ->getQuery()
            ->getSingleScalarResult();

        return ($result ?? 0) + 1;
    }

    /**
     * Get module statistics for a course
     */
    public function getModuleStats(Course $course): array
    {
        $qb = $this->createQueryBuilder('cm');

        $totalModules = $qb->select('COUNT(cm.id)')
            ->andWhere('cm.course = :course')
            ->setParameter('course', $course)
            ->getQuery()
            ->getSingleScalarResult();

        $activeModules = $qb->select('COUNT(cm.id)')
            ->andWhere('cm.course = :course')
            ->andWhere('cm.is_active = :active')
            ->setParameter('course', $course)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        $totalDuration = $qb->select('SUM(cm.duration)')
            ->andWhere('cm.course = :course')
            ->andWhere('cm.is_active = :active')
            ->setParameter('course', $course)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total' => $totalModules,
            'active' => $activeModules,
            'total_duration' => $totalDuration ?? 0
        ];
    }
}
