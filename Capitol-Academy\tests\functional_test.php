<?php

/**
 * Comprehensive Functional Test Script for Capitol Academy
 * 
 * This script tests all major functionality to ensure everything works correctly
 * Run this script after implementing all fixes to verify the system
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use App\Kernel;

class FunctionalTester
{
    private HttpKernelInterface $kernel;
    private array $testResults = [];

    public function __construct()
    {
        $this->kernel = new Kernel('test', true);
        $this->kernel->boot();
    }

    public function runAllTests(): void
    {
        echo "🚀 Starting Capitol Academy Functional Tests\n";
        echo "=" . str_repeat("=", 50) . "\n\n";

        $this->testDatabaseConnection();
        $this->testHomepageAccess();
        $this->testAdminRoutes();
        $this->testUserRoutes();
        $this->testAssetService();
        $this->testValidationService();
        $this->testIpAddressService();
        $this->testErrorHandling();
        $this->testSecurity();

        $this->displayResults();
    }

    private function testDatabaseConnection(): void
    {
        echo "📊 Testing Database Connection...\n";
        
        try {
            $container = $this->kernel->getContainer();
            $entityManager = $container->get('doctrine.orm.entity_manager');
            
            // Test basic connection
            $connection = $entityManager->getConnection();
            $connection->connect();
            
            // Test entity repositories
            $userRepo = $entityManager->getRepository('App\Entity\User');
            $adminRepo = $entityManager->getRepository('App\Entity\Admin');
            $courseRepo = $entityManager->getRepository('App\Entity\Course');
            $contactRepo = $entityManager->getRepository('App\Entity\Contact');
            
            $this->addResult('Database Connection', true, 'Successfully connected to database');
            $this->addResult('Entity Repositories', true, 'All repositories accessible');
            
        } catch (\Exception $e) {
            $this->addResult('Database Connection', false, $e->getMessage());
        }
    }

    private function testHomepageAccess(): void
    {
        echo "🏠 Testing Homepage Access...\n";
        
        try {
            $request = Request::create('/', 'GET');
            $response = $this->kernel->handle($request);
            
            $success = $response->getStatusCode() === 200;
            $message = $success ? 'Homepage loads successfully' : 'Homepage returned status ' . $response->getStatusCode();
            
            $this->addResult('Homepage Access', $success, $message);
            
        } catch (\Exception $e) {
            $this->addResult('Homepage Access', false, $e->getMessage());
        }
    }

    private function testAdminRoutes(): void
    {
        echo "👨‍💼 Testing Admin Routes...\n";
        
        $adminRoutes = [
            '/admin/login' => 'Admin Login Page',
            '/admin/dashboard' => 'Admin Dashboard',
            '/admin/courses' => 'Course Management',
            '/admin/users' => 'User Management',
            '/admin/contacts' => 'Contact Management',
        ];

        foreach ($adminRoutes as $route => $description) {
            try {
                $request = Request::create($route, 'GET');
                $response = $this->kernel->handle($request);
                
                // Admin routes should either load (200) or redirect to login (302)
                $success = in_array($response->getStatusCode(), [200, 302]);
                $message = $success ? "$description accessible" : "Status: " . $response->getStatusCode();
                
                $this->addResult($description, $success, $message);
                
            } catch (\Exception $e) {
                $this->addResult($description, false, $e->getMessage());
            }
        }
    }

    private function testUserRoutes(): void
    {
        echo "👤 Testing User Routes...\n";
        
        $userRoutes = [
            '/login' => 'User Login Page',
            '/register' => 'User Registration Page',
            '/contact' => 'Contact Page',
            '/courses' => 'Courses Page',
        ];

        foreach ($userRoutes as $route => $description) {
            try {
                $request = Request::create($route, 'GET');
                $response = $this->kernel->handle($request);
                
                $success = $response->getStatusCode() === 200;
                $message = $success ? "$description loads successfully" : "Status: " . $response->getStatusCode();
                
                $this->addResult($description, $success, $message);
                
            } catch (\Exception $e) {
                $this->addResult($description, false, $e->getMessage());
            }
        }
    }

    private function testAssetService(): void
    {
        echo "🖼️ Testing Asset Service...\n";
        
        try {
            $container = $this->kernel->getContainer();
            $assetService = $container->get('App\Service\AssetService');
            
            // Test logo URL generation
            $logoUrl = $assetService->getLogoUrl('main');
            $this->addResult('Logo URL Generation', !empty($logoUrl), 'Logo URL: ' . $logoUrl);
            
            // Test profile image fallback
            $profileUrl = $assetService->getProfileImageUrl(null, 'admin');
            $this->addResult('Profile Image Fallback', !empty($profileUrl), 'Profile fallback works');
            
            // Test asset existence check
            $exists = $assetService->assetExists('/images/logo.png');
            $this->addResult('Asset Existence Check', is_bool($exists), 'Asset check returns boolean');
            
        } catch (\Exception $e) {
            $this->addResult('Asset Service', false, $e->getMessage());
        }
    }

    private function testValidationService(): void
    {
        echo "✅ Testing Validation Service...\n";
        
        try {
            $container = $this->kernel->getContainer();
            $validationService = $container->get('App\Service\ValidationService');
            
            // Test email validation
            $emailErrors = $validationService->validateEmail('invalid-email');
            $this->addResult('Email Validation', !empty($emailErrors), 'Invalid email detected');
            
            $emailErrors = $validationService->validateEmail('<EMAIL>');
            $this->addResult('Valid Email', empty($emailErrors), 'Valid email passes');
            
            // Test password validation
            $passwordErrors = $validationService->validatePassword('weak');
            $this->addResult('Weak Password Detection', !empty($passwordErrors), 'Weak password detected');
            
            $passwordErrors = $validationService->validatePassword('StrongP@ssw0rd123');
            $this->addResult('Strong Password', count($passwordErrors) <= 1, 'Strong password validation');
            
        } catch (\Exception $e) {
            $this->addResult('Validation Service', false, $e->getMessage());
        }
    }

    private function testIpAddressService(): void
    {
        echo "🌐 Testing IP Address Service...\n";
        
        try {
            $container = $this->kernel->getContainer();
            $ipService = $container->get('App\Service\IpAddressService');
            
            $request = Request::create('/', 'GET');
            $ip = $ipService->getClientIpAddress($request);
            
            $this->addResult('IP Address Detection', !empty($ip), 'IP detected: ' . $ip);
            
            $ipInfo = $ipService->getIpWithInfo($request);
            $this->addResult('IP Info Generation', is_array($ipInfo), 'IP info is array');
            
        } catch (\Exception $e) {
            $this->addResult('IP Address Service', false, $e->getMessage());
        }
    }

    private function testErrorHandling(): void
    {
        echo "🚨 Testing Error Handling...\n";
        
        try {
            $container = $this->kernel->getContainer();
            $errorService = $container->get('App\Service\ErrorHandlingService');
            
            // Test error formatting
            $errors = ['field1' => 'Error 1', 'field2' => ['Error 2', 'Error 3']];
            $formatted = $errorService->formatValidationErrors($errors);
            
            $this->addResult('Error Formatting', is_array($formatted), 'Errors formatted correctly');
            
        } catch (\Exception $e) {
            $this->addResult('Error Handling Service', false, $e->getMessage());
        }
    }

    private function testSecurity(): void
    {
        echo "🔒 Testing Security Features...\n";
        
        try {
            // Test CSRF protection
            $request = Request::create('/admin/login', 'POST');
            $response = $this->kernel->handle($request);
            
            // Should not be 200 without proper CSRF token
            $success = $response->getStatusCode() !== 200;
            $this->addResult('CSRF Protection', $success, 'CSRF protection active');
            
            // Test XSS protection in validation
            $container = $this->kernel->getContainer();
            $validationService = $container->get('App\Service\ValidationService');
            
            $xssErrors = $validationService->validateTextInput('<script>alert("xss")</script>');
            $this->addResult('XSS Protection', !empty($xssErrors), 'XSS attempt blocked');
            
        } catch (\Exception $e) {
            $this->addResult('Security Features', false, $e->getMessage());
        }
    }

    private function addResult(string $test, bool $success, string $message): void
    {
        $this->testResults[] = [
            'test' => $test,
            'success' => $success,
            'message' => $message
        ];
        
        $status = $success ? '✅' : '❌';
        echo "  $status $test: $message\n";
    }

    private function displayResults(): void
    {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 TEST RESULTS SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        
        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, fn($r) => $r['success']));
        $failed = $total - $passed;
        
        echo "Total Tests: $total\n";
        echo "✅ Passed: $passed\n";
        echo "❌ Failed: $failed\n";
        echo "Success Rate: " . round(($passed / $total) * 100, 2) . "%\n\n";
        
        if ($failed > 0) {
            echo "❌ FAILED TESTS:\n";
            echo str_repeat("-", 40) . "\n";
            foreach ($this->testResults as $result) {
                if (!$result['success']) {
                    echo "• {$result['test']}: {$result['message']}\n";
                }
            }
        } else {
            echo "🎉 ALL TESTS PASSED! Capitol Academy is ready for production.\n";
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $tester = new FunctionalTester();
    $tester->runAllTests();
}
