@CHARSET "UTF-8";
/******* GENERAL RESET *******/
  #container ul, li{



margin:0pt;
padding:0pt;

}


.clear{
	clear: both;
	height: 0;
	visibility: hidden;
	display: block;
}
a{
	text-decoration: none;
}
/******* GENERAL RESET *******/
/******* LOGO *******/
#logo{
	margin-top: 1em;
	display: block;
}
/******* /LOGO  *******/
/******* MENU *******/
#container{
	
	width: 348px;
	
	
	
}
#container ul{
	list-style: none;
	list-style-position: outside;
}
#container ul.menu li{
	float: left;
	margin-left: 5px;
	margin-bottom: -1px;
	 border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
	
}
#container ul.menu li{
	font-weight: 700;
	display: block;
	padding: 5px 7px;
	background: #efefef;
	
	border: 1px solid #d0ccc9;
	border-width: 1px 1px 1px 1px;
	position: relative;
	color: #9f9f9f;
	cursor: pointer;
	 border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
	width:95px;
	text-align:center;
	
}
#container ul.menu li.active{
	background: #fff;
	top: 1px;
	border-bottom: 0;
	color: #504f4f;
	border-top:3px solid #9d0e12;
	padding-top:3px;
	 
}
/******* /MENU *******/
/******* CONTENT *******/
.content{
	margin:0;
	background: #efefef;
	background: #fff;
	border: 1px solid #d0ccc9;
	
	padding: 10px;
	padding-bottom: 20px;
	font-size: 11px;
	 border-radius:  0 0 5px 5px; 
    -moz-border-radius:  0 0 5px 5px;
   
	-webkit-border-bottom-left-radius: 5px;
    -webkit-border-bottom-right-radius: 5px;
	width:321px;
	
	
	
}

/******* /CONTENT *******/
/******* NEWS *******/

.content.news{
	display: block;
	direction:ltr;
	height:235px;
}
/******* /NEWS *******/
/******* TUTORIALS *******/

.content.tutorials{
	display: none;
	padding-top:25px ;
	height:220px;
	direction:ltr;
}
/******* /TUTORIALS *******/
.content.links{
	display: none;
	height:235px;
}
.content.links a{
	color: #5f95ef;
}
/******* /LINKS *******/