{% extends 'base.html.twig' %}

{% block title %}Shopping Cart - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .cart-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .cart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .cart-header {
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .cart-content {
            padding: 40px;
        }

        .cart-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .cart-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .item-thumbnail {
            width: 80px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
        }

        .item-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #a90418;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: #011a2d;
            color: white;
            border-color: #011a2d;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 5px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 0.85rem;
            transition: background 0.3s ease;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .cart-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            position: sticky;
            top: 100px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
        }

        .summary-row.total {
            border-top: 2px solid #dee2e6;
            padding-top: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            color: #011a2d;
        }

        .checkout-btn {
            background: #a90418;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .checkout-btn:hover {
            background: #8a0315;
            transform: translateY(-2px);
        }

        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .empty-cart {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-cart i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #dee2e6;
        }

        .continue-shopping {
            background: #011a2d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .continue-shopping:hover {
            background: #0d2a42;
            color: white;
            text-decoration: none;
        }

        .alert-removed-items {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .cart-content {
                padding: 20px;
            }
            
            .cart-item {
                padding: 15px;
            }
            
            .item-thumbnail {
                width: 60px;
                height: 45px;
            }
            
            .quantity-controls {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class="cart-section">
        <div class="container">
            <div class="cart-container">
                <div class="cart-header">
                    <h1 class="mb-3">
                        <i class="fas fa-shopping-cart me-3"></i>Shopping Cart
                    </h1>
                    <p class="mb-0">Review your selected items before checkout</p>
                </div>

                <div class="cart-content">
                    {% if removed_items|length > 0 %}
                        <div class="alert-removed-items">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Items Removed</h6>
                            <p class="mb-0">
                                {{ removed_items|length }} item(s) were removed from your cart because they are no longer available.
                            </p>
                        </div>
                    {% endif %}

                    {% if cart.items|length > 0 %}
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="cart-items">
                                    {% for item_key, item in cart.items %}
                                        <div class="cart-item" data-item-key="{{ item_key }}">
                                            <div class="row align-items-center">
                                                <div class="col-md-2">
                                                    <div class="item-thumbnail">
                                                        {% if item.thumbnail %}
                                                            <img src="{{ asset('uploads/' ~ item.type ~ 's/thumbnails/' ~ item.thumbnail) }}" 
                                                                 alt="{{ item.title }}">
                                                        {% else %}
                                                            <div class="d-flex align-items-center justify-content-center h-100">
                                                                <i class="fas fa-play text-white"></i>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-4">
                                                    <div class="item-info">
                                                        <h6>{{ item.title }}</h6>
                                                        <span class="item-type">{{ item.type|replace({'_': ' '})|title }}</span>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-2">
                                                    <div class="item-price">
                                                        ${{ item.price|number_format(2) }}
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-2">
                                                    <div class="quantity-controls">
                                                        <button class="quantity-btn" onclick="updateQuantity('{{ item.type }}', {{ item.id }}, {{ item.quantity - 1 }})">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                        <input type="number" 
                                                               class="quantity-input" 
                                                               value="{{ item.quantity }}" 
                                                               min="1" 
                                                               onchange="updateQuantity('{{ item.type }}', {{ item.id }}, this.value)">
                                                        <button class="quantity-btn" onclick="updateQuantity('{{ item.type }}', {{ item.id }}, {{ item.quantity + 1 }})">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-2 text-end">
                                                    <button class="remove-btn" onclick="removeItem('{{ item.type }}', {{ item.id }})">
                                                        <i class="fas fa-trash me-1"></i>Remove
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <div class="mt-4">
                                    <a href="{{ path('app_premium_videos') }}" class="continue-shopping">
                                        <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                                    </a>
                                    
                                    <button class="btn btn-outline-danger ms-3" onclick="clearCart()">
                                        <i class="fas fa-trash me-2"></i>Clear Cart
                                    </button>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="cart-summary">
                                    <h5 class="mb-4">Order Summary</h5>
                                    
                                    <div class="summary-row">
                                        <span>Items ({{ cart.total_items }})</span>
                                        <span>${{ cart.subtotal|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class="summary-row">
                                        <span>Tax</span>
                                        <span>${{ cart.tax|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class="summary-row total">
                                        <span>Total</span>
                                        <span>${{ cart.total|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class="mt-4">
                                        {% if app.user %}
                                            <button class="checkout-btn" onclick="alert('Checkout functionality will be implemented in the next step')">
                                                <i class="fas fa-credit-card me-2"></i>Proceed to Checkout
                                            </button>
                                        {% else %}
                                            <p class="text-muted text-center mb-3">Please log in to checkout</p>
                                            <a href="{{ path('app_login') }}" class="checkout-btn text-decoration-none text-center d-block">
                                                <i class="fas fa-sign-in-alt me-2"></i>Login to Checkout
                                            </a>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="mt-3 text-center">
                                        <small class="text-muted">
                                            <i class="fas fa-shield-alt me-1"></i>
                                            Secure checkout with PayPal
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="empty-cart">
                            <i class="fas fa-shopping-cart"></i>
                            <h3>Your cart is empty</h3>
                            <p class="mb-4">Looks like you haven't added any items to your cart yet.</p>
                            <a href="{{ path('app_premium_videos') }}" class="continue-shopping">
                                <i class="fas fa-video me-2"></i>Browse Videos
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function updateQuantity(type, id, quantity) {
            quantity = parseInt(quantity);
            if (quantity < 0) return;

            fetch('{{ path('app_cart_update') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=${type}&id=${id}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to update cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the cart');
            });
        }

        function removeItem(type, id) {
            if (!confirm('Are you sure you want to remove this item from your cart?')) {
                return;
            }

            fetch('{{ path('app_cart_remove') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=${type}&id=${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to remove item');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while removing the item');
            });
        }

        function clearCart() {
            if (!confirm('Are you sure you want to clear your entire cart?')) {
                return;
            }

            fetch('{{ path('app_cart_clear') }}', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to clear cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while clearing the cart');
            });
        }
    </script>
{% endblock %}
