<?php

namespace App\Repository;

use App\Entity\Partner;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Partner>
 */
class PartnerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Partner::class);
    }

    /**
     * Find all active partners ordered by display order
     */
    public function findActivePartnersOrdered(): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.isActive = :active')
            ->setParameter('active', true)
            ->orderBy('p.displayOrder', 'ASC')
            ->addOrderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all partners ordered by display order
     */
    public function findAllOrdered(): array
    {
        return $this->createQueryBuilder('p')
            ->orderBy('p.displayOrder', 'ASC')
            ->addOrderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find partners with search functionality
     */
    public function findWithSearch(string $search = ''): array
    {
        $qb = $this->createQueryBuilder('p');

        if (!empty($search)) {
            $qb->where('p.name LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        return $qb->orderBy('p.displayOrder', 'ASC')
                  ->addOrderBy('p.name', 'ASC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Get the next display order value
     */
    public function getNextDisplayOrder(): int
    {
        $result = $this->createQueryBuilder('p')
            ->select('MAX(p.displayOrder)')
            ->getQuery()
            ->getSingleScalarResult();

        return ($result ?? 0) + 1;
    }

    /**
     * Update display orders for reordering
     */
    public function updateDisplayOrders(array $partnerIds): void
    {
        $em = $this->getEntityManager();
        
        foreach ($partnerIds as $order => $partnerId) {
            $em->createQuery('UPDATE App\Entity\Partner p SET p.displayOrder = :order WHERE p.id = :id')
               ->setParameter('order', $order + 1)
               ->setParameter('id', $partnerId)
               ->execute();
        }
    }

    /**
     * Count active partners
     */
    public function countActivePartners(): int
    {
        return $this->createQueryBuilder('p')
            ->select('COUNT(p.id)')
            ->where('p.isActive = :active')
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get partner statistics
     */
    public function getPartnerStats(): array
    {
        $total = $this->count([]);
        $active = $this->countActivePartners();

        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active
        ];
    }

    /**
     * Find partner by slug (generated from name)
     */
    public function findBySlug(string $slug): ?Partner
    {
        $partners = $this->findAll();

        foreach ($partners as $partner) {
            if ($partner->getSlug() === $slug) {
                return $partner;
            }
        }

        return null;
    }
}
