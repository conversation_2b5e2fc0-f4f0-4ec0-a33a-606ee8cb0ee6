<?php

namespace App\Service;

use App\Entity\Admin;
use Symfony\Component\Security\Core\Security;

/**
 * Service for checking admin permissions throughout the application
 */
class AdminPermissionService
{
    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    /**
     * Check if current admin has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        $admin = $this->getCurrentAdmin();
        if (!$admin) {
            return false;
        }

        return $admin->hasPermission($permission);
    }

    /**
     * Check if current admin is master admin
     */
    public function isMasterAdmin(): bool
    {
        $admin = $this->getCurrentAdmin();
        if (!$admin) {
            return false;
        }

        return $admin->isMasterAdmin();
    }

    /**
     * Get current admin user
     */
    public function getCurrentAdmin(): ?Admin
    {
        $user = $this->security->getUser();
        return $user instanceof Admin ? $user : null;
    }

    /**
     * Check multiple permissions (OR logic - user needs at least one)
     */
    public function hasAnyPermission(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check multiple permissions (AND logic - user needs all)
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get all permissions for current admin
     */
    public function getCurrentAdminPermissions(): array
    {
        $admin = $this->getCurrentAdmin();
        if (!$admin) {
            return [];
        }

        return $admin->getPermissions();
    }

    /**
     * Check if admin can access users section
     */
    public function canAccessUsers(): bool
    {
        return $this->hasPermission('users.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage users
     */
    public function canManageUsers(): bool
    {
        return $this->hasAnyPermission(['users.create', 'users.edit', 'users.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access courses section
     */
    public function canAccessCourses(): bool
    {
        return $this->hasPermission('courses.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access plans section
     */
    public function canAccessPlans(): bool
    {
        return $this->hasPermission('plans.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage plans
     */
    public function canManagePlans(): bool
    {
        return $this->hasAnyPermission(['plans.create', 'plans.edit', 'plans.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage courses
     */
    public function canManageCourses(): bool
    {
        return $this->hasAnyPermission(['courses.create', 'courses.edit', 'courses.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access contacts section
     */
    public function canAccessContacts(): bool
    {
        return $this->hasPermission('contacts.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage contacts
     */
    public function canManageContacts(): bool
    {
        return $this->hasAnyPermission(['contacts.process', 'contacts.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access admin management section
     */
    public function canAccessAdminManagement(): bool
    {
        return $this->hasPermission('admin.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access market analysis section
     */
    public function canAccessMarketAnalysis(): bool
    {
        return $this->hasPermission('market_analysis.view') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage market analysis
     */
    public function canManageMarketAnalysis(): bool
    {
        return $this->hasAnyPermission(['market_analysis.create', 'market_analysis.edit', 'market_analysis.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage other admins
     */
    public function canManageAdmins(): bool
    {
        return $this->hasAnyPermission(['admin.create', 'admin.edit', 'admin.delete']) || $this->isMasterAdmin();
    }

    /**
     * Check if admin can access enrollments section
     */
    public function canAccessEnrollments(): bool
    {
        return $this->hasPermission('enrollments.read') || $this->isMasterAdmin();
    }

    /**
     * Check if admin can manage enrollments
     */
    public function canManageEnrollments(): bool
    {
        return $this->hasAnyPermission(['enrollments.create', 'enrollments.edit', 'enrollments.delete']) || $this->isMasterAdmin();
    }

    /**
     * Get permission-based navigation items
     */
    public function getNavigationItems(): array
    {
        $items = [];

        // Dashboard is always available
        $items[] = [
            'name' => 'Dashboard',
            'route' => 'admin_dashboard',
            'icon' => 'fas fa-tachometer-alt',
            'active' => true
        ];

        // Users section
        if ($this->canAccessUsers()) {
            $items[] = [
                'name' => 'Users',
                'route' => 'admin_users',
                'icon' => 'fas fa-users',
                'active' => true
            ];
        }

        // Courses section
        if ($this->canAccessCourses()) {
            $items[] = [
                'name' => 'Courses',
                'route' => 'admin_courses',
                'icon' => 'fas fa-graduation-cap',
                'active' => true
            ];
        }

        // Plans section
        if ($this->canAccessPlans()) {
            $items[] = [
                'name' => 'Plans',
                'route' => 'admin_plans',
                'icon' => 'fas fa-layer-group',
                'active' => true
            ];
        }

        // Enrollments section
        if ($this->canAccessEnrollments()) {
            $items[] = [
                'name' => 'Enrollments',
                'route' => 'admin_enrollments_list',
                'icon' => 'fas fa-users',
                'active' => true
            ];
        }

        // Contacts section
        if ($this->canAccessContacts()) {
            $items[] = [
                'name' => 'Contacts',
                'route' => 'admin_contacts',
                'icon' => 'fas fa-envelope',
                'active' => true
            ];
        }

        // Market Analysis section
        if ($this->canAccessMarketAnalysis()) {
            $items[] = [
                'name' => 'Market Analysis',
                'route' => 'admin_market_analysis_index',
                'icon' => 'fas fa-chart-bar',
                'active' => true
            ];
        }

        // Admin management section
        if ($this->canAccessAdminManagement()) {
            $items[] = [
                'name' => 'Admins',
                'route' => 'admin_admins',
                'icon' => 'fas fa-users-cog',
                'active' => true
            ];
        }

        return $items;
    }

    /**
     * Get permission-based action buttons for entities
     */
    public function getEntityActions(string $entityType, $entity = null): array
    {
        $actions = [];

        switch ($entityType) {
            case 'user':
                if ($this->hasPermission('users.view')) {
                    $actions[] = ['type' => 'view', 'permission' => 'users.view'];
                }
                if ($this->hasPermission('users.edit')) {
                    $actions[] = ['type' => 'edit', 'permission' => 'users.edit'];
                }
                if ($this->hasPermission('users.delete')) {
                    $actions[] = ['type' => 'delete', 'permission' => 'users.delete'];
                }
                break;

            case 'course':
                if ($this->hasPermission('courses.view')) {
                    $actions[] = ['type' => 'view', 'permission' => 'courses.view'];
                }
                if ($this->hasPermission('courses.edit')) {
                    $actions[] = ['type' => 'edit', 'permission' => 'courses.edit'];
                }
                if ($this->hasPermission('courses.delete')) {
                    $actions[] = ['type' => 'delete', 'permission' => 'courses.delete'];
                }
                break;

            case 'plan':
                if ($this->hasPermission('plans.view')) {
                    $actions[] = ['type' => 'view', 'permission' => 'plans.view'];
                }
                if ($this->hasPermission('plans.edit')) {
                    $actions[] = ['type' => 'edit', 'permission' => 'plans.edit'];
                }
                if ($this->hasPermission('plans.delete')) {
                    $actions[] = ['type' => 'delete', 'permission' => 'plans.delete'];
                }
                break;

            case 'contact':
                if ($this->hasPermission('contacts.view')) {
                    $actions[] = ['type' => 'view', 'permission' => 'contacts.view'];
                }
                if ($this->hasPermission('contacts.process')) {
                    $actions[] = ['type' => 'process', 'permission' => 'contacts.process'];
                }
                if ($this->hasPermission('contacts.delete')) {
                    $actions[] = ['type' => 'delete', 'permission' => 'contacts.delete'];
                }
                break;

            case 'admin':
                if ($this->hasPermission('admin.view')) {
                    $actions[] = ['type' => 'view', 'permission' => 'admin.view'];
                }
                if ($this->hasPermission('admin.edit')) {
                    $actions[] = ['type' => 'edit', 'permission' => 'admin.edit'];
                }
                if ($this->hasPermission('admin.delete')) {
                    $actions[] = ['type' => 'delete', 'permission' => 'admin.delete'];
                }
                break;
        }

        return $actions;
    }



    /**
     * Get all available permissions
     */
    public function getAllPermissions(): array
    {
        return [
            'users.view' => 'View Users',
            'users.create' => 'Create Users',
            'users.edit' => 'Edit Users',
            'users.delete' => 'Delete Users',
            'courses.view' => 'View Courses',
            'courses.create' => 'Create Courses',
            'courses.edit' => 'Edit Courses',
            'courses.delete' => 'Delete Courses',
            'courses.modules' => 'Manage Course Modules',
            'plans.view' => 'View Plans',
            'plans.create' => 'Create Plans',
            'plans.edit' => 'Edit Plans',
            'plans.delete' => 'Delete Plans',
            'enrollments.read' => 'View Enrollments',
            'enrollments.create' => 'Create Enrollments',
            'enrollments.edit' => 'Edit Enrollments',
            'enrollments.delete' => 'Delete Enrollments',
            'contacts.view' => 'View Contacts',
            'contacts.process' => 'Process Contacts',
            'contacts.delete' => 'Delete Contacts',
            'market_analysis.view' => 'View Market Analysis',
            'market_analysis.create' => 'Create Market Analysis',
            'market_analysis.edit' => 'Edit Market Analysis',
            'market_analysis.delete' => 'Delete Market Analysis',
            'admin.view' => 'View Administrators',
            'admin.create' => 'Create Administrators',
            'admin.edit' => 'Edit Administrators',
            'admin.delete' => 'Delete Administrators',
        ];
    }

    /**
     * Throw permission denied exception with professional message
     */
    public function denyAccess(string $feature = 'this feature'): void
    {
        throw new \Symfony\Component\Security\Core\Exception\AccessDeniedException(
            "You do not have permission to access {$feature}. Please contact your administrator."
        );
    }
}
