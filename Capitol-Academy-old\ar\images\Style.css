@charset "utf-8";
/* CSS Document */

body { margin:0;
padding:0;
background-color:#F7F6F6;}
#header1{ background-image:url(background%20capitol.png);
	}
	a img, img {
border : 0;

}
.invalidInput, .error{
color:#8a1f11;
background-color:#fbe3e4;
}
.error,.notice{
padding:0.5em;
margin:0.5em;
}	
#contac { background-image:url(http://capitol-academy.com/ar/images/contac.png);
	}
#bg { background-image:url(background%20capitol.png) ;
background-repeat:no-repeat;}	
#b{ background-color:transparent;
border:0;
padding:0;
margin:0;
font-family: Tahoma;
    font-size: 14px;
    font-style: inherit;
    font-weight: inherit;
	line-height:21px;
color:#585858;}

button{
   
    cursor:pointer;
	
	
}
#contact{
	background-image:url(sp.png);
background-repeat:repeat-y;
background-position:left;
padding:0 10px 0 5px;}
 .contact {font-family: Tahoma;
    font-size: 13px;
    font-style: inherit;
    font-weight: bold;
	text-decoration::underline;
color:#3a3a3a;}

#flag{
	padding:0 1px 0 1px;}
.st { font-family:Tahoma;
font-style:inherit;
font-size:18px;
color:#9f1418;
font-weight:inherit;
padding:11px 15px;
text-decoration:none;}	

#c td{ padding:5px;}
	
	
#contacts{ background-image:url(contact.png);
font-family:Arial;
font-size:20px;
color:#3a3a3a;
}
#cotac{
	background-image:url(conta.png);
	font-family:Tahoma;
	font-size:11px;
	font-style:inherit;
	font-weight:bold;
	color:#626262;
}
.conta{
	font-family:Tahoma;
	font-size:12px;
	font-style:inherit;
	font-weight:bold;
	color:#626262;
	text-decoration:none;
}
#pro1 { background-image:url(case%20gris%20promotion1.png);}
#pro { background-image:url(case%20gris%20promotion.png);}	
#cor2 td.f{ border-bottom:none;}
#cor2 td { border-bottom:1px Dashed #999;
}

#cor2 { 
	padding:5px;
	 background-image:url(courses.png);
	 }
	 .link2 a {font-family:Tahoma;
font-size:12px;
color:#626262;
text-decoration:none;
font-weight:bold;


}
#cor3 td.f{ border-bottom:none;}
#cor3 td { border-bottom:1px Dashed #999;
}

#cor3 { 
	padding:5px;
	 background-image:url(courses2.png);
	 }

.link a {font-family:Tahoma;
font-size:14px;
color:#626262;
text-decoration:none;


}
#cor td.f{ border-bottom:none;}
#cor td { border-bottom:1px Dashed #999;
}

#cor { 
	padding:4px;
	 background-image:url(chapitre.png);
	 }
.link a {font-family:Tahoma;
font-size:12px;
color:#626262;
text-decoration:none;
font-weight:bold;


}

.titab1{ font-family:Tahoma;
font-size:15px;
color:#626262;
background-color:#FFF;
height:25px;
font-weight:inherit;
width:120px;
padding:8px;
border-left:1px solid #d0cfcf;
border-right:1px solid #d0cfcf;
margin:0 -5px -1px 0;
 border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;

}

.titab_s{
	font-family:Tahoma;
font-size:15px;
color:#626262;
background-color:#FFF;
height:25px;
font-weight:bold;
width:120px;
padding:8px;

margin:0 0 -9px 0 ;
border-top:4px solid #666;
border-left:1px solid #d0cfcf;
border-right:1px solid #d0cfcf;

 border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
	}
a, a:active { outline: none;
-moz-outline-style: none; }	
.titab_trs{
	font-family:Tahoma;
font-size:15px;
color:#626262;
background-color:#FFF;
height:25px;
font-weight:bold;
width:220px;
padding:8px;

margin:0 0 -8px 0 ;
border-top:4px solid #666;
border-left:1px solid #d0cfcf;
border-right:1px solid #d0cfcf;
border-bottom:1px solid #d0cfcf;
 border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
	}
	
.list1 li { line-height:25px;}	
	
.eng{ font-weight:bold;
color:#005781;
text-decoration:underline;

float:left;
}
.code_cou{
	text-align:left;
	padding-left:110px;}	
.titab{ font-family:Tahoma;
font-size:15px;
color:#005781;
padding:9px;}
.title{ font-family:Tahoma;
font-style:normal;
font-size:19px;
color:#005781;
font-weight:bold;
}
.titlefun{ font-family:Tahoma;
font-size:19px;

font-weight:bold;
}
 p, li { font-family: Tahoma;
    font-size: 14px;
    font-style: inherit;
    font-weight: inherit;
	line-height:21px;
color:#585858;
}
.ct{font-family: Tahoma;
    font-size: 12px;
    font-style: inherit;
    font-weight: inherit;
	
color:#585858;
}
.soustitle2{ font-family:Tahoma;
font-size:19px;
color:#3e3e3e;
font-weight:bold;}
.soustitle{ font-family:Tahoma;
font-size:15px;
color:#3e3e3e;
font-weight:bold;}
.cont{ font-family:Tahoma;
font-size:19px;
color:#585858;
}
.cont1{ font-family:Tahoma;
font-size:19px;
color:#0080FF;
text-decoration:none;
}
#bu{border-radius: 5px ;
    -moz-border-radius: 5px ;
    -webkit-border-radius: 5px;
   }
#ba{ padding-right:5px;}

.contf{
	font-family:Tahoma;
	font-style:inherit;
	font-size:12px;
	color:#585858;
	padding:10px;
	font-weight:bold;
}
#his a{ font-family:Tahoma;
font-style: inherit;
font-size:10px;
color:#808080;
font-weight:inherit;
text-decoration:none;

	}
#menu{ background-image:url(menu1.png);

}
.cor
{ font-family:Tahoma;
font-size:12px;
font-style:inherit;
text-align:justify;
color:#585858;
padding:110px 17px 0 17px;
direction:rtl;
line-height:normal;

	
	}
.int{
 font-family:Tahoma;
font-size:12px;
font-style:inherit;
text-align:justify;
color:#585858;
width:132px;
height:18px;
vertical-align:middle;


	border:1px solid #98999a;}	
.in{ width:130px;
height:15px;

	border:1px solid #98999a;}	
.cor2
{ font-family:Tahoma;
font-size:12px;
font-style:inherit;
text-align:justify;
color:#585858;
padding:110px 17px 0 17px;
direction:rtl;
line-height:normal;

	
	}
	
#bordure{
	border:1px solid #CDCDCD;
	padding:5px 9px 0 9px;
	border-radius: 5px ;
    -moz-border-radius: 5px ;
    -webkit-border-radius: 5px; }
	
.cor3
{ font-family:Tahoma;
font-size:12px;
font-style:inherit;
width:205px;
text-align:justify;
color:#585858;
padding:110px 18px 15px 0 ;
direction:rtl;
line-height:normal;

	
}
#sitmap { background-color: #3a3a3a; }
.code a{ font-family:Tahoma;
font-size:24px;
color:#005781;
text-decoration:none;
 background-image:url(sp1.png);
 background-repeat:repeat-y;
background-position:left;
padding: 0  0 0 8px; }
.code2 { font-family:Tahoma;
font-size:20px;


padding-left:8px;
text-decoration:none;
font-weight:normal;}

.pop{ font-family:Verdana;
font-size:22px;
color:#005781;
font-weight:bold;}
#cont{ direction:rtl;
padding:0 45px 0 0;
vertical-align:inherit; }
#cont2{ direction:rtl;
padding:0 45px 0 0;
font-size:17px;
font-weight:bold;  }
#cont3{ direction:rtl;
padding:0 45px 0 2px;

 }

.inp{ width:180px;

}
.inp_s{ width:163px;}
.hasPlaceholder, inp, inp_s {
   color: #777;
}
.inpbox{ padding:5px;}
.d{ width:60px;
font-family:Tahoma;
	font-style:inherit;
	font-size:11px;
	color:#585858;
	font-weight:bold;}
.list{ width:185px;
font-family:Tahoma;
	font-style:inherit;
	font-size:12px;
	color:#585858;
	font-weight:bold;}
a, a:active { outline: none;
-moz-outline-style: none; }
.tab1 table{-moz-border-radius: 10px;
-webkit-border-radius: 10px;
border-radius: 10px;}

.tab1 td, .tab1 th{ border:1px solid #999;
}
#sp { text-align:left; padding-right:15px; color:#898989; max-width:1px; }
.copy td { color:#ffffff; font-size:11px; font-family: Tahoma, Geneva, sans-serif;  line-height:12px; padding:4px 10px; font-weight:bold; }
.copy td a { color:#ffffff; font-size:11px; font-family: Tahoma, Geneva, sans-serif;  line-height:12px; padding:4px 5px; font-weight:bold; text-decoration:none;}
.copy td a:hover { text-decoration:underline;}
#ff:hover { text-decoration:underline;} 
#wom a:hover { text-decoration:underline;}
#wom { text-align:left; }
#wom a { color:#c6e14b; }
.dis{ border-bottom:1px solid #898989;
border-top:1px solid #898989;}
.sitemap2 { padding:5px;}
.sitemap2  th { border:0; text-align:right;}
.sitemap2   td  { text-align:right; color:#FFF;}
.sitemap2 th a { color:#ffffff; font-size:11px; font-family: Tahoma, Geneva, sans-serif; font-weight:bold; line-height:12px; text-decoration:none;}
.sitemap2 td a { color:#ababab; font-family:Tahoma; line-height:14px; text-decoration:none;
font-size:11px;}
.sitemap2 th a:hover { text-decoration:underline;}
.sitemap2 td a:hover { text-decoration:underline;}
.tab1 {

border-collapse:collapse;
margin:6px;


}
.bounus table{-moz-border-radius: 10px;
-webkit-border-radius: 10px;
border-radius: 10px;}

.bounus td, .bounus th{ border:1px dashed #999;
padding:5px;
text-align:center;
font-family:Tahoma;
	font-style:inherit;
	font-size:12px;
	color:#585858;
	padding:10px;
	font-weight:bold;
}

.bounus {

border-collapse:collapse;



}

.cour{
	font-family:Tahoma;
	font-style: inherit;
	font-size:11px;
	color:#585858;
	padding:5px;
	font-weight:bold;
	
}
.cour1{
	font-family:Tahoma;
	font-style: inherit;
	font-size:14px;
	color:#8A1A38;
	padding:5px;
	font-weight:bold;
}

.court{
	font-family:Tahoma;
	font-style: inherit;
	font-size:14px;
	color:#8A1A38;
	padding:5px;
	font-weight:bold;
	text-decoration:none;
}


 .tabc{
 border-collapse:collapse;
 background-color:#FFF;
  font-family: Tahoma;
    font-size: 12px;
    font-style: inherit;
    font-weight: bold;
	
	
color:#585858;
}
.tabc td { border-bottom: 1px solid #d0cfcf;
padding-right:5px;}
.tabc1{
 border-collapse:collapse;
}
.tabc1 td {
border-bottom: 3px solid #d0cfcf;}
.sugg { font-family:Tahoma;
font-size:19px;
color:#005781;
text-decoration:underline;}
#titim{vertical-align:middle; padding-bottom:4px;}


/* Menu */
/* Some stylesheet reset */
ul.nav, .nav ul {
	list-style: none;
	margin: 0;
	padding: 0;
	vertical-align: baseline;

}

/* The container */
.nav {
	display: block;
	position: relative;
	margin:0;
	padding:0;
	width:1000px;
	z-index:7;
}

/* The list elements which contain the links */
.nav>li {
	display: block;
	float: right;
	margin: 0;
	padding: 0;	
}

	/* General link styling */
	.nav>li>a, .nav ul li a {
		/* Layout */
		display: block;
		position: relative;
		
		margin: 0;
		padding: 5px 9px;

		/* Typography */
		font-family:Tahoma;
		color: #d8d8d8;
		text-decoration: none;

		
		font-size: 12px;
		background-color:#3a3a3a;
		font-weight:bold;

		
	}

	/* The main menu links */
	.nav>li>a {
		border-right: 1px dotted #3a3a3a;
		border-left: 1px dotted #1b1b1b;
	}

	/* Rounded corners for the first link of the main menu */
	.nav>li:first-child>a {
		border-right: 0;
	}

	/* Rounded corners for the last link of the main menu */
	.nav>li:last-child>a {
		border-left: 0;
		color:#c6e14b;
		
	}


	/* The submenu links */
	.nav ul li a {
		/* Layout */
		border-top: 1px dotted #3a3a3a;
		border-bottom: 1px dotted #1b1b1b;
		padding: 5px 15px;
		font-family:Tahoma;
		color: #d8d8d8;
		text-decoration: none;

		
		font-size: 10px;
		background-color:#3a3a3a;
		font-weight:bold;
		

		/* Background */
		background: #3a3a3a;
	}

	/* The hover state of the menu/submenu links */
	.nav>li>a:hover, .nav>li:hover>a, .nav ul li a:hover, .nav ul li:hover>a {
		color: #3a3a3a;
		
		background: #a0a0a0;
		background: -webkit-linear-gradient(bottom, #a0a0a0, #ffffff);
		background: -ms-linear-gradient(bottom,  #a0a0a0, #ffffff); 
		background: -moz-linear-gradient(bottom,  #a0a0a0, #ffffff);
		background: -o-linear-gradient(bottom,  #a0a0a0, #ffffff);
		border-color: transparent;
	}

	/* The links which have submenus have more space to the left */
	.nav>.dropdown>a {
		padding-right: 20px;
	}

	/* The arrow indicating a dropdown menu */
	.nav>.dropdown>a::after {
		content: "";
		position: absolute;
		top: 17px;
		right: 5px;
		width: 0px;
		height: 0px;

		/* Creating the arrow using borders */
		border: 4px solid transparent;
		border-top: 4px solid #c6e14b; 
	}

	/* The same arrow, but with a darker color, to create the shadow effect */
	.nav>.dropdown>a::before {
		content: "";
		position: absolute;
		top: 17px;
		right: 5px;
		width: 0px;
		height: 0px;

		/* Creating the arrow using borders */
		border: 4px solid transparent;
		border-top: 4px solid #c6e14b;
	}

	/* Changing the color of the arrow on hover */
	.nav>li>a:hover::after, .nav>li:hover>a::after {
		border-top: 4px solid #c6e14b;
	}

	.nav>li>a:hover::before, .nav>li:hover>a::before {
		border-top: 4px solid rgba(71, 67, 88, .3);
	}

	/* THE SUBMENUS */
		.nav ul {
			position: absolute;
			float:right;
			
		}

		/* Level 1 submenu */
		.nav>li>ul {
			text-align:right;
			padding-top: 5px;
			top: -9999px;
			opacity: 0;
			-webkit-transition: opacity .3s ease-in;
			-moz-transition: opacity .3s ease-in;
			-o-transition: opacity .3s ease-in;
			-ms-transition: opacity .3s ease-in;
			width: 160px;
		}

		/* Showing the submenu when the user is hovering the parent link */
		.nav>li:hover>ul {
			top: 28px;
			
			opacity: 1;
		}

		/* Level 2+ submenus */
		.nav ul ul {
			width: 215px;
			right: 160px;
			top: -9999px;
			padding-left: 5px;
			opacity: 0;
			-webkit-transition: opacity .3s ease-in;
			-moz-transition: opacity .3s ease-in;
			-o-transition: opacity .3s ease-in;
			-ms-transition: opacity .3s ease-in;
		}

		/* Showing the submenu when the user is hovering the parent link */
		.nav ul>li:hover>ul {
			top: 0px;
			opacity: 1;
		}

		/* The containers of the submenu links */
		.nav ul li {
			margin: 0;
			padding: 0;
			display: block;
			position: relative;
		}

		/* Rounded corners for the first link of the submenu */
		.nav ul>li:first-child>a {
			border-top: 0;
			border-top-left-radius: 4px;
			border-top-right-radius: 4px;
		}

		/* Rounded corners for the last link of the submenu */
		.nav ul>li:last-child>a{
			border-bottom: 0;
			border-bottom-left-radius: 4px;
			border-bottom-right-radius: 4px;
		}

		/* The arrow indicating a level 2+ submenu */
		.nav ul>.dropdown>a::after {
			content: "";
			position: absolute;
			top: 12px;
			left: 10px;
			width: 0px;
			height: 0px;

			/* Creating the arrow using borders */
			border: 4px solid transparent;
			border-right: 4px solid #c6e14b; 
		}

		/* The same arrow, but with a darker color, to create the shadow effect */
		.nav ul>.dropdown>a::before {
			content: "";
			position: absolute;
			top: 12px;
			left: 10px;
			width: 0px;
			height: 0px;

			/* Creating the arrow using borders */
			border: 4px solid transparent;
			border-right: 4px solid #c6e14b;
		}

		/* Changing the color of the arrow on hover */
		.nav ul>li>a:hover::after, .nav ul>li:hover>a::after {
			border-right: 4px solid #c6e14b;
		}

		.nav ul>li>a:hover::before, .nav ul>li:hover>a::before {
			border-right: 4px solid rgba(71, 67, 88 .3);
		}