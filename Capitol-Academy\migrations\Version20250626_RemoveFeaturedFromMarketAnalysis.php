<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove featured concept from market_analysis table
 */
final class Version20250626_RemoveFeaturedFromMarketAnalysis extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove is_featured column and idx_featured index from market_analysis table';
    }

    public function up(Schema $schema): void
    {
        // Drop the index first
        $this->addSql('DROP INDEX idx_featured ON market_analysis');
        
        // Drop the column
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN is_featured');
    }

    public function down(Schema $schema): void
    {
        // Add the column back
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN is_featured TINYINT(1) NOT NULL DEFAULT 0');
        
        // Recreate the index
        $this->addSql('CREATE INDEX idx_featured ON market_analysis (is_featured)');
    }
}
