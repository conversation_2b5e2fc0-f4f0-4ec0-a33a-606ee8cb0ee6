<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\PasswordUpgraderInterface;

/**
 * @extends ServiceEntityRepository<User>
 *
 * @implements PasswordUpgraderInterface<User>
 *
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements PasswordUpgraderInterface
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    /**
     * Used to upgrade (rehash) the user's password automatically over time.
     */
    public function upgradePassword(PasswordAuthenticatedUserInterface $user, string $newHashedPassword): void
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Instances of "%s" are not supported.', $user::class));
        }

        $user->setPassword($newHashedPassword);
        $this->getEntityManager()->persist($user);
        $this->getEntityManager()->flush();
    }

    /**
     * Find user by username or email
     */
    public function findByUsernameOrEmail(string $identifier): ?User
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.username = :identifier OR u.email = :identifier')
            ->setParameter('identifier', $identifier)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find all users ordered by creation date
     */
    public function findAllOrderedByCreatedAt(): array
    {
        return $this->createQueryBuilder('u')
            ->orderBy('u.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Count total users
     */
    public function countUsers(): int
    {
        return $this->createQueryBuilder('u')
            ->select('COUNT(u.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find user by slugified name
     */
    public function findBySlugifiedName(string $slugifiedName): ?User
    {
        // Convert slug back to search pattern
        $searchName = str_replace('-', ' ', $slugifiedName);

        return $this->createQueryBuilder('u')
            ->andWhere('LOWER(CONCAT(u.firstName, \' \', u.lastName)) = :name')
            ->setParameter('name', strtolower($searchName))
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find users by role
     */
    public function findByRole(string $role): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('JSON_CONTAINS(u.roles, :role) = 1')
            ->setParameter('role', json_encode($role))
            ->getQuery()
            ->getResult();
    }

    /**
     * Find users by full name (first name + last name)
     */
    public function findByFullName(string $fullName): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('CONCAT(u.firstName, \' \', u.lastName) = :fullName')
            ->setParameter('fullName', $fullName)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find user by email prefix (part before @)
     */
    public function findByEmailPrefix(string $emailPrefix): ?User
    {
        return $this->createQueryBuilder('u')
            ->andWhere('SUBSTRING(u.email, 1, LOCATE(\'@\', u.email) - 1) = :emailPrefix')
            ->setParameter('emailPrefix', $emailPrefix)
            ->getQuery()
            ->getOneOrNullResult();
    }
}
