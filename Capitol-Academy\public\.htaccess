# Capitol Academy - Professional .htaccess Configuration

# Enable URL Rewriting
RewriteEngine On

# Symfony Front Controller
RewriteCond %{REQUEST_URI}::$0 ^(/.+)/(.*)::\2$
RewriteRule .* - [E=BASE:%1]
RewriteCond %{HTTP:Authorization} .+
RewriteRule ^ - [E=HTTP_AUTHORIZATION:%0]
RewriteCond %{ENV:REDIRECT_STATUS} =""
RewriteRule ^index\.php(?:/(.*)|$) %{ENV:BASE}/$1 [R=301,L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ %{ENV:BASE}/index.php [L]

# Favicon and Icon Handling
<IfModule mod_rewrite.c>
    # Redirect old favicon requests to new location
    RewriteRule ^favicon\.ico$ /favicons/favicon-32x32.png [R=301,L]
    RewriteRule ^apple-touch-icon\.png$ /favicons/apple-touch-icon.png [R=301,L]
    RewriteRule ^apple-touch-icon-precomposed\.png$ /favicons/apple-touch-icon.png [R=301,L]
</IfModule>

# Cache Control for Static Assets
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Favicon and icons - cache for 1 year
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType image/vnd.microsoft.icon "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    
    # Web App Manifest
    ExpiresByType application/manifest+json "access plus 1 year"
    ExpiresByType text/cache-manifest "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/eot "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    # Favicon and icons
    <FilesMatch "\.(ico|png|jpg|jpeg|gif|svg|webp)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>
    
    # Web App Manifest
    <FilesMatch "\.(webmanifest|manifest)$">
        Header set Cache-Control "public, max-age=31536000"
        Header set Content-Type "application/manifest+json"
    </FilesMatch>
    
    # Security Headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Remove Server Information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# MIME Types for Web App Manifest and Icons
<IfModule mod_mime.c>
    AddType application/manifest+json .webmanifest .manifest
    AddType image/x-icon .ico
    AddType image/png .png
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/manifest+json
</IfModule>

# Security: Prevent access to sensitive files
<FilesMatch "\.(env|yml|yaml|ini|log|sh|sql|md)$">
    Require all denied
</FilesMatch>

# Prevent access to version control directories
<IfModule mod_rewrite.c>
    RewriteRule ^\.git - [F,L]
    RewriteRule ^\.svn - [F,L]
</IfModule>
