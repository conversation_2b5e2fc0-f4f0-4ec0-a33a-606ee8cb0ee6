<?php

namespace App\Repository;

use App\Entity\User;
use App\Entity\Video;
use App\Entity\UserVideoAccess;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserVideoAccess>
 *
 * @method UserVideoAccess|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserVideoAccess|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserVideoAccess[]    findAll()
 * @method UserVideoAccess[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserVideoAccessRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserVideoAccess::class);
    }

    /**
     * Check if user has access to a specific video
     */
    public function hasUserAccess(User $user, Video $video): bool
    {
        $access = $this->findOneBy([
            'user' => $user,
            'video' => $video,
            'isActive' => true
        ]);

        return $access && $access->isValid();
    }

    /**
     * Get user's access record for a specific video
     */
    public function getUserVideoAccess(User $user, Video $video): ?UserVideoAccess
    {
        return $this->findOneBy([
            'user' => $user,
            'video' => $video,
            'isActive' => true
        ]);
    }

    /**
     * Get all videos a user has access to
     */
    public function getUserAccessibleVideos(User $user, bool $validOnly = true): array
    {
        $qb = $this->createQueryBuilder('uva')
            ->join('uva.video', 'v')
            ->where('uva.user = :user')
            ->andWhere('uva.isActive = :isActive')
            ->andWhere('v.isActive = :videoActive')
            ->setParameter('user', $user)
            ->setParameter('isActive', true)
            ->setParameter('videoActive', true)
            ->orderBy('uva.grantedAt', 'DESC');

        if ($validOnly) {
            $qb->andWhere('uva.expiresAt IS NULL OR uva.expiresAt > :now')
               ->setParameter('now', new \DateTimeImmutable());
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Get user's video access by category
     */
    public function getUserVideoAccessByCategory(User $user, string $category): array
    {
        return $this->createQueryBuilder('uva')
            ->join('uva.video', 'v')
            ->where('uva.user = :user')
            ->andWhere('uva.isActive = :isActive')
            ->andWhere('v.isActive = :videoActive')
            ->andWhere('v.category = :category')
            ->andWhere('uva.expiresAt IS NULL OR uva.expiresAt > :now')
            ->setParameter('user', $user)
            ->setParameter('isActive', true)
            ->setParameter('videoActive', true)
            ->setParameter('category', $category)
            ->setParameter('now', new \DateTimeImmutable())
            ->orderBy('uva.grantedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find expired access records
     */
    public function findExpiredAccess(): array
    {
        return $this->createQueryBuilder('uva')
            ->where('uva.expiresAt IS NOT NULL')
            ->andWhere('uva.expiresAt < :now')
            ->andWhere('uva.isActive = :isActive')
            ->setParameter('now', new \DateTimeImmutable())
            ->setParameter('isActive', true)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find access records expiring soon
     */
    public function findAccessExpiringSoon(int $days = 7): array
    {
        $futureDate = (new \DateTimeImmutable())->modify("+{$days} days");
        
        return $this->createQueryBuilder('uva')
            ->where('uva.expiresAt IS NOT NULL')
            ->andWhere('uva.expiresAt BETWEEN :now AND :futureDate')
            ->andWhere('uva.isActive = :isActive')
            ->setParameter('now', new \DateTimeImmutable())
            ->setParameter('futureDate', $futureDate)
            ->setParameter('isActive', true)
            ->orderBy('uva.expiresAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Grant access to a video for a user
     */
    public function grantAccess(
        User $user, 
        Video $video, 
        ?\DateTimeImmutable $expiresAt = null,
        ?string $accessSource = null,
        ?int $orderId = null,
        ?int $videoPlanId = null
    ): UserVideoAccess {
        // Check if access already exists
        $existingAccess = $this->findOneBy([
            'user' => $user,
            'video' => $video
        ]);

        if ($existingAccess) {
            // Update existing access
            $existingAccess->setExpiresAt($expiresAt);
            $existingAccess->setIsActive(true);
            $existingAccess->setAccessSource($accessSource);
            $existingAccess->setOrderId($orderId);
            $existingAccess->setVideoPlanId($videoPlanId);
            $existingAccess->setGrantedAt(new \DateTimeImmutable());
            
            return $existingAccess;
        }

        // Create new access record
        $access = new UserVideoAccess();
        $access->setUser($user);
        $access->setVideo($video);
        $access->setExpiresAt($expiresAt);
        $access->setAccessSource($accessSource);
        $access->setOrderId($orderId);
        $access->setVideoPlanId($videoPlanId);

        $this->getEntityManager()->persist($access);
        
        return $access;
    }

    /**
     * Revoke access to a video for a user
     */
    public function revokeAccess(User $user, Video $video): bool
    {
        $access = $this->findOneBy([
            'user' => $user,
            'video' => $video,
            'isActive' => true
        ]);

        if ($access) {
            $access->setIsActive(false);
            return true;
        }

        return false;
    }

    /**
     * Get access statistics for a video
     */
    public function getVideoAccessStatistics(Video $video): array
    {
        $result = $this->createQueryBuilder('uva')
            ->select('
                COUNT(uva.id) as total_access_grants,
                SUM(CASE WHEN uva.expiresAt IS NULL OR uva.expiresAt > :now THEN 1 ELSE 0 END) as active_access,
                SUM(CASE WHEN uva.expiresAt IS NOT NULL AND uva.expiresAt <= :now THEN 1 ELSE 0 END) as expired_access,
                SUM(uva.accessCount) as total_views,
                AVG(uva.accessCount) as avg_views_per_user
            ')
            ->where('uva.video = :video')
            ->andWhere('uva.isActive = :isActive')
            ->setParameter('video', $video)
            ->setParameter('isActive', true)
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->getSingleResult();

        return [
            'total_access_grants' => (int)$result['total_access_grants'],
            'active_access' => (int)$result['active_access'],
            'expired_access' => (int)$result['expired_access'],
            'total_views' => (int)$result['total_views'],
            'avg_views_per_user' => round((float)$result['avg_views_per_user'], 2)
        ];
    }

    /**
     * Get user's access statistics
     */
    public function getUserAccessStatistics(User $user): array
    {
        $result = $this->createQueryBuilder('uva')
            ->select('
                COUNT(uva.id) as total_videos_accessed,
                SUM(CASE WHEN uva.expiresAt IS NULL OR uva.expiresAt > :now THEN 1 ELSE 0 END) as active_access,
                SUM(uva.accessCount) as total_video_views
            ')
            ->where('uva.user = :user')
            ->andWhere('uva.isActive = :isActive')
            ->setParameter('user', $user)
            ->setParameter('isActive', true)
            ->setParameter('now', new \DateTimeImmutable())
            ->getQuery()
            ->getSingleResult();

        return [
            'total_videos_accessed' => (int)$result['total_videos_accessed'],
            'active_access' => (int)$result['active_access'],
            'total_video_views' => (int)$result['total_video_views']
        ];
    }

    /**
     * Record video access (increment view count)
     */
    public function recordVideoAccess(User $user, Video $video): bool
    {
        $access = $this->getUserVideoAccess($user, $video);
        
        if ($access && $access->isValid()) {
            $access->incrementAccessCount();
            return true;
        }

        return false;
    }

    /**
     * Clean up expired access records (mark as inactive)
     */
    public function cleanupExpiredAccess(): int
    {
        $expiredAccess = $this->findExpiredAccess();
        $count = 0;

        foreach ($expiredAccess as $access) {
            $access->setIsActive(false);
            $count++;
        }

        if ($count > 0) {
            $this->getEntityManager()->flush();
        }

        return $count;
    }

    public function save(UserVideoAccess $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(UserVideoAccess $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
