{#
    Capitol Academy Admin Table Header Component
    Standardized header with search, statistics, and action buttons
    
    Parameters:
    - title: Page title text
    - icon: FontAwesome icon class (e.g., 'fas fa-users')
    - search_placeholder: Search input placeholder text
    - create_button: Object with {url, text, icon} properties
    - additional_buttons: Array of button objects with {url, text, icon, title} properties
    - stats: Object with {total, active, inactive, recent} counts
#}

<!-- Professional Header Card -->
<div class="card border-0 shadow-lg mb-4">
    <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                    <i class="{{ icon|default('fas fa-table') }} mr-3" style="font-size: 2rem;"></i>
                    {{ title|default('Admin Management') }}
                </h2>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end align-items-center flex-wrap">
                    <!-- Professional Search -->
                    <div class="search-container me-3 mb-2 mb-md-0" style="position: relative;">
                        <div class="input-group" style="width: 320px;">
                            <input type="text"
                                   id="professional-search"
                                   class="form-control form-control-lg admin-search-input"
                                   placeholder="{{ search_placeholder|default('Search...') }}"
                                   style="border: 2px solid #011a2d; background: #ffffff; color: #343a40; font-size: 1rem; transition: all 0.3s ease; box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25); border-radius: 8px 0 0 8px; outline: none;">
                            <div class="input-group-append">
                                <button type="button"
                                        class="btn btn-lg admin-search-btn"
                                        id="search-clear-btn"
                                        style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); border: 2px solid #011a2d; border-left: none; border-radius: 0 8px 8px 0; color: white;">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div id="search-results-count" class="text-muted small mt-1" style="display: none;"></div>
                    </div>
                    
                    <!-- Additional Action Buttons -->
                    {% if additional_buttons is defined and additional_buttons|length > 0 %}
                        {% for button in additional_buttons %}
                            <a href="{{ button.url }}" 
                               class="btn btn-outline-light me-2 mb-2 mb-md-0" 
                               style="font-weight: 500; border-radius: 6px; padding: 0.5rem 1rem; transition: all 0.3s ease;"
                               title="{{ button.title|default('') }}">
                                <i class="{{ button.icon }} me-2"></i>{{ button.text }}
                            </a>
                        {% endfor %}
                    {% endif %}
                    
                    <!-- Create Button -->
                    {% if create_button is defined %}
                        <a href="{{ create_button.url }}" 
                           class="btn btn-light admin-btn-create mb-2 mb-md-0" 
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem;">
                            <i class="{{ create_button.icon|default('fas fa-plus') }} me-2"></i>{{ create_button.text|default('Create New') }}
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    {% if stats is defined %}
    <div class="card-body pb-0">
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm admin-stat-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="admin-stat-icon me-3"
                                 style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);">
                                <i class="fas fa-layer-group text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Total Items</h6>
                                <h4 class="mb-0" style="color: #011a2d;">{{ stats.total|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm admin-stat-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="admin-stat-icon me-3"
                                 style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                                <i class="fas fa-check-circle text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Active</h6>
                                <h4 class="mb-0 text-success">{{ stats.active|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm admin-stat-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="admin-stat-icon me-3"
                                 style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                                <i class="fas fa-pause-circle text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Inactive</h6>
                                <h4 class="mb-0 text-secondary">{{ stats.inactive|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm admin-stat-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="admin-stat-icon me-3"
                                 style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);">
                                <i class="fas fa-clock text-white"></i>
                            </div>
                            <div>
                                <h6 class="text-muted mb-1">Recent</h6>
                                <h4 class="mb-0" style="color: #a90418;">{{ stats.recent|default(0) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Table Content Area -->
    <div class="card-body pt-0">
        <!-- This is where the table content will be placed -->
    </div>
</div>

<!-- Responsive Design Styles -->
<style>
@media (max-width: 768px) {
    .card-header .row {
        text-align: center;
    }
    
    .card-header .col-md-6:last-child {
        margin-top: 1rem;
    }
    
    .search-container {
        margin-right: 0 !important;
        margin-bottom: 1rem;
    }
    
    .search-container .input-group {
        width: 100% !important;
    }
    
    .admin-btn-create {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .card-header h2 {
        font-size: 1.5rem !important;
    }
    
    .card-header h2 i {
        font-size: 1.5rem !important;
    }
    
    .admin-stat-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .admin-stat-icon {
        width: 40px !important;
        height: 40px !important;
    }
    
    .admin-stat-card h4 {
        font-size: 1.25rem;
    }
}
</style>
