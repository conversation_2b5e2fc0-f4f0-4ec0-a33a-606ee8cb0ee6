<?php

namespace App\Form;

use App\Entity\PromotionalBanner;
use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\ColorType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;

use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PromotionalBannerType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Banner Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., Cyber Friday Sale—Save up to 25%',
                    'maxlength' => 255
                ],
                'help' => 'Main title displayed in the promotional banner'
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., Join our Analysis Group Today & Save Over $150',
                    'rows' => 3,
                    'maxlength' => 1000
                ],
                'help' => 'Additional description text (optional)'
            ])


            ->add('endDate', DateTimeType::class, [
                'label' => 'End Date/Time',
                'required' => false,
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'form-control enhanced-field',
                    'style' => 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;'
                ],
                'help' => 'When the banner should stop being displayed (optional)'
            ])
            ->add('backgroundColor', ColorType::class, [
                'label' => 'Background Color',
                'required' => false,
                'data' => '#001427',
                'attr' => [
                    'class' => 'form-control form-control-color',
                    'style' => 'width: 60px; height: 40px;'
                ],
                'help' => 'Background color for the banner (default: #001427)'
            ])

;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PromotionalBanner::class,
            'attr' => [
                'class' => 'needs-validation',
                'novalidate' => true
            ]
        ]);
    }
}
