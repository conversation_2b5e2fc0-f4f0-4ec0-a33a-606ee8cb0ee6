<?php

namespace App\Form;

use App\Entity\Partner;
use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class PartnerType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Partner Name',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., World Bank, CNBC, UIB',
                    'maxlength' => 255
                ],
                'help' => 'The name of the partner organization',
                'constraints' => [
                    new NotBlank(['message' => 'Partner name is required'])
                ]
            ])
            ->add('logoFile', FileType::class, [
                'label' => 'Partner Logo',
                'mapped' => false,
                'required' => !$options['is_edit'],
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'help' => 'Upload a logo image (PNG, JPG, SVG recommended). Optimal size: 200x100px',
                'constraints' => [
                    new File([
                        'maxSize' => '2M',
                        'mimeTypes' => [
                            'image/png',
                            'image/jpeg',
                            'image/jpg',
                            'image/gif',
                            'image/svg+xml',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (PNG, JPG, GIF, SVG, WebP)',
                        'maxSizeMessage' => 'The file is too large ({{ size }} {{ suffix }}). Maximum allowed size is {{ limit }} {{ suffix }}.'
                    ])
                ]
            ])
            ->add('websiteUrl', UrlType::class, [
                'label' => 'Website URL',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'https://www.partner-website.com'
                ],
                'help' => 'Official website URL of the partner organization'
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Brief description of the partnership...'
                ],
                'help' => 'Optional description of the partnership or organization'
            ])
            ->add('displayOrder', IntegerType::class, [
                'label' => 'Display Order',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., 1, 2, 3...',
                    'min' => 0
                ],
                'help' => 'Order in which the partner appears (lower numbers appear first)',
                'constraints' => [
                    new PositiveOrZero(['message' => 'Display order must be a positive number or zero'])
                ]
            ])
;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Partner::class,
            'is_edit' => false
        ]);
    }
}
