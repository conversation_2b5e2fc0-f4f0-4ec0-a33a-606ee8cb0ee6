<?php

namespace App\Entity;

use App\Repository\UserVideoAccessRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: UserVideoAccessRepository::class)]
#[ORM\Table(name: 'user_video_access')]
#[ORM\UniqueConstraint(name: 'unique_user_video', columns: ['user_id', 'video_id'])]
class UserVideoAccess
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\ManyToOne(targetEntity: Video::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Video $video = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $grantedAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $expiresAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column]
    private bool $isActive = true;

    #[ORM\Column(length: 50, nullable: true)]
    #[Assert\Length(max: 50, maxMessage: 'Access source cannot be longer than 50 characters')]
    private ?string $accessSource = null; // 'purchase', 'gift', 'promotion', etc.

    #[ORM\Column(nullable: true)]
    private ?int $orderId = null; // Reference to the order that granted this access

    #[ORM\Column(nullable: true)]
    private ?int $videoPlanId = null; // Reference to the video plan if access was granted via plan

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $lastAccessedAt = null;

    #[ORM\Column]
    private int $accessCount = 0;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        $this->grantedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getVideo(): ?Video
    {
        return $this->video;
    }

    public function setVideo(?Video $video): static
    {
        $this->video = $video;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getGrantedAt(): ?\DateTimeImmutable
    {
        return $this->grantedAt;
    }

    public function setGrantedAt(\DateTimeImmutable $grantedAt): static
    {
        $this->grantedAt = $grantedAt;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getExpiresAt(): ?\DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function setExpiresAt(?\DateTimeImmutable $expiresAt): static
    {
        $this->expiresAt = $expiresAt;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getAccessSource(): ?string
    {
        return $this->accessSource;
    }

    public function setAccessSource(?string $accessSource): static
    {
        $this->accessSource = $accessSource;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    public function setOrderId(?int $orderId): static
    {
        $this->orderId = $orderId;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getVideoPlanId(): ?int
    {
        return $this->videoPlanId;
    }

    public function setVideoPlanId(?int $videoPlanId): static
    {
        $this->videoPlanId = $videoPlanId;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getLastAccessedAt(): ?\DateTimeImmutable
    {
        return $this->lastAccessedAt;
    }

    public function setLastAccessedAt(?\DateTimeImmutable $lastAccessedAt): static
    {
        $this->lastAccessedAt = $lastAccessedAt;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getAccessCount(): int
    {
        return $this->accessCount;
    }

    public function setAccessCount(int $accessCount): static
    {
        $this->accessCount = $accessCount;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function incrementAccessCount(): static
    {
        $this->accessCount++;
        $this->lastAccessedAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    // Helper methods

    public function isExpired(): bool
    {
        if ($this->expiresAt === null) {
            return false; // Lifetime access
        }

        return $this->expiresAt < new \DateTimeImmutable();
    }

    public function isValid(): bool
    {
        return $this->isActive && !$this->isExpired();
    }

    public function hasLifetimeAccess(): bool
    {
        return $this->expiresAt === null;
    }

    public function getDaysUntilExpiry(): ?int
    {
        if ($this->expiresAt === null) {
            return null; // Lifetime access
        }

        $now = new \DateTimeImmutable();
        if ($this->expiresAt < $now) {
            return 0; // Already expired
        }

        $interval = $now->diff($this->expiresAt);
        return $interval->days;
    }

    public function getExpiryStatus(): string
    {
        if ($this->hasLifetimeAccess()) {
            return 'Lifetime Access';
        }

        if ($this->isExpired()) {
            return 'Expired';
        }

        $days = $this->getDaysUntilExpiry();
        if ($days === 0) {
            return 'Expires Today';
        } elseif ($days === 1) {
            return 'Expires Tomorrow';
        } elseif ($days <= 7) {
            return "Expires in {$days} days";
        } else {
            return 'Active';
        }
    }

    public function getExpiryStatusClass(): string
    {
        if ($this->hasLifetimeAccess()) {
            return 'text-success';
        }

        if ($this->isExpired()) {
            return 'text-danger';
        }

        $days = $this->getDaysUntilExpiry();
        if ($days <= 7) {
            return 'text-warning';
        }

        return 'text-success';
    }

    public function extendAccess(int $days): static
    {
        if ($this->expiresAt === null) {
            // If lifetime access, set expiry from now
            $this->expiresAt = (new \DateTimeImmutable())->modify("+{$days} days");
        } else {
            // Extend existing expiry
            $this->expiresAt = $this->expiresAt->modify("+{$days} days");
        }
        
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function grantLifetimeAccess(): static
    {
        $this->expiresAt = null;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }
}
