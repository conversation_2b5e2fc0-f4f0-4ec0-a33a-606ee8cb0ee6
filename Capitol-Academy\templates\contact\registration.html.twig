{% extends 'base.html.twig' %}

{% block title %}Student Registration - Capitol Academy{% endblock %}

{% block meta_description %}Register as a student at Capitol Academy to begin your journey in financial markets education and professional trading.{% endblock %}

{% block body %}
<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="bg-light py-3">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ path('app_home') }}">Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Student Registration</li>
        </ol>
    </div>
</nav>

<!-- Hero Section -->
<section class="bg-primary text-white py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-2">Student Registration</h1>
                <p class="lead mb-0">
                    Begin your journey to becoming a professional trader with Capitol Academy's comprehensive education programs.
                </p>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-user-graduate fa-4x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-5">
                    <h2 class="mb-4">Student Registration Form</h2>
                    
                    <p class="lead mb-4">
                        To register as a student and interact with Capitol Academy, our customer support team will help you and answer all your questions.
                    </p>
                    
                    <p class="mb-4">
                        Please fill out this form and the Capitol Academy support team will contact you with course information and registration details.
                    </p>
                    
                    {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true}}) }}
                    
                    <div class="row g-3">
                        <div class="col-md-12">
                            {{ form_row(form.fullName, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="row g-3 mt-2">
                        <div class="col-md-6">
                            {{ form_row(form.country, {
                                'attr': {'class': 'form-select'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                        <div class="col-md-6">
                            {{ form_row(form.email, {
                                'attr': {'class': 'form-control'},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="row g-3 mt-2">
                        <div class="col-md-12">
                            {{ form_row(form.subject, {
                                'attr': {'class': 'form-control', 'readonly': true},
                                'label_attr': {'class': 'form-label fw-bold'}
                            }) }}
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        {{ form_row(form.message, {
                            'attr': {'class': 'form-control', 'rows': 5, 'placeholder': 'Tell us about your trading experience, goals, and which courses interest you most...'},
                            'label': 'Additional Information',
                            'label_attr': {'class': 'form-label fw-bold'}
                        }) }}
                    </div>
                    
                    <div class="d-flex justify-content-center gap-3 mt-4">
                        <button type="submit" class="btn btn-success btn-lg px-5 btn-enhanced">
                            <i class="fas fa-user-plus me-2"></i>Submit Registration
                        </button>
                        <button type="reset" class="btn btn-outline-secondary btn-lg px-5 btn-enhanced" onclick="return confirm('Are you sure you want to reset all fields?')">
                            <i class="fas fa-undo me-2"></i>Reset Form
                        </button>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- What to Expect -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">What to Expect</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Personal consultation call within 24 hours</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Customized course recommendations</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Enrollment assistance and guidance</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Access to exclusive student resources</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Ongoing academic support</li>
                    </ul>
                </div>
            </div>
            
            <!-- Course Categories -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Available Programs</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <span class="badge bg-primary w-100 p-2">Financial Markets</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-success w-100 p-2">Technical Analysis</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-info w-100 p-2">Trading Strategies</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-warning w-100 p-2">Risk Management</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-danger w-100 p-2">Psychology</span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-dark w-100 p-2">Capital Mgmt</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ path('app_courses_list') }}" class="btn btn-outline-info btn-sm w-100">
                            View All Courses
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Need Help?</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fab fa-skype text-primary me-3 fa-lg"></i>
                        <div>
                            <strong>Skype</strong><br>
                            <a href="skype:capitol_academy" class="text-decoration-none">capitol_academy</a>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-phone text-primary me-3 fa-lg"></i>
                        <div>
                            <strong>Phone</strong><br>
                            +44-************
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-envelope text-primary me-3 fa-lg"></i>
                        <div>
                            <strong>Email</strong><br>
                            <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
