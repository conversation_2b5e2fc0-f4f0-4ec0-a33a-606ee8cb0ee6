{% extends 'admin/base.html.twig' %}

{% block title %}Create Partner - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Partner{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_partners') }}">Partners</a></li>
<li class="breadcrumb-item active">Create Partner</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-handshake mr-3" style="font-size: 2rem;"></i>
                        Create New Partner
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Partners Button -->
                        <a href="{{ path('admin_partners') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Partners
                        </a>
                    </div>
                </div>
            </div>
        </div>

        {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': true, 'enctype': 'multipart/form-data'}}) }}
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Partner Name -->
                            <div class="form-group">
                                <label for="{{ form.name.vars.id }}" class="form-label">
                                    <i class="fas fa-building text-primary mr-1"></i>
                                    Partner Name <span class="text-danger">*</span>
                                </label>
                                {{ form_widget(form.name, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'Enter partner organization name', 'required': 'required'}}) }}
                                <div class="invalid-feedback">
                                    Please provide a partner name.
                                </div>
                                {{ form_errors(form.name) }}
                            </div>

                            <!-- Website URL and Display Order Row -->
                            <div class="row">
                                <!-- Website URL -->
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="{{ form.websiteUrl.vars.id }}" class="form-label">
                                            <i class="fas fa-globe text-primary mr-1"></i>
                                            Website URL
                                        </label>
                                        {{ form_widget(form.websiteUrl, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'https://www.partner-website.com'}}) }}
                                        <small class="form-text text-muted help-text" style="display: none;">
                                            Partner's official website URL.
                                        </small>
                                        {{ form_errors(form.websiteUrl) }}
                                    </div>
                                </div>

                                <!-- Display Order -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="displayOrder" class="form-label">
                                            <i class="fas fa-sort-numeric-up text-primary mr-1"></i>
                                            Display Order
                                        </label>
                                        {{ form_widget(form.displayOrder, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'placeholder': 'e.g., 1, 2, 3...', 'min': '0'}}) }}
                                        <small class="form-text text-muted help-text" style="display: none;">
                                            Order in which partner appears on the website.
                                        </small>
                                        {{ form_errors(form.displayOrder) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="form-group">
                                <label for="{{ form.description.vars.id }}" class="form-label">
                                    <i class="fas fa-align-left text-primary mr-1"></i>
                                    Description
                                </label>
                                {{ form_widget(form.description, {'attr': {'class': 'form-control enhanced-field', 'style': 'border: 2px solid #ced4da;', 'rows': '4', 'placeholder': 'Brief description of the partnership...'}}) }}
                                <small class="form-text text-muted help-text" style="display: none;">
                                    Provide a brief description of the partnership.
                                </small>
                                {{ form_errors(form.description) }}
                            </div>
                            <!-- Logo Upload -->
                            <div class="form-group">
                                <label for="{{ form.logoFile.vars.id }}" class="form-label">
                                    <i class="fas fa-image text-primary mr-1"></i>
                                    Partner Logo
                                </label>
                                {{ form_widget(form.logoFile, {'attr': {'class': 'form-control enhanced-field', 'style': 'height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;', 'accept': 'image/jpeg,image/png,image/jpg'}}) }}
                                <small class="form-text text-muted help-text" style="display: none;">
                                    <strong>Professional dimensions:</strong> 200x100px (2:1 ratio)<br>
                                    JPEG/PNG, max 2MB. Optimal for partner logo display.
                                </small>
                                {{ form_errors(form.logoFile) }}
                                <div class="image-preview mt-2" id="logo-preview" style="display: none;">
                                    <div class="d-flex justify-content-center">
                                        <div class="professional-image-container" style="width: 300px; height: 150px; border: 2px solid #1e3c72; border-radius: 8px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; overflow: hidden;">
                                            <img src="" alt="Logo Preview" style="max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 6px;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" id="submitBtn" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;">
                                <i class="fas fa-save mr-2"></i>
                                <span id="submitText">Create Partner</span>
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_partners') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Enhanced form validation and submit handling
    const form = document.querySelector('form.needs-validation');
    const submitBtn = document.getElementById('submitBtn');
    const submitText = document.getElementById('submitText');

    if (form) {
        form.addEventListener('submit', function(event) {
            // Custom validation logic
            let isValid = true;
            let errorMessages = [];

            // Clear previous validation states
            clearFieldValidation();

            // Validate partner name (required field)
            const nameField = form.querySelector('[name="partner[name]"]');
            if (nameField && !nameField.value.trim()) {
                isValid = false;
                setFieldError(nameField, 'Partner name is required');
                errorMessages.push('Partner name is required');
            }

            // Validate display order (required field)
            const displayOrderField = form.querySelector('[name="partner[displayOrder]"]');
            if (displayOrderField && (!displayOrderField.value || displayOrderField.value < 0)) {
                isValid = false;
                setFieldError(displayOrderField, 'Display order is required and must be a positive number');
                errorMessages.push('Display order is required and must be a positive number');
            }

            // Validate website URL format if provided
            const websiteField = form.querySelector('[name="partner[websiteUrl]"]');
            if (websiteField && websiteField.value.trim()) {
                const urlPattern = /^https?:\/\/.+/;
                if (!urlPattern.test(websiteField.value.trim())) {
                    isValid = false;
                    setFieldError(websiteField, 'Website URL must be a valid URL starting with http:// or https://');
                    errorMessages.push('Website URL must be a valid URL starting with http:// or https://');
                }
            }

            // Validate logo file if selected
            const logoField = form.querySelector('[name="partner[logoFile]"]');
            if (logoField && logoField.files.length > 0) {
                const file = logoField.files[0];
                const maxSize = 2 * 1024 * 1024; // 2MB
                const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml', 'image/webp'];

                if (file.size > maxSize) {
                    isValid = false;
                    setFieldError(logoField, 'Logo file must be smaller than 2MB');
                    errorMessages.push('Logo file must be smaller than 2MB');
                }

                if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    setFieldError(logoField, 'Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                    errorMessages.push('Logo must be a valid image file (PNG, JPG, GIF, SVG, WebP)');
                }
            }

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Only show error banner if there are actual errors
                if (errorMessages.length > 0) {
                    showValidationErrors(errorMessages);
                }

                // Reset button state on validation error
                resetSubmitButton();

                // Scroll to first error field
                scrollToFirstError();
            } else {
                // Form is valid, show processing state and allow submission
                if (submitBtn && submitText) {
                    submitBtn.disabled = true;
                    submitText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating...';
                }
                // Remove any existing error messages
                removeValidationErrors();
            }

            form.classList.add('was-validated');
        });

        // Reset button state when form fields are modified
        const formInputs = form.querySelectorAll('input, select, textarea');
        formInputs.forEach(function(input) {
            input.addEventListener('input', function() {
                // Only reset button if it's not currently processing a valid submission
                if (submitBtn && !submitBtn.disabled) {
                    resetSubmitButton();
                }
                // Remove validation classes when user starts typing
                clearFieldValidation(input);
                // Remove general error messages when user starts correcting
                removeValidationErrors();
            });
        });
    }

    // Function to reset submit button
    function resetSubmitButton() {
        if (submitBtn && submitText) {
            submitBtn.disabled = false;
            submitText.innerHTML = 'Create Partner';
        }
    }

    // Function to show validation errors
    function showValidationErrors(errors) {
        // Remove existing error alerts
        removeValidationErrors();

        const errorHtml = `
            <div class="alert alert-danger alert-dismissible fade show validation-error" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Please fix the following errors:</strong>
                <ul class="mb-0 mt-2">
                    ${errors.map(error => `<li>${error}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Insert error message at the top of the form
        const cardBody = form.querySelector('.card-body');
        if (cardBody) {
            cardBody.insertAdjacentHTML('afterbegin', errorHtml);
        }
    }

    // Function to remove validation errors
    function removeValidationErrors() {
        const existingErrors = document.querySelectorAll('.validation-error');
        existingErrors.forEach(error => error.remove());
    }

    // Function to set field-specific error
    function setFieldError(field, message) {
        field.classList.add('is-invalid');

        // Find or create invalid-feedback div for this field
        let feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
        if (feedbackDiv) {
            feedbackDiv.textContent = message;
            feedbackDiv.style.display = 'block';
        }
    }

    // Function to clear field validation
    function clearFieldValidation(specificField = null) {
        if (specificField) {
            specificField.classList.remove('is-invalid', 'is-valid');
            const feedbackDiv = specificField.parentNode.querySelector('.invalid-feedback');
            if (feedbackDiv) {
                feedbackDiv.style.display = 'none';
            }
        } else {
            // Clear all field validations
            const allFields = form.querySelectorAll('input, select, textarea');
            allFields.forEach(field => {
                field.classList.remove('is-invalid', 'is-valid');
                const feedbackDiv = field.parentNode.querySelector('.invalid-feedback');
                if (feedbackDiv) {
                    feedbackDiv.style.display = 'none';
                }
            });
        }
    }

    // Function to scroll to first error field
    function scrollToFirstError() {
        const firstErrorField = form.querySelector('.is-invalid');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstErrorField.focus();
        }
    }

    // Reset button state if there are server-side validation errors
    {% if not form.vars.valid %}
        resetSubmitButton();
    {% endif %}

    // Form enhancement animations
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // Logo Preview Functionality
    $('#{{ form.logoFile.vars.id }}').on('change', function() {
        previewImage(this, '#logo-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $(previewSelector).show();
                $(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            $(previewSelector).hide();
        }
    }
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}
</style>
{% endblock %}
