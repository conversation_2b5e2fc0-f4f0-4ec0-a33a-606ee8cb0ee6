{% extends 'admin/base.html.twig' %}

{% block title %}Contact Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Contact Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_contacts') }}">Contacts</a></li>
<li class="breadcrumb-item active">{{ contact.fullName }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-envelope mr-3" style="font-size: 2rem;"></i>
                        Contact Details: {{ contact.fullName }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back Button (Icon Only) -->
                        <a href="{{ path('admin_contacts') }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;"
                           title="Back to Contacts"
                           onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';"
                           onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';">
                            <i class="fas fa-arrow-left" style="font-size: 1.1rem;"></i>
                        </a>

                        <!-- Print Button (Icon Only) -->
                        <button type="button"
                                onclick="printContactDetails()"
                                class="btn me-2 mb-2 mb-md-0"
                                style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 50%; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;"
                                title="Print Contact Details"
                                onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='scale(1.05)';"
                                onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='scale(1)';">
                            <i class="fas fa-print" style="font-size: 1.1rem;"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">

                        <!-- Full Name and Email (Same Line) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user text-primary mr-1"></i>
                                        Full Name
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.fullName }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-envelope text-primary mr-1"></i>
                                        Email
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.email }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Subject and Source Page (Same Line) -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tag text-primary mr-1"></i>
                                        Subject
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.subject ?? 'No subject provided' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-link text-primary mr-1"></i>
                                        Source Page
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.sourcePage ?? 'Unknown' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message (Full Width) -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-comment text-primary mr-1"></i>
                                Message
                            </label>
                            <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500; line-height: 1.6; min-height: 120px;">
                                {{ contact.message ? contact.message|nl2br : 'No message provided' }}
                            </div>
                        </div>

                        <!-- IP Address and Country (Same Line) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-map-marker-alt text-primary mr-1"></i>
                                        IP Address
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.ipAddress ?? 'Not recorded' }}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-globe text-primary mr-1"></i>
                                        Country
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.country ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Status and Created Date (Same Line) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {% if contact.processed %}
                                            <span class="badge bg-success">Processed</span>
                                        {% else %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar text-primary mr-1"></i>
                                        Created Date
                                    </label>
                                    <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 500;">
                                        {{ contact.createdAt|date('F j, Y \\a\\t g:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Print Styles */
@media print {
    .btn, .breadcrumb, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #011a2d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }

    .enhanced-display-field {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        -webkit-print-color-adjust: exact;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function
function printContactDetails() {
    window.print();
}
</script>
{% endblock %}
