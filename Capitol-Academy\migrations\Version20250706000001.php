<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove deprecated fields from video table: slug, duration, tags, vdocipher_video_id
 */
final class Version20250706000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove deprecated fields from video table: slug, duration, tags, vdocipher_video_id';
    }

    public function up(Schema $schema): void
    {
        // Check if columns exist before dropping them to avoid errors
        $this->addSql('SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE=\'ALLOW_INVALID_DATES\'');
        
        // Drop slug column if it exists
        $this->addSql('SET @sql = (SELECT IF(
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE table_name = \'video\' 
             AND table_schema = DATABASE() 
             AND column_name = \'slug\') > 0,
            \'ALTER TABLE video DROP COLUMN slug\',
            \'SELECT 1\'
        ))');
        $this->addSql('PREPARE stmt FROM @sql');
        $this->addSql('EXECUTE stmt');
        $this->addSql('DEALLOCATE PREPARE stmt');
        
        // Drop duration column if it exists
        $this->addSql('SET @sql = (SELECT IF(
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE table_name = \'video\' 
             AND table_schema = DATABASE() 
             AND column_name = \'duration\') > 0,
            \'ALTER TABLE video DROP COLUMN duration\',
            \'SELECT 1\'
        ))');
        $this->addSql('PREPARE stmt FROM @sql');
        $this->addSql('EXECUTE stmt');
        $this->addSql('DEALLOCATE PREPARE stmt');
        
        // Drop tags column if it exists
        $this->addSql('SET @sql = (SELECT IF(
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE table_name = \'video\' 
             AND table_schema = DATABASE() 
             AND column_name = \'tags\') > 0,
            \'ALTER TABLE video DROP COLUMN tags\',
            \'SELECT 1\'
        ))');
        $this->addSql('PREPARE stmt FROM @sql');
        $this->addSql('EXECUTE stmt');
        $this->addSql('DEALLOCATE PREPARE stmt');
        
        // Drop vdocipher_video_id column if it exists
        $this->addSql('SET @sql = (SELECT IF(
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
             WHERE table_name = \'video\' 
             AND table_schema = DATABASE() 
             AND column_name = \'vdocipher_video_id\') > 0,
            \'ALTER TABLE video DROP COLUMN vdocipher_video_id\',
            \'SELECT 1\'
        ))');
        $this->addSql('PREPARE stmt FROM @sql');
        $this->addSql('EXECUTE stmt');
        $this->addSql('DEALLOCATE PREPARE stmt');
        
        $this->addSql('SET SQL_MODE=@OLD_SQL_MODE');
    }

    public function down(Schema $schema): void
    {
        // Re-add the columns if needed for rollback
        $this->addSql('ALTER TABLE video ADD slug VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE video ADD duration INT DEFAULT NULL');
        $this->addSql('ALTER TABLE video ADD tags JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE video ADD vdocipher_video_id VARCHAR(255) DEFAULT NULL');
        
        // Add unique constraint back to vdocipher_video_id
        $this->addSql('CREATE UNIQUE INDEX UNIQ_7CC7DA2C8B5A2A9A ON video (vdocipher_video_id)');
    }
}
