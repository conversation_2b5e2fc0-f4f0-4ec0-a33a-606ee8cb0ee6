<?php

namespace App\Repository;

use App\Entity\Payment;
use App\Entity\User;
use App\Entity\Course;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Payment>
 */
class PaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Payment::class);
    }

    public function save(Payment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Payment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByStripePaymentId(string $stripePaymentId): ?Payment
    {
        return $this->findOneBy(['stripePaymentId' => $stripePaymentId]);
    }

    public function findByStripePaymentIntentId(string $paymentIntentId): ?Payment
    {
        return $this->createQueryBuilder('p')
            ->andWhere('JSON_EXTRACT(p.stripeData, \'$.payment_intent\') = :paymentIntentId')
            ->setParameter('paymentIntentId', $paymentIntentId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findSuccessfulPaymentForUserAndCourse(User $user, Course $course): ?Payment
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->andWhere('p.course = :course')
            ->andWhere('p.status = :status')
            ->setParameter('user', $user)
            ->setParameter('course', $course)
            ->setParameter('status', 'succeeded')
            ->orderBy('p.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findPaymentsByUser(User $user): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->setParameter('user', $user)
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findPaymentsByCourse(Course $course): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.course = :course')
            ->andWhere('p.status = :status')
            ->setParameter('course', $course)
            ->setParameter('status', 'succeeded')
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function getTotalRevenueForCourse(Course $course): float
    {
        $result = $this->createQueryBuilder('p')
            ->select('SUM(p.amount)')
            ->andWhere('p.course = :course')
            ->andWhere('p.status = :status')
            ->setParameter('course', $course)
            ->setParameter('status', 'succeeded')
            ->getQuery()
            ->getSingleScalarResult();

        return (float) ($result ?? 0);
    }

    public function getTotalRevenue(): float
    {
        $result = $this->createQueryBuilder('p')
            ->select('SUM(p.amount)')
            ->andWhere('p.status = :status')
            ->setParameter('status', 'succeeded')
            ->getQuery()
            ->getSingleScalarResult();

        return (float) ($result ?? 0);
    }
}
