.wt-rotator{
font-family:Tahoma;
font-size:13px;
background-color:#000;
border:1px solid #000;
position:relative;
width:625px;
height:298px;
overflow:hidden;
border-radius: 5px;
    -moz-border-radius: 5px ;
    -webkit-border-radius: 5px;
	font-weight:bold;
}
.wt-rotator a{
outline:none;
}
.wt-rotator .screen{
position:relative;
top:0;
left:0;
width:625px;
height:298px;
overflow:hidden;
}
.wt-rotator .strip{
display:block;
position:absolute;
top:0;
left:0;
z-index:0;
overflow:hidden;
}
.wt-rotator .content-box{
display:none;
position:absolute;
top:0;
left:0;
overflow:hidden;
padding-top:20px;
}
.wt-rotator .main-img{
display:none;
position:absolute;
top:0;
left:0;
z-index:0;
border:0;
}

.wt-rotator .timer{
position:absolute;
left:0;
height:4px;
background-color:#FFF;
-moz-opacity:.5;
filter:alpha(opacity=50);
opacity:0.5;
z-index:4;
visibility:hidden;
font-size:0;
}
.wt-rotator .desc{
position:absolute;
color:#FFF;
z-index:6;
overflow:hidden;
visibility:hidden;

direction:ltr;


}
.wt-rotator .inner-bg{
position:relative;
top:0px;;
width:625px;
height:38px;
background-color:#000;
-moz-opacity:.5;
filter:alpha(opacity=50);
opacity:.5;

}
.wt-rotator .inner-text{
position:absolute;
top:0;
left:auto;
padding:10px;
width:auto;
height:auto;
z-index:1;
text-align:left;
}
.wt-rotator .c-panel{
position:absolute;
top:0;
z-index:7;
visibility:hidden;
}
.wt-rotator .outer-hp,
.wt-rotator .outer-vp{
position:absolute;
background:#333;
background:-moz-linear-gradient(#444, #111);
background:-webkit-gradient(linear, 0 top, 0 bottom, from(#444), to(#111));
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#444444', endColorstr='#111111',GradientType=0);
border:1px solid #000;
}
.wt-rotator .outer-hp{
left:0;
width:100%;
border-left:none;
border-right:none;
}
.wt-rotator .outer-vp{
top:0;
height:100%;
border-top:none;
border-bottom:none;
}
.wt-rotator .back-scroll,
.wt-rotator .fwd-scroll{
		
position:absolute;
top:0;
left:0;
z-index:10;
background:url(../assets/spacer.png) no-repeat;
}
.wt-rotator .thumbnails,
.wt-rotator .buttons{
display:inline;
position:relative;
float:left;
overflow:hidden;
height:30px;
right:10px;

}
.wt-rotator .thumbnails ul{
position:relative;
list-style:none;
margin:0;
padding:0;
}
.wt-rotator .thumbnails ul.inside{
position:absolute;
top:0;
left:0;
}
.wt-rotator .thumbnails li,
.wt-rotator .play-btn,
.wt-rotator .prev-btn,
.wt-rotator .next-btn{
position:relative;
list-style:none;
display:inline;
float:left;
overflow:hidden;
width:20px;
height:20px;
line-height:20px;
text-align:center;
color:#EEE;
text-shadow: 0 1px 0 #222;
background-color:#000;
background:-moz-linear-gradient(#333, #000);
background:-webkit-gradient(linear, 0 top,  bottom, from(#333), to(#000));
border:1px solid #111;
cursor:pointer;
font-size:12px;
font-weight:bold;
background-repeat:no-repeat !important;
background-position:center !important;
-moz-border-radius:3px;
-webkit-border-radius:3px;
border-radius:3px;


}
.wt-rotator .thumbnails li:hover{
color:#FFF;
text-shadow: 0 1px 0 #888;
background-color:#CCC;
background:-moz-linear-gradient(#CCC, #999);
background:-webkit-gradient(linear, 0 top, 0 bottom, from(#CCC), to(#999));
}
.wt-rotator .thumbnails li.curr-thumb{
color:#000;
text-shadow: 0 1px 0 #fff;
background-color:#FFF;
background:-moz-linear-gradient(#FFF, #E0E0E0);
background:-webkit-gradient(linear, 0 top, 0 bottom, from(#FFF), to(#E0E0E0));
cursor:default;
}
.wt-rotator .thumbnails li.image{
background:#000;
-moz-border-radius:0;
-webkit-border-radius:0;
border-radius:0;
}
.wt-rotator .thumbnails li.image.curr-thumb,
.wt-rotator .thumbnails li.image:hover{
border-color:#06F;
}
.wt-rotator .thumbnails li.image a{
display:block;
border:0;
}
.wt-rotator .thumbnails li.image img{
display:block;
border:0;
position:absolute;
-moz-opacity:.85;
filter:alpha(opacity=85);
opacity:0.85;
}
.wt-rotator .thumbnails li.image:hover img{
-moz-opacity:1;
filter:alpha(opacity=100);
opacity:1;
}
.wt-rotator .thumbnails li.image.curr-thumb img{
-moz-opacity:1;
filter:alpha(opacity=100);
opacity:1;
cursor:default;
}
.wt-rotator .thumbnails li *{
display:none;


}
.wt-rotator .thumbnails li div{
background-color:#000;
font-family:Tahoma;
font-size:13px;
font-style:inherit;

font-weight:bold;

direction:rtl;
text-align:right;

}
.wt-rotator .play-btn{
	
background:#000 url(../assets/play.png);
background:url(../assets/play.png), -moz-linear-gradient(#333, #000);
background:url(../assets/play.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#333), to(#000));
}
.wt-rotator .play-btn:hover{
background-color:#CCC !important;
background:url(../assets/play.png), -moz-linear-gradient(#CCC, #999);
background:url(../assets/play.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#CCC), to(#999));
}
.wt-rotator .pause{
background:#000 url(../assets/pause.png);
background:url(../assets/pause.png), -moz-linear-gradient(#333, #000);
background:url(../assets/pause.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#333), to(#000));
}
.wt-rotator .pause:hover{
background-color:#CCC !important;
background:url(../assets/pause.png), -moz-linear-gradient(#CCC, #999);
background:url(../assets/pause.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#CCC), to(#999));
}
.wt-rotator .prev-btn{
background:#000 url(../assets/prev.png);
background:url(../assets/prev.png), -moz-linear-gradient(#333, #000);
background:url(../assets/prev.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#333), to(#000));
}
.wt-rotator .prev-btn:hover{
background-color:#CCC !important;
background:url(../assets/prev.png), -moz-linear-gradient(#CCC, #999);
background:url(../assets/prev.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#CCC), to(#999));
}
.wt-rotator .next-btn{
background:#000 url(../assets/next.png);
background:url(../assets/next.png), -moz-linear-gradient(#333, #000);
background:url(../assets/next.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#333), to(#000));
}
.wt-rotator .next-btn:hover{
background-color:#CCC !important;
background:url(../assets/next.png), -moz-linear-gradient(#CCC, #999);
background:url(../assets/next.png), -webkit-gradient(linear, 0 top, 0 bottom, from(#CCC), to(#999));
}