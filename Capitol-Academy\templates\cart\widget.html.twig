<div class="cart-widget">
    <div class="cart-icon" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-shopping-cart"></i>
        {% if cart_summary.item_count > 0 %}
            <span class="cart-badge">{{ cart_summary.item_count }}</span>
        {% endif %}
    </div>
    
    <div class="dropdown-menu dropdown-menu-end cart-dropdown" aria-labelledby="cartDropdown">
        <div class="cart-dropdown-header">
            <h6 class="mb-0">
                <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                {% if cart_summary.item_count > 0 %}
                    ({{ cart_summary.item_count }} item{{ cart_summary.item_count > 1 ? 's' : '' }})
                {% endif %}
            </h6>
        </div>
        
        {% if cart_summary.is_empty %}
            <div class="cart-dropdown-empty">
                <i class="fas fa-shopping-cart"></i>
                <p class="mb-2">Your cart is empty</p>
                <a href="{{ path('app_premium_videos') }}" class="btn btn-sm btn-primary">
                    Browse Videos
                </a>
            </div>
        {% else %}
            <div class="cart-dropdown-items">
                {% for item_key, item in cart_items %}
                    <div class="cart-dropdown-item">
                        <div class="item-thumbnail">
                            {% if item.thumbnail %}
                                <img src="{{ asset('uploads/' ~ item.type ~ 's/thumbnails/' ~ item.thumbnail) }}" 
                                     alt="{{ item.title }}">
                            {% else %}
                                <div class="placeholder-thumbnail">
                                    <i class="fas fa-play"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="item-details">
                            <h6 class="item-title">{{ item.title|length > 25 ? item.title|slice(0, 25) ~ '...' : item.title }}</h6>
                            <div class="item-meta">
                                <span class="item-type">{{ item.type|replace({'_': ' '})|title }}</span>
                                <span class="item-price">${{ item.price|number_format(2) }}</span>
                            </div>
                        </div>
                        <button class="remove-item-btn" onclick="removeFromCart('{{ item.type }}', {{ item.id }})" title="Remove item">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
                
                {% if total_items > 3 %}
                    <div class="cart-dropdown-more">
                        <small class="text-muted">
                            +{{ total_items - 3 }} more item{{ total_items - 3 > 1 ? 's' : '' }}
                        </small>
                    </div>
                {% endif %}
            </div>
            
            <div class="cart-dropdown-footer">
                <div class="cart-total">
                    <strong>Total: ${{ cart_summary.total }}</strong>
                </div>
                <div class="cart-actions">
                    <a href="{{ path('app_cart') }}" class="btn btn-outline-primary btn-sm">
                        View Cart
                    </a>
                    {% if app.user %}
                        <button class="btn btn-primary btn-sm" onclick="alert('Checkout functionality coming soon')">
                            Checkout
                        </button>
                    {% else %}
                        <a href="{{ path('app_login') }}" class="btn btn-primary btn-sm">
                            Login
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
.cart-widget {
    position: relative;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 8px;
    color: white;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.cart-icon:hover {
    color: #a90418;
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #a90418;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.cart-dropdown {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.cart-dropdown-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-empty {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.cart-dropdown-empty i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #dee2e6;
}

.cart-dropdown-items {
    max-height: 300px;
    overflow-y: auto;
}

.cart-dropdown-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.cart-dropdown-item:hover {
    background: #f8f9fa;
}

.item-thumbnail {
    width: 50px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
}

.item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-thumbnail {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.item-details {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #011a2d;
    margin-bottom: 4px;
    line-height: 1.2;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.item-type {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-size: 0.7rem;
}

.item-price {
    font-weight: 600;
    color: #a90418;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #dc3545;
    padding: 5px;
    margin-left: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.remove-item-btn:hover {
    opacity: 1;
}

.cart-dropdown-more {
    padding: 10px 20px;
    text-align: center;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-footer {
    padding: 15px 20px;
    background: #f8f9fa;
}

.cart-total {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #011a2d;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.cart-actions .btn {
    flex: 1;
    font-size: 0.85rem;
}

@media (max-width: 768px) {
    .cart-dropdown {
        width: 300px;
    }
    
    .cart-dropdown-item {
        padding: 12px 15px;
    }
    
    .item-thumbnail {
        width: 40px;
        height: 32px;
    }
}
</style>

<script>
function removeFromCart(type, id) {
    fetch('{{ path('app_cart_remove') }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=${type}&id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the cart widget
            updateCartWidget();
        } else {
            alert(data.message || 'Failed to remove item');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing the item');
    });
}

function updateCartWidget() {
    fetch('{{ path('app_cart_widget') }}')
    .then(response => response.text())
    .then(html => {
        document.querySelector('.cart-widget').outerHTML = html;
    })
    .catch(error => {
        console.error('Error updating cart widget:', error);
    });
}
</script>
