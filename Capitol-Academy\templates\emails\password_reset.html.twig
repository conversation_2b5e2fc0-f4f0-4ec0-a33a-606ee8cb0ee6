<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capitol Academy - Password Reset Code</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Email-safe styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .email-header {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 40px 30px;
            text-align: center;
            color: #ffffff;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 50%;
        }

        .logo-fallback {
            font-size: 2rem;
            font-weight: bold;
            color: #011a2d;
            display: none;
        }

        .email-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .email-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .email-body {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #011a2d;
            margin-bottom: 20px;
        }

        .message {
            font-size: 16px;
            color: #343a40;
            margin-bottom: 30px;
            line-height: 1.7;
        }

        .code-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #011a2d;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }

        .code-label {
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 15px;
        }

        .verification-code {
            font-size: 36px;
            font-weight: 800;
            color: #011a2d;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            background-color: #ffffff;
            padding: 15px 25px;
            border-radius: 8px;
            border: 2px solid #a90418;
            display: inline-block;
            margin-bottom: 15px;
        }

        .code-note {
            font-size: 14px;
            color: #6c757d;
            font-style: italic;
        }

        .expiry-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }

        .expiry-warning .icon {
            font-size: 24px;
            color: #856404;
            margin-bottom: 10px;
        }

        .expiry-text {
            font-size: 15px;
            color: #856404;
            font-weight: 600;
        }

        .instructions {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }

        .instructions-title {
            font-size: 16px;
            font-weight: 600;
            color: #0056b3;
            margin-bottom: 10px;
        }

        .instructions-list {
            list-style: none;
            padding: 0;
        }

        .instructions-list li {
            font-size: 14px;
            color: #0056b3;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .instructions-list li:before {
            content: "→";
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .security-notice {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .security-notice .icon {
            font-size: 20px;
            color: #721c24;
            margin-bottom: 10px;
        }

        .security-text {
            font-size: 14px;
            color: #721c24;
            line-height: 1.6;
        }

        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }

        .footer-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .company-info {
            font-size: 12px;
            color: #adb5bd;
            line-height: 1.5;
        }

        .divider {
            height: 2px;
            background: linear-gradient(90deg, #011a2d 0%, #a90418 50%, #011a2d 100%);
            margin: 30px 0;
            border-radius: 1px;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 8px;
            }

            .email-header {
                padding: 30px 20px;
            }

            .email-body {
                padding: 30px 20px;
            }

            .email-title {
                font-size: 24px;
            }

            .verification-code {
                font-size: 28px;
                letter-spacing: 4px;
                padding: 12px 20px;
            }

            .code-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="logo">
                <img src="{{ app.request.schemeAndHttpHost }}/images/logos/logo-round.png" alt="Capitol Academy"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div class="logo-fallback">CA</div>
            </div>
            <h1 class="email-title">Capitol Academy</h1>
            <p class="email-subtitle">Password Reset Request</p>
        </div>

        <!-- Body -->
        <div class="email-body">
            <div class="greeting">Hello {{ user.firstName }},</div>

            <div class="message">
                We received a request to reset your password for your Capitol Academy account. Please use the verification code below to proceed.
            </div>

            <!-- Verification Code -->
            <div class="code-container">
                <div class="code-label">Your Verification Code</div>
                <div class="verification-code">{{ token }}</div>
                <div class="code-note">Enter this code on the password reset page</div>
            </div>

            <!-- Expiry Warning -->
            <div class="expiry-warning">
                <div class="icon">⏰</div>
                <div class="expiry-text">This code will expire in {{ expires_in_minutes }} minutes</div>
            </div>

            <div class="divider"></div>

            <!-- Security Notice -->
            <div class="security-notice">
                <div class="icon">🔒</div>
                <div class="security-text">
                    <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email. Your account remains secure.
                </div>
            </div>

            <div class="message">
                If you're having trouble with the password reset process, please don't hesitate to contact our support team. We're here to help you regain access to your Capitol Academy account.
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-text">
                Best regards,<br>
                <strong>The Capitol Academy Team</strong>
            </div>
            
            <div class="company-info">
                Capitol Academy - Professional Trading Education<br>
                This is an automated message. Please do not reply to this email.<br>
                © {{ "now"|date("Y") }} Capitol Academy. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>
