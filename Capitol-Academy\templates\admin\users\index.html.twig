{% extends 'admin/base.html.twig' %}

{% block title %}Users Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Users Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Users</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Users Management',
    'page_icon': 'fas fa-users',
    'search_placeholder': 'Search users by name, email, or country...',
    'stats': [
        {
            'title': 'Total Users',
            'value': users|length,
            'icon': 'fas fa-users',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Users',
            'value': users|filter(user => not user.isBlocked)|length,
            'icon': 'fas fa-user-check',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Blocked Users',
            'value': users|filter(user => user.isBlocked)|length,
            'icon': 'fas fa-user-slash',
            'color': '#dc3545',
            'gradient': 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': users|filter(user => user.createdAt and user.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-user-plus',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Profile'},
            {'text': 'Name'},
            {'text': 'Email'},
            {'text': 'Country'},
            {'text': 'Phone'},
            {'text': 'Status'},
            {'text': 'Joined'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for user in users %}
            {% set row_cells = [
                {
                    'content': '<div class="d-flex align-items-center justify-content-center">
                        ' ~ (user.profilePicture ?
                            '<img src="' ~ asset('uploads/profiles/' ~ user.profilePicture) ~ '" alt="' ~ user.fullName ~ '" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover; border: 2px solid #011a2d;">' :
                            '<div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; font-weight: bold;">' ~ user.firstName|first ~ user.lastName|first ~ '</div>'
                        ) ~ '
                    </div>'
                },
                {
                    'content': '<h6 class="user-name mb-0 font-weight-bold text-dark">' ~ user.fullName ~ '</h6>'
                },
                {
                    'content': '<span class="user-email text-dark">' ~ user.email ~ '</span>'
                },
                {
                    'content': '<span class="user-country text-dark">' ~ (user.country|default('N/A')) ~ '</span>'
                },
                {
                    'content': '<span class="text-dark">' ~ (user.phone|default('N/A')) ~ '</span>'
                },
                {
                    'content': user.isBlocked ?
                        '<span class="badge" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-ban mr-1"></i> Blocked</span>' :
                        '<span class="badge" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>'
                },
                {
                    'content': '<small class="text-muted">' ~ user.createdAt|date('M d, Y') ~ '</small>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_user_details', {'emailPrefix': user.emailPrefix}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Details"><i class="fas fa-eye"></i></a>
                        ' ~ (canEdit ?
                            (user.isBlocked ?
                                '<button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Unblock User" onclick="toggleUserStatus(' ~ user.id ~ ', \'' ~ user.fullName ~ '\', ' ~ user.isBlocked ~ ')"><i class="fas fa-unlock"></i></button>' :
                                '<button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Block User" onclick="toggleUserStatus(' ~ user.id ~ ', \'' ~ user.fullName ~ '\', ' ~ user.isBlocked ~ ')"><i class="fas fa-lock"></i></button>'
                            ) : ''
                        ) ~
                        (canDelete ?
                            '<button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete User" onclick="deleteUser(' ~ user.id ~ ', \'' ~ user.fullName ~ '\')"><i class="fas fa-trash"></i></button>' : ''
                        ) ~
                    '</div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'user-row',
            'empty_message': 'No users found',
            'empty_icon': 'fas fa-users',
            'empty_description': 'Users will appear here as they register.',
            'search_config': {
                'fields': ['.user-name', '.user-email', '.user-country']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.user-row',
        ['.user-name', '.user-email', '.user-country']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === 'true' ? 'block' : 'unblock';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// User management functions
function toggleUserStatus(userId, userName, isBlocked) {
    const action = isBlocked ? 'unblock' : 'block';
    showStatusModal(userName, isBlocked ? 'true' : 'false', function() {
        executeUserStatusToggle(userId);
    });
}

function deleteUser(userId, userName) {
    showDeleteModal(userName, function() {
        executeUserDelete(userId);
    });
}

// Actual execution functions
function executeUserStatusToggle(userId) {
    fetch(`/admin/users/${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the user status');
    });
}

function executeUserDelete(userId) {
    fetch(`/admin/users/${userId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the user');
    });
}
</script>
{% endblock %}


