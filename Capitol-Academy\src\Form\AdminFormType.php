<?php

namespace App\Form;

use App\Entity\Admin;
use App\Service\EmailUniquenessValidator;
use App\Service\ValidationService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class AdminFormType extends AbstractType
{
    public function __construct(
        private EmailUniquenessValidator $emailValidator,
        private ValidationService $validationService
    ) {}

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('firstName', TextType::class, [
                'label' => 'First Name',
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please enter a first name',
                    ]),
                    new Length([
                        'min' => 2,
                        'minMessage' => 'First name should be at least {{ limit }} characters',
                        'max' => 100,
                        'maxMessage' => 'First name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter first name'
                ]
            ])
            ->add('lastName', TextType::class, [
                'label' => 'Last Name',
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please enter a last name',
                    ]),
                    new Length([
                        'min' => 2,
                        'minMessage' => 'Last name should be at least {{ limit }} characters',
                        'max' => 100,
                        'maxMessage' => 'Last name cannot be longer than {{ limit }} characters',
                    ]),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter last name'
                ]
            ])
            ->add('username', TextType::class, [
                'label' => 'Username',
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please enter a username',
                    ]),
                    new Length([
                        'min' => 3,
                        'minMessage' => 'Username should be at least {{ limit }} characters',
                        'max' => 50,
                        'maxMessage' => 'Username cannot be longer than {{ limit }} characters',
                    ]),
                    new Callback([$this, 'validateUsername']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter username'
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => 'Email Address',
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please enter an email address',
                    ]),
                    new Email([
                        'message' => 'Please enter a valid email address',
                    ]),
                    new Callback([$this, 'validateEmailUniqueness']),
                ],
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter email address'
                ]
            ])
            ->add('plainPassword', RepeatedType::class, [
                'type' => PasswordType::class,
                'mapped' => false,
                'first_options' => [
                    'label' => 'Password',
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Enter password'
                    ]
                ],
                'second_options' => [
                    'label' => 'Confirm Password',
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Confirm password'
                    ]
                ],
                'invalid_message' => 'The password fields must match.',
                'constraints' => [
                    new NotBlank([
                        'message' => 'Please enter a password',
                    ]),
                    new Length([
                        'min' => 8,
                        'minMessage' => 'Password must be at least {{ limit }} characters',
                        'max' => 128,
                        'maxMessage' => 'Password cannot be longer than {{ limit }} characters',
                    ]),
                    new Callback([$this, 'validatePasswordStrength']),
                ],
            ]);
    }

    public function validateEmailUniqueness($email, ExecutionContextInterface $context): void
    {
        if (!$this->emailValidator->isEmailUnique($email)) {
            $context->buildViolation($this->emailValidator->getEmailDuplicateMessage($email))
                ->addViolation();
        }
    }

    public function validatePasswordStrength($password, ExecutionContextInterface $context): void
    {
        $errors = $this->validationService->validatePassword($password);

        foreach ($errors as $error) {
            $context->buildViolation($error)->addViolation();
        }
    }

    public function validateUsername($username, ExecutionContextInterface $context): void
    {
        // Check format
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $context->buildViolation('Username can only contain letters, numbers, and underscores.')
                ->addViolation();
        }

        // Check uniqueness (you'll need to inject AdminRepository for this)
        // For now, we'll handle this in the controller or create a separate validator
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Admin::class,
        ]);
    }
}
