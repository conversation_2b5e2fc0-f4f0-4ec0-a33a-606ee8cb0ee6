{% extends 'admin/base.html.twig' %}

{% block title %}Create Category - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Category{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_category_index') }}">Categories</a></li>
<li class="breadcrumb-item active">Create Category</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-folder mr-3" style="font-size: 2rem;"></i>
                        Create New Category
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Categories Button -->
                        <a href="{{ path('admin_category_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="width: 45px; height: 45px; border-radius: 50%; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; text-decoration: none;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';"
                           title="Back to Categories">
                            <i class="fas fa-arrow-left" style="font-size: 1.1rem;"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>


        <form method="post" class="needs-validation" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('category_create') }}">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Category Name -->
                            <div class="form-group">
                                <label for="name" class="form-label">
                                    <i class="fas fa-folder text-primary mr-1" aria-hidden="true"></i>
                                    Category Name <span class="text-danger" aria-label="required">*</span>
                                </label>
                                <input type="text"
                                       class="form-control enhanced-field"
                                       id="name"
                                       name="name"
                                       placeholder="Enter category name"
                                       required
                                       maxlength="255"
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; transition: all 0.3s ease;">
                                <div class="invalid-feedback">
                                    Please provide a category name.
                                </div>
                            </div>

                            <!-- Display Options and Description Row -->
                            <div class="row">
                                <!-- Display Options -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-eye text-primary mr-1" aria-hidden="true"></i>
                                            Display Options
                                        </label>
                                        <div class="enhanced-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; height: calc(2.5rem + 2 * 1.5rem + 2 * 0.75rem + 4px); display: flex; flex-direction: column; justify-content: center;">
                                            <div class="form-check mb-2">
                                                <input type="checkbox" class="form-check-input" id="displayInCourses" name="displayInCourses" value="1">
                                                <label class="form-check-label" for="displayInCourses">
                                                    <i class="fas fa-graduation-cap mr-2 text-info"></i>
                                                    Display in Course Creation
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="displayInVideos" name="displayInVideos" value="1">
                                                <label class="form-check-label" for="displayInVideos">
                                                    <i class="fas fa-video mr-2 text-success"></i>
                                                    Display in Video Creation
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="description" class="form-label">
                                            <i class="fas fa-align-left text-primary mr-1" aria-hidden="true"></i>
                                            Description
                                        </label>
                                        <textarea class="form-control enhanced-field"
                                                  id="description"
                                                  name="description"
                                                  rows="3"
                                                  placeholder="Enter category description"
                                                  style="border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; transition: all 0.3s ease; resize: vertical; height: calc(2.5rem + 2 * 1.5rem + 2 * 0.75rem + 4px);"></textarea>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 1px solid #dee2e6; padding: 1.5rem;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem; transition: all 0.3s ease;">
                                <i class="fas fa-save mr-2"></i>
                                Create Category
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_category_index') }}" class="btn btn-secondary btn-lg" style="font-weight: 600; border-radius: 8px; padding: 0.75rem 2rem;">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Create Category';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Category...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Form field focus effects
    $('.form-control, .enhanced-field').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control,
.form-group.focused .enhanced-field {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.enhanced-field:hover {
    border-color: #2a5298 !important;
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Enhanced form controls styling */
.form-control,
.enhanced-field {
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus,
.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.form-control:hover,
.enhanced-field:hover {
    border-color: #2a5298 !important;
}
</style>
{% endblock %}
