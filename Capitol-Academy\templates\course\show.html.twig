{% extends 'base.html.twig' %}

{% block title %}{{ course.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ course.description|slice(0, 160) }}{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
    --ca-green: #99b75a;
    --ca-blue: #00233e;
}

/* Hero Section */
.course-hero {
    background: linear-gradient(135deg, var(--ca-primary) 0%, var(--ca-blue) 100%);
    padding: 120px 0 80px;
    color: white;
    position: relative;
    overflow: hidden;
}

.course-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
}

.course-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(169, 4, 24, 0.3);
}

.course-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    font-family: 'Montserrat', sans-serif;
    line-height: 1.2;
}

.course-hero-description {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 800px;
    line-height: 1.6;
    font-family: 'Calibri', sans-serif;
}

.course-meta-badges {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.meta-badge {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.meta-badge i {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* CTA Section */
.cta-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-top: 40px;
    text-align: center;
}

.cta-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    font-family: 'Montserrat', sans-serif;
}

.cta-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.btn-primary-cta {
    background: linear-gradient(135deg, var(--ca-accent) 0%, #c41230 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(169, 4, 24, 0.4);
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
    margin-right: 20px;
}

.btn-primary-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(169, 4, 24, 0.5);
    color: white;
    text-decoration: none;
}

.btn-secondary-cta {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 13px 35px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary-cta:hover {
    background: white;
    color: var(--ca-primary);
    text-decoration: none;
    transform: translateY(-2px);
}

/* Content Section */
.course-content-section {
    padding: 100px 0;
    background: var(--ca-light-gray);
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin-top: 60px;
}

.main-content {
    background: white;
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
}

.content-section {
    margin-bottom: 50px;
}

.content-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 25px;
    font-family: 'Montserrat', sans-serif;
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-title i {
    color: var(--ca-accent);
    font-size: 1.5rem;
}

.section-content {
    color: var(--ca-dark-gray);
    line-height: 1.8;
    font-size: 1.1rem;
    font-family: 'Calibri', sans-serif;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.features-list li:last-child {
    border-bottom: none;
}

.features-list i {
    color: var(--ca-green);
    font-size: 1.2rem;
    width: 20px;
}

/* Sidebar */
.course-sidebar {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(1, 26, 45, 0.08);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.sidebar-section {
    margin-bottom: 40px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ca-primary);
    margin-bottom: 20px;
    font-family: 'Montserrat', sans-serif;
}

.course-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.course-info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--ca-medium-gray);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-value {
    color: var(--ca-dark-gray);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .course-sidebar {
        position: static;
    }
}

@media (max-width: 768px) {
    .course-hero-title {
        font-size: 2.5rem;
    }

    .course-meta-badges {
        justify-content: center;
    }

    .main-content {
        padding: 30px;
    }

    .course-sidebar {
        padding: 30px;
    }
}
</style>
{% endblock %}

{% block body %}
<!-- Course Hero Section -->
<section class="course-hero">
    <div class="container">
        <div class="hero-content">
            <div class="course-badge">{{ course.code }}</div>
            <h1 class="course-hero-title">{{ course.title }}</h1>
            <p class="course-hero-description">{{ course.description }}</p>

            <!-- Course Meta Information -->
            <div class="course-meta-badges">
                <div class="meta-badge">
                    <i class="fas fa-tag"></i>
                    <span>{{ course.category|default('Trading') }}</span>
                </div>
                {% if course.level %}
                <div class="meta-badge">
                    <i class="fas fa-layer-group"></i>
                    <span>{{ course.level }}</span>
                </div>
                {% endif %}
                <div class="meta-badge">
                    <i class="fas fa-eye"></i>
                    <span>{{ course.viewCount|default(0) }} Views</span>
                </div>
                {% if course.isActive %}
                <div class="meta-badge">
                    <i class="fas fa-check-circle"></i>
                    <span>Active Course</span>
                </div>
                {% endif %}
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <h3 class="cta-title">Ready to Start Your Trading Journey?</h3>
                <p class="cta-description">Contact our team to learn more about enrollment and get personalized guidance for your trading education.</p>
                <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-primary-cta">
                    <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                </a>
                <a href="{{ path('app_courses') }}" class="btn-secondary-cta">
                    <i class="fas fa-arrow-left me-2"></i>View All Courses
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Course Content Section -->
<section class="course-content-section">
    <div class="container">
        <div class="content-grid">
            <!-- Main Content -->
            <div class="main-content">
                <!-- Course Overview -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-info-circle"></i>
                        Course Overview
                    </h2>
                    <div class="section-content">
                        <p>{{ course.description }}</p>
                        <p>This comprehensive course is designed to provide you with the essential knowledge and practical skills needed to succeed in financial markets. Our expert instructors will guide you through every aspect of the subject matter with real-world examples and hands-on exercises.</p>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                {% if course.learningOutcomes %}
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-graduation-cap"></i>
                        What You'll Learn
                    </h2>
                    <div class="section-content">
                        <ul class="features-list">
                            {% for outcome in course.learningOutcomes %}
                            <li>
                                <i class="fas fa-check-circle"></i>
                                <span>{{ outcome }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Course Features -->
                {% if course.features %}
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        Course Features
                    </h2>
                    <div class="section-content">
                        <ul class="features-list">
                            {% for feature in course.features %}
                            <li>
                                <i class="fas fa-arrow-right"></i>
                                <span>{{ feature }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}

                <!-- Course Modules -->
                {% if modules and modules|length > 0 %}
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-list"></i>
                        Course Modules
                    </h2>
                    <div class="section-content">
                        <ul class="features-list">
                            {% for module in modules %}
                            <li>
                                <i class="fas fa-play-circle"></i>
                                <span><strong>{{ module.title }}</strong> - {{ module.description|slice(0, 100) }}{% if module.description|length > 100 %}...{% endif %}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="course-sidebar">
                <!-- Course Information -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Course Information</h3>
                    <div class="course-info-item">
                        <span class="info-label">
                            <i class="fas fa-code"></i>
                            Course Code
                        </span>
                        <span class="info-value">{{ course.code }}</span>
                    </div>
                    {% if course.category %}
                    <div class="course-info-item">
                        <span class="info-label">
                            <i class="fas fa-tag"></i>
                            Category
                        </span>
                        <span class="info-value">{{ course.category }}</span>
                    </div>
                    {% endif %}
                    {% if course.level %}
                    <div class="course-info-item">
                        <span class="info-label">
                            <i class="fas fa-layer-group"></i>
                            Level
                        </span>
                        <span class="info-value">{{ course.level }}</span>
                    </div>
                    {% endif %}
                    <div class="course-info-item">
                        <span class="info-label">
                            <i class="fas fa-eye"></i>
                            Views
                        </span>
                        <span class="info-value">{{ course.viewCount|default(0) }}</span>
                    </div>
                    <div class="course-info-item">
                        <span class="info-label">
                            <i class="fas fa-calendar"></i>
                            Created
                        </span>
                        <span class="info-value">{{ course.createdAt|date('M Y') }}</span>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Quick Actions</h3>
                    <div class="d-grid gap-3">
                        <a href="{{ path('app_contact_registration') }}?course={{ course.code }}" class="btn-primary-cta" style="margin-right: 0; text-align: center;">
                            <i class="fas fa-envelope me-2"></i>Contact for Enrollment
                        </a>
                        <a href="{{ path('app_courses') }}" class="btn-secondary-cta" style="color: var(--ca-primary); border-color: var(--ca-primary); text-align: center;">
                            <i class="fas fa-arrow-left me-2"></i>Back to Courses
                        </a>
                    </div>
                </div>

                <!-- Need Help -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Need Help?</h3>
                    <div class="section-content">
                        <p style="color: var(--ca-medium-gray); font-size: 0.95rem; line-height: 1.6;">
                            Have questions about this course? Our team is here to help you choose the right program for your trading goals.
                        </p>
                        <a href="{{ path('app_contact') }}" style="color: var(--ca-accent); text-decoration: none; font-weight: 600;">
                            <i class="fas fa-phone me-2"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% endblock %}