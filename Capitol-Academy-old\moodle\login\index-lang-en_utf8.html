<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Capitol Academy: Login to the site "/>
    <title>Capitol Academy: Login to the site</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/login/index-lang-en_utf8.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
addonload(function() { if(el = document.getElementById('username')) el.focus(); });
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="login course-1 notloggedin dir-ltr lang-en_utf8" id="login-index">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">Capitol Academy</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">You are here</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../../moodle.html">Academy</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> Login to the site</li></ul></div>
        <div class="navbutton"><div class="langmenu"><form action="/" method="get" id="chooselang" class="popupform"><div><label for="chooselang_jump"><span class="accesshide ">Language</span></label><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=ar_utf8">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=en_utf8" selected="selected">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/login/index.php?lang=fr_utf8">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="fzlz72tdX4"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="Go"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div></div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix"><div class="loginbox clearfix twocolumns">
  <div class="loginpanel">
    <h2>Returning to this web site?</h2>
      <div class="subcontent loginsub">
        <div class="desc">
          Login here using your username and password<br/>(Cookies must be enabled in your browser)<span class="helplink"><a title="Help with Cookies must be enabled in your browser (new window)" href="../../moodle/help-module-moodle-file-cookies_html-forcelang.html" onclick="this.target='/'; return openpopup('/', 'popup', '/', 0);"></a></span>        </div>
                <form action="../../moodle/login/index.html" method="post" id="login">
          <div class="loginform">
            <div class="form-label"><label for="username">Username</label></div>
            <div class="form-input">
              <input type="text" name="username" id="username" size="15" value=""/>
            </div>
            <div class="clearer"><!-- --></div>
            <div class="form-label"><label for="password">Password</label></div>
            <div class="form-input">
              <input type="password" name="password" id="password" size="15" value=""/>
              <input type="submit" value="Login"/>
              <input type="hidden" name="testcookies" value="1"/>
            </div>
            <div class="clearer"><!-- --></div>
          </div>
        </form>
      </div>
      <div class="subcontent guestsub">
        <div class="desc">
          Some courses may allow guest access        </div>
        <form action="../../moodle/login/index.html" method="post" id="guestlogin">
          <div class="guestform">
            <input type="hidden" name="username" value="guest"/>
            <input type="hidden" name="password" value="guest"/>
            <input type="hidden" name="testcookies" value="1"/>
            <input type="submit" value="Login as a guest"/>
          </div>
        </form>
      </div>
      <div class="subcontent forgotsub">
        <div class="desc">
          Forgotten your username or password?        </div>
        <form action="../../moodle/login/forgot_password.html" method="post" id="changepassword">
          <div class="forgotform">
            <input type="hidden" name="sesskey" value="fzlz72tdX4"/>
            <input type="submit" value="Yes, help me log in"/>
          </div>
        </form>
      </div>
     </div>
    <div class="signuppanel">
      <h2>Is this your first time here?</h2>
      <div class="subcontent">
Hi! For full access to courses you'll need to take
   a minute to create a new account for yourself on this web site.
   Each of the individual courses may also have a one-time
   "enrolment key", which you won't need until later. Here are
   the steps:
   <ol>
   <li>Fill out the <a href="../../moodle/login/signup.html">New Account</a> form with your details.</li>
   <li>An email will be immediately sent to your email address.</li>
   <li>Read your email, and click on the web link it contains.</li>
   <li>Your account will be confirmed and you will be logged in.</li>
   <li>Now, select the course you want to participate in.</li>
   <li>If you are prompted for an "enrolment key" - use the one
   that your teacher has given you. This will "enrol" you in the
   course.</li>
   <li>You can now access the full course. From now on you will only need
   to enter your personal username and password (in the form on this page)
   to log in and access any course you have enrolled in.</li>
   </ol>                 <div class="signupform">
                   <form action="../../moodle/login/signup.html" method="get" id="signup">
                   <div><input type="submit" value="Create new account"/></div>
                   </form>
                 </div>
      </div>
    </div>
</div>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div><div class="homelink"><a href="../../moodle.html">Home</a></div></div>
</div>
</body>
</html>