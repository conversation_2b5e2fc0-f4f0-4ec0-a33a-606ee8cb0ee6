/* Elastislide Style */

.elastislide-list {
	list-style-type: none;
	display: none;
}

.no-js .elastislide-list {
	display: block;
}

.elastislide-carousel ul li {
	min-width: 80px; /* minimum width of the image (min width + border) */
	padding:0 1px;
}

.elastislide-wrapper {
	position: relative;
	background-color: #fff;
	margin: 0 auto;
	min-height: 60px;
	box-shadow: 1px 1px 3px rgba(0,0,0,0.2);
}

.elastislide-wrapper.elastislide-loading {
	background-image: url(../images/loading.gif);
	background-repeat: no-repeat;
	background-position: center center;
}

.elastislide-horizontal {
	padding: 10px 40px;
}

.elastislide-vertical {
	padding: 40px 10px;
}

.elastislide-carousel {
	overflow: hidden;
	position: relative;
}

.elastislide-carousel ul {
	position: relative;
	display: block;
	list-style-type: none;
	padding: 0;
	margin: 0;
	-webkit-backface-visibility: hidden;
	-webkit-transform: translateX(0px);
	-moz-transform: translateX(0px);
	-ms-transform: translateX(0px);
	-o-transform: translateX(0px);
	transform: translateX(0px);
}

.elastislide-horizontal ul {
	white-space: nowrap;
}

.elastislide-carousel ul li {
	margin: 0;
	-webkit-backface-visibility: hidden;
}

.elastislide-horizontal ul li {
	height: 100%;
	display: inline-block;
}

.elastislide-vertical ul li {
	display: block;
}

.elastislide-carousel ul li a {
	display: inline-block;
	width: 100%;
}

.elastislide-carousel ul li a img {
	display: block;
	border: 2px solid white;
	max-width: 100%;
	
}

/* Navigation Arrows */

.elastislide-wrapper nav span {
	position: absolute;
	background: #ddd url(../images/nav.png) no-repeat 4px 3px;
	width: 23px;
	height: 23px;
	border-radius: 50%;
	text-indent: -9000px;
	cursor: pointer;
	opacity: 0.8;
}

.elastislide-wrapper nav span:hover {
	opacity: 1.0
}

.elastislide-horizontal nav span {
	top: 50%;
	left: 10px;
	margin-top: -11px;
}

.elastislide-vertical nav span {
	top: 10px;
	left: 50%;
	margin-left: -11px;
	background-position: -17px 5px;
}

.elastislide-horizontal nav span.elastislide-next {
	right: 10px;
	left: auto;
	background-position: 4px -17px;
}

.elastislide-vertical nav span.elastislide-next {
	bottom: 10px;
	top: auto;
	background-position: -17px -18px;
}