{% extends 'base.html.twig' %}

{% block title %}Error {{ status_code|default('') }} - Capitol Academy{% endblock %}

{% block meta_description %}An error occurred while processing your request. Return to Capitol Academy's homepage or contact us for assistance.{% endblock %}

{% block body %}
<div class="error-page-container">
    <!-- Hero Section -->
    <section class="error-hero-section py-5" style="background: linear-gradient(135deg, #011a2d 0%, #1e3c72 100%); min-height: 70vh;">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="error-content text-white">
                        <!-- Error Code -->
                        <h1 class="error-number display-1 fw-bold mb-4" style="font-size: 6rem; color: #a90418; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                            {{ status_code|default('Error') }}
                        </h1>
                        
                        <!-- Error Title -->
                        <h2 class="error-title h1 fw-bold mb-4">
                            {% if status_code == 403 %}
                                Access Denied
                            {% elseif status_code == 500 %}
                                Server Error
                            {% else %}
                                Something Went Wrong
                            {% endif %}
                        </h2>
                        
                        <!-- Error Description -->
                        <p class="error-description lead mb-5" style="font-size: 1.3rem; line-height: 1.8; opacity: 0.9;">
                            {% if status_code == 403 %}
                                You don't have permission to access this resource.
                            {% elseif status_code == 500 %}
                                We're experiencing technical difficulties. Please try again later.
                            {% else %}
                                An unexpected error occurred while processing your request.
                            {% endif %}
                            Our team has been notified and is working to resolve the issue.
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class="error-actions d-flex flex-wrap justify-content-center gap-3">
                            <a href="{{ path('app_home') }}" class="btn btn-light btn-lg px-4 py-3" style="border-radius: 8px; font-weight: 600;">
                                <i class="fas fa-home me-2"></i>
                                Return Home
                            </a>
                            <a href="javascript:history.back()" class="btn btn-outline-light btn-lg px-4 py-3" style="border-radius: 8px; font-weight: 600;">
                                <i class="fas fa-arrow-left me-2"></i>
                                Go Back
                            </a>
                            <a href="{{ path('app_contact') }}" class="btn btn-outline-light btn-lg px-4 py-3" style="border-radius: 8px; font-weight: 600;">
                                <i class="fas fa-envelope me-2"></i>
                                Contact Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Helpful Links Section -->
    <section class="helpful-links-section py-5" style="background: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h3 class="h2 fw-bold mb-4" style="color: #011a2d;">
                        Continue Your Learning Journey
                    </h3>
                    <p class="lead" style="color: #6c757d;">
                        Don't let this stop you from achieving your trading goals
                    </p>
                </div>
            </div>
            
            <div class="row g-4">
                <!-- Trading Courses -->
                <div class="col-lg-6 col-md-6">
                    <div class="helpful-link-card card border-0 h-100 shadow-sm" style="transition: transform 0.3s ease;">
                        <div class="card-body p-4 text-center">
                            <div class="icon-container mb-3">
                                <i class="fas fa-graduation-cap" style="font-size: 3rem; color: #011a2d;"></i>
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #011a2d;">Trading Courses</h5>
                            <p class="text-muted mb-4">
                                Master financial markets with our comprehensive course catalog
                            </p>
                            <a href="{{ path('app_courses') }}" class="btn btn-outline-primary">
                                Explore Courses
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Market Analysis -->
                <div class="col-lg-6 col-md-6">
                    <div class="helpful-link-card card border-0 h-100 shadow-sm" style="transition: transform 0.3s ease;">
                        <div class="card-body p-4 text-center">
                            <div class="icon-container mb-3">
                                <i class="fas fa-chart-line" style="font-size: 3rem; color: #011a2d;"></i>
                            </div>
                            <h5 class="fw-bold mb-3" style="color: #011a2d;">Market Analysis</h5>
                            <p class="text-muted mb-4">
                                Stay ahead with expert insights on stocks, forex, and crypto
                            </p>
                            <a href="{{ path('app_market_analysis') }}" class="btn btn-outline-primary">
                                Read Analysis
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Error Page Styles */
.error-page-container {
    min-height: 100vh;
}

.error-number {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.helpful-link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
}

.helpful-link-card .icon-container {
    transition: transform 0.3s ease;
}

.helpful-link-card:hover .icon-container {
    transform: scale(1.1);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .error-number {
        font-size: 4rem !important;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1.1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}
