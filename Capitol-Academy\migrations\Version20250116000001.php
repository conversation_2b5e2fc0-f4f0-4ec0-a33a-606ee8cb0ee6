<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Course Entity and Module Simplification Migration
 * 
 * This migration removes the following fields and tables:
 * - Course: price, duration, banner_image, mode, total_reviews, average_rating, 
 *   enrolled_count, active_enrollments, completed_count, certified_count, moodleCourseId
 * - CourseModule: price, duration
 * - Drops: enrollments, certifications tables
 */
final class Version20250116000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Simplify Course entity and remove enrollment/certification system';
    }

    public function up(Schema $schema): void
    {
        // Drop enrollment and certification tables
        $this->addSql('DROP TABLE IF EXISTS certifications');
        $this->addSql('DROP TABLE IF EXISTS enrollments');
        
        // Remove fields from courses table
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS price');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS duration');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS banner_image');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS mode');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS total_reviews');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS average_rating');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS enrolled_count');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS active_enrollments');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS completed_count');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS certified_count');
        $this->addSql('ALTER TABLE courses DROP COLUMN IF EXISTS moodleCourseId');
        
        // Remove fields from course_modules table
        $this->addSql('ALTER TABLE course_modules DROP COLUMN IF EXISTS price');
        $this->addSql('ALTER TABLE course_modules DROP COLUMN IF EXISTS duration');
    }

    public function down(Schema $schema): void
    {
        // Add fields back to courses table
        $this->addSql('ALTER TABLE courses ADD COLUMN price DECIMAL(10,2) DEFAULT NULL');
        $this->addSql('ALTER TABLE courses ADD COLUMN duration INT DEFAULT NULL');
        $this->addSql('ALTER TABLE courses ADD COLUMN banner_image VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE courses ADD COLUMN mode VARCHAR(20) DEFAULT \'onsite\'');
        $this->addSql('ALTER TABLE courses ADD COLUMN total_reviews INT DEFAULT 0');
        $this->addSql('ALTER TABLE courses ADD COLUMN average_rating DECIMAL(3,2) DEFAULT \'0.00\'');
        $this->addSql('ALTER TABLE courses ADD COLUMN enrolled_count INT DEFAULT 0');
        $this->addSql('ALTER TABLE courses ADD COLUMN active_enrollments INT DEFAULT 0');
        $this->addSql('ALTER TABLE courses ADD COLUMN completed_count INT DEFAULT 0');
        $this->addSql('ALTER TABLE courses ADD COLUMN certified_count INT DEFAULT 0');
        $this->addSql('ALTER TABLE courses ADD COLUMN moodleCourseId VARCHAR(255) DEFAULT NULL');
        
        // Add fields back to course_modules table
        $this->addSql('ALTER TABLE course_modules ADD COLUMN price DECIMAL(10,2) DEFAULT NULL');
        $this->addSql('ALTER TABLE course_modules ADD COLUMN duration INT DEFAULT NULL');
        
        // Recreate enrollments table
        $this->addSql('CREATE TABLE enrollments (
            id INT AUTO_INCREMENT NOT NULL,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            payment_id INT DEFAULT NULL,
            enrolled_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            completed_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            status VARCHAR(20) NOT NULL DEFAULT \'active\',
            progress_percentage INT DEFAULT 0,
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            PRIMARY KEY(id),
            UNIQUE INDEX unique_user_course (user_id, course_id),
            INDEX IDX_CCD8C132A76ED395 (user_id),
            INDEX IDX_CCD8C132591CC992 (course_id),
            INDEX IDX_CCD8C1324C3A3BB (payment_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        
        // Recreate certifications table
        $this->addSql('CREATE TABLE certifications (
            id INT AUTO_INCREMENT NOT NULL,
            enrollment_id INT NOT NULL,
            student_id INT NOT NULL,
            course_id INT NOT NULL,
            certified_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            notes LONGTEXT DEFAULT NULL,
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\',
            PRIMARY KEY(id),
            INDEX IDX_82987F2E591CC992 (enrollment_id),
            INDEX IDX_82987F2ECB944F1A (student_id),
            INDEX IDX_82987F2E591CC992_COURSE (course_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB');
        
        // Add foreign key constraints
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C132A76ED395 FOREIGN KEY (user_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C132591CC992 FOREIGN KEY (course_id) REFERENCES courses (id)');
        $this->addSql('ALTER TABLE enrollments ADD CONSTRAINT FK_CCD8C1324C3A3BB FOREIGN KEY (payment_id) REFERENCES payments (id)');
        
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2E591CC992 FOREIGN KEY (enrollment_id) REFERENCES enrollments (id)');
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2ECB944F1A FOREIGN KEY (student_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2E591CC992_COURSE FOREIGN KEY (course_id) REFERENCES courses (id)');
    }
}
