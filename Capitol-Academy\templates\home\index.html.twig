{% extends 'base.html.twig' %}

{% block title %}Capitol Academy - Professional Financial Training{% endblock %}

{% block body %}

<main class="homepage-main">
    <!-- Section 1: Hero Section -->
    <section class="hero-section position-relative overflow-hidden" style="height: 80vh;">
        <!-- Background Image -->
        <div class="hero-background" style="
            background-image: url('{{ asset('images/backgrounds/Banner 1 HP.jpeg') }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        "></div>

        <!-- Light Overlay for Text Readability -->
        <div class="hero-overlay" style="
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(1, 26, 45, 0.2);
            z-index: 2;
        "></div>

        <!-- Hero Content -->
        <div class="container position-relative" style="z-index: 3; height: 80vh;">
            <div class="row align-items-center h-100">
                <div class="col-lg-8">
                    <div class="hero-content text-white py-4">
                        <h1 class="hero-title fw-bold mb-4" style="
                            font-size: 3.5rem;
                            line-height: 1.2;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                            color: white;
                            font-family: 'Montserrat', sans-serif;
                        ">
                            <div class="mb-2">You Can LEARN.</div>
                            <div class="mb-2">We Can TEACH.</div>
                            <div class="mb-2">We'll Be Great.</div>
                            <div>TOGETHER.</div>
                        </h1>

                        <div class="hero-actions">
                            <a href="{{ path('app_contact_registration') }}" class="btn btn-outline-light px-3 py-3" style="
                                border: 2px solid white;
                                background: transparent;
                                color: white;
                                font-weight: 600;
                                border-radius: 8px;
                                transition: all 0.3s ease;
                                font-size: 1.1rem;
                            " onmouseover="this.style.background='white'; this.style.color='#011a2d'; this.style.borderColor='white'; this.style.borderRadius='8px';"
                               onmouseout="this.style.background='transparent'; this.style.color='white'; this.style.borderColor='white'; this.style.borderRadius='8px';">
                                <i class="fas fa-user-plus me-2"></i>Register Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 2: Trusted Partners -->
    <section class="partners-section py-4" style="background: white; margin: 0; padding-top: 2rem; padding-bottom: 2rem;">
        <div class="container position-relative">
            <!-- Inline Trusted by text with logos -->
            <div class="d-flex align-items-center justify-content-center">
                <h3 class="fw-bold text-dark me-5 mb-2" style="font-size: 1.5rem; white-space: nowrap; margin-right: 3rem !important;">Trusted by</h3>

                <!-- Enhanced Seamless Scrolling Partners Logos -->
                <div class="partners-carousel-container overflow-hidden position-relative flex-grow-1" style="height: 50px;">
                    <div class="partners-carousel" id="partnersCarousel">
                        {% set partners = get_active_partners() %}
                        {% if partners|length > 0 %}
                            <!-- First complete set of logos -->
                            <div class="partner-set d-flex align-items-center">
                                {% for partner in partners %}
                                <div class="partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center" style="min-width: 80px; height: 50px;">
                                    <img src="{{ partner.logoUrl }}" alt="{{ partner.name }}"
                                         class="img-fluid partner-img"
                                         style="
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         "
                                         onmouseover="this.style.transform='scale(1.1)'"
                                         onmouseout="this.style.transform='scale(1)'"
                                         onerror="this.src='/images/placeholders/image-placeholder.png'">
                                </div>
                                {% endfor %}
                            </div>

                            <!-- Exact duplicate set for seamless infinite loop -->
                            <div class="partner-set d-flex align-items-center">
                                {% for partner in partners %}
                                <div class="partner-logo flex-shrink-0 mx-4 d-flex align-items-center justify-content-center" style="min-width: 80px; height: 50px;">
                                    <img src="{{ partner.logoUrl }}" alt="{{ partner.name }}"
                                         class="img-fluid partner-img"
                                         style="
                                             height: 35px;
                                             opacity: 1;
                                             transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                                             object-fit: contain;
                                             filter: grayscale(0%) brightness(1);
                                         "
                                         onmouseover="this.style.transform='scale(1.1)'"
                                         onmouseout="this.style.transform='scale(1)'"
                                         onerror="this.src='/images/placeholders/image-placeholder.png'">
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <!-- Fallback if no partners in database -->
                            <div class="text-center w-100 d-flex align-items-center justify-content-center" style="height: 50px;">
                                <p class="text-muted mb-0">Partner logos will appear here</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>



        </div>
    </section>

    <!-- Section 3: Our Courses -->
    <section class="courses-section position-relative" style="
        background-image: url('{{ asset('images/backgrounds/fond-sitemap.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        margin-top: 2rem;
        padding: 4rem 0;
    ">
        <!-- Enhanced Darker Overlay for Better Contrast -->
        <div class="position-absolute top-0 start-0 w-100 h-100" style="background-image: url('{{ asset('images/backgrounds/fond-sitemap.png') }}'); border: 10px solid rgba(255, 255, 255, 0.1); z-index: 1;"></div>

        <div class="container position-relative" style="z-index: 2;">
            <!-- Enhanced Main Section Title -->
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="fw-bold mb-4" style="
                        font-size: 2.5rem;
                        color: #00233e;
                        margin-bottom: 2rem;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 700;
                        line-height: 1.1;
                    ">Our Courses</h2>
                </div>
            </div>

            <!-- Content Area -->
            <div class="row mb-5 align-items-center">
                <!-- Left: Text Content -->
                <div class="col-lg-7 pe-5 d-flex flex-column justify-content-center">
                    <h3 class="fw-bold mb-4" style="
                        font-size: 1.3rem;
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-weight: 600;
                        line-height: 1.2;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    ">Financial Market Technicals Analysis Certified Courses</h3>

                    <p class="mb-4" style="
                        font-size: 1.4rem;
                        line-height: 1.6;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                        font-weight: 400;
                        margin-right: 3rem;
                    ">
                        From finance fundamentals to advanced deep-dives, each course offers video lessons, practical templates,
                        interactive exercises, skill validation, and a certificate of completion. Over 75% of Capitol Academy
                        learners report improved productivity or competency within weeks.
                    </p>
                </div>

                <!-- Right: Certificate Image -->
                <div class="col-lg-5 d-flex align-items-center justify-content-center ps-4">
                    <div class="certificate-container" style="
                        transition: all 0.3s ease;
                        transform-style: preserve-3d;
                    " onmouseover="this.style.transform='rotateY(5deg) rotateX(5deg) scale(1.05)'"
                       onmouseout="this.style.transform='rotateY(0deg) rotateX(0deg) scale(1)'">
                        <img src="{{ asset('images/certificates/certificate-sample.png') }}"
                             alt="Certificate Sample"
                             class="img-fluid"
                             style="
                                max-width: 450px;
                                width: 100%;
                                border-radius: 10px;
                                box-shadow: 0 10px 25px rgba(0,0,0,0.3);
                             "
                             onerror="this.src='{{ asset('images/placeholders/certificate-placeholder.png') }}'">
                    </div>
                </div>
            </div>

            <!-- Course Cards Row -->
            <div class="row">
                <div class="col-12">
                    <div class="course-cards-container">
                        <div class="row g-3 justify-content-center" style="margin-top: 0.5rem;">
                            {% for course in popular_courses|slice(0, 4) %}
                            <div class="col-lg-3 col-md-6 mb-4">
                                <div class="course-card h-100" style="
                                    background: white;
                                    border-radius: 15px;
                                    overflow: hidden;
                                    box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
                                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                                    border: 1px solid rgba(255, 255, 255, 0.3);
                                    width: 280px;
                                    height: 320px;
                                    margin: 0 auto 1.5rem auto;
                                    position: relative;
                                    backdrop-filter: blur(10px);
                                " onmouseover="this.style.transform='translateY(-8px) scale(1.03)'; this.style.boxShadow='0 15px 35px rgba(1, 26, 45, 0.25)'"
                                   onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 8px 25px rgba(1, 26, 45, 0.15)'"
                                   style="display: flex; flex-direction: column;">

                                    <!-- Course Thumbnail -->
                                    <div class="course-thumbnail" style="
                                        background: #f8f9fa;
                                        padding: 0.8rem;
                                        text-align: center;
                                        flex: 1;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    ">
                                        {% if course.thumbnailImage %}
                                            <img src="{{ course_image_url(course.thumbnailImage, 'thumbnail') }}"
                                                 alt="{{ course.title }}"
                                                 style="
                                                     width: 100%;
                                                     height: 120px;
                                                     object-fit: cover;
                                                     border-radius: 6px;
                                                     border: 1px solid #dee2e6;
                                                     box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                                 "
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div style="
                                                width: 100%;
                                                height: 120px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
                                                display: none;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 6px;
                                                border: 1px solid #dee2e6;
                                                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                                            ">
                                                <div class="text-center">
                                                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                                                    <div style="font-size: 0.8rem; font-weight: 600;">{{ course.code }}</div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div style="
                                                width: 100%;
                                                height: 120px;
                                                background: linear-gradient(135deg, #011a2d 0%, #1a3461 50%, #a90418 100%);
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                color: white;
                                                border-radius: 6px;
                                                border: 1px solid #dee2e6;
                                                position: relative;
                                                overflow: hidden;
                                            ">
                                                <div class="text-center" style="position: relative; z-index: 1;">
                                                    <i class="fas fa-graduation-cap fa-2x mb-2" style="text-shadow: 0 2px 4px rgba(0,0,0,0.3);"></i>
                                                    <div style="font-size: 0.8rem; font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">{{ course.code }}</div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Course Info Footer -->
                                    <div class="course-footer" style="
                                        background: white;
                                        padding: 1rem;
                                        border-top: 1px solid #dee2e6;
                                        margin-top: auto;
                                    ">
                                        <!-- Course Code and Title on Same Line -->
                                        <div class="d-flex align-items-center justify-content-between mb-3">
                                            <span class="badge" style="
                                                background: #011a2d;
                                                color: white;
                                                font-size: 0.7rem;
                                                font-weight: 600;
                                                padding: 0.3rem 0.6rem;
                                                border-radius: 12px;
                                                flex-shrink: 0;
                                            ">{{ course.code }}</span>
                                            <div style="
                                                font-size: 0.8rem;
                                                font-weight: 600;
                                                color: #011a2d;
                                                line-height: 1.2;
                                                margin-left: 0.5rem;
                                                flex-grow: 1;
                                                text-align: left;
                                            ">
                                                {{ course.title|length > 25 ? course.title|slice(0, 25) ~ '...' : course.title }}
                                            </div>
                                        </div>

                                        <!-- Learn More Button -->
                                        <div class="text-center">
                                            <a href="{{ path('app_course_show', {code: course.code}) }}" class="btn btn-sm w-100" style="
                                                background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                                color: white;
                                                border: none;
                                                border-radius: 20px;
                                                font-size: 0.8rem;
                                                font-weight: 600;
                                                padding: 0.5rem 1rem;
                                                transition: all 0.3s ease;
                                                box-shadow: 0 2px 8px rgba(169, 4, 24, 0.2);
                                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(169, 4, 24, 0.3)'"
                                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(169, 4, 24, 0.2)'">
                                                <i class="fas fa-arrow-right me-2"></i>Learn More
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Enhanced Explore Courses Button -->
                        <div class="text-center mt-5 mb-4">
                            <a href="{{ path('app_courses') }}" class="btn btn-lg px-5 py-3" style="
                                border: 2px solid #971020;
                                border-radius: 8px;
                                font-weight: 600;
                                background: #971020;
                                color: white;
                                text-decoration: none;
                                transition: all 0.3s ease;
                                font-family: 'Montserrat', sans-serif;

                            " onmouseover="this.style.background='white'; this.style.color='#971020'; this.style.transform='translateY(-2px)'"
                               onmouseout="this.style.background='#971020'; this.style.color='white'; this.style.transform='translateY(0)'">
                                <span style="position: relative; z-index: 1;">
                                    <i class="fas fa-graduation-cap me-2"></i>Explore Our Courses
                                </span>
                                <div style="position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s;"></div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Free Trading Stream -->
    <section class="trading-stream-section position-relative" style="
        background-image: url('{{ asset('images/backgrounds/Background 3 Free Trading Stream.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
    ">
        <!-- Overlay -->
        <div class="position-absolute top-0 start-0 w-100 h-100" style="background: rgba(0, 0, 0, 0.6); z-index: 1;"></div>

        <div class="container position-relative" style="z-index: 2;">
            <div class="row align-items-center">
                <!-- Left: Content -->
                <div class="col-lg-6 text-white mb-4 mb-lg-0 pe-5">
                    <h2 class="fw-bold mb-4" style="font-size: 2.5rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;">Free Trading Stream</h2>
                    <p class="mb-0" style="font-size: 1.3rem; line-height: 1.6; text-shadow: 1px 1px 2px rgba(0,0,0,0.5); font-family: 'Calibri', Arial, sans-serif;">
                        Join our experts for real-time market analysis, detailed trade breakdowns, and expert guidance. From beginners to seasoned traders, our stream provides the knowledge you need to make informed decisions and improve your trading skills.
                    </p>
                </div>

                <!-- Right: YouTube Video Embed - Smaller Size -->
                <div class="col-lg-6">
                    <div class="video-embed-container position-relative" style="
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 15px 30px rgba(0,0,0,0.4);
                        background: #000;
                        aspect-ratio: 16/9;
                        max-width: 450px;
                        margin: 0 auto;
                    ">
                        <iframe
                            src="https://www.youtube.com/embed/3ox5jXJvRpU?rel=0&modestbranding=1&showinfo=0"
                            title="Free Trading Stream"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            allowfullscreen
                            style="
                                width: 100%;
                                height: 100%;
                                border-radius: 15px;
                            ">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Past Trading Stream Content within same section -->
        <div class="container position-relative" style="z-index: 2; margin-top: 4rem;">
            <div class="row">
                <div class="col-12 text-white mb-3">
                    <h2 class="fw-bold mb-2" style="font-size: 2rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); font-family: 'Montserrat', sans-serif;">Past Trading Stream</h2>
                    <!-- White horizontal line under title -->
                    <div style="width: 100%; height: 2px; background: white; margin-bottom: 1.5rem;"></div>
                </div>
            </div>

            <!-- 4 Video Thumbnails in a Row -->
            <div class="row g-4 justify-content-center">
                {% if past_videos|length > 0 %}
                    {% for video in past_videos|slice(0, 4) %}
                    <div class="col-lg-3 col-md-6">
                        <div class="video-card position-relative" style="
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            transition: all 0.3s ease;
                            cursor: pointer;
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        " onmouseover="this.style.transform='translateY(-8px) scale(1.02)'; this.style.boxShadow='0 15px 35px rgba(0,0,0,0.6)'"
                           onmouseout="this.style.transform='translateY(0) scale(1)'; this.style.boxShadow='0 10px 25px rgba(0,0,0,0.4)'"
                           onclick="openVideoPlayer({{ video.id }})">
                            <div class="video-thumbnail position-relative" style="height: 160px;">
                                {% if video.thumbnail %}
                                    <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                         alt="{{ video.title }}"
                                         class="w-100 h-100"
                                         style="object-fit: cover;"
                                         onerror="this.src='{{ asset('images/placeholders/video-placeholder.jpg') }}'">
                                {% else %}
                                    <div class="w-100 h-100" style="background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-video text-white" style="font-size: 2rem; opacity: 0.7;"></i>
                                    </div>
                                {% endif %}

                                <!-- Play overlay -->
                                <div class="video-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="
                                    background: rgba(0,0,0,0.3);
                                    transition: all 0.3s ease;
                                ">
                                    <div class="play-button text-center">
                                        <div class="play-icon" style="
                                            width: 50px;
                                            height: 50px;
                                            background: rgba(255,255,255,0.9);
                                            border-radius: 50%;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            margin: 0 auto;
                                            transition: all 0.3s ease;
                                            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                                        ">
                                            <i class="fas fa-play" style="font-size: 1.2rem; margin-left: 2px; color: #011a2d;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="video-info p-3 text-white">
                                <h6 class="fw-bold mb-1" style="font-size: 0.9rem; line-height: 1.3;">{{ video.title|length > 25 ? video.title|slice(0, 25) ~ '...' : video.title }}</h6>
                                <p class="small text-white-50 mb-0">{{ video.category ?? 'Trading Stream' }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    {% for i in 1..4 %}
                    <div class="col-lg-3 col-md-6">
                        <div class="video-card position-relative" style="
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            overflow: hidden;
                            box-shadow: 0 10px 25px rgba(0,0,0,0.4);
                            backdrop-filter: blur(10px);
                            border: 1px solid rgba(255,255,255,0.2);
                        ">
                            <div class="video-thumbnail position-relative" style="height: 160px; background: linear-gradient(135deg, #011a2d 0%, #a90418 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-video text-white" style="font-size: 2rem; opacity: 0.7;"></i>
                            </div>
                            <div class="video-info p-3 text-white">
                                <h6 class="fw-bold mb-1" style="font-size: 0.9rem;">Sample Video {{ i }}</h6>
                                <p class="small text-white-50 mb-0">Trading Stream</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}
            </div>

            <!-- Explore Videos Button -->
            <div class="text-center mt-5">
                <a href="{{ path('app_videos') }}" class="btn btn-outline-light btn-lg px-5 py-3" style="
                    border: 2px solid white;
                    border-radius: 8px;
                    font-weight: 600;
                    background: transparent;
                    color: white;
                    transition: all 0.3s ease;
                    font-family: 'Montserrat', sans-serif;
                    margin-top: 30px;
                    margin-bottom: 20px;
                " onmouseover="this.style.background='white'; this.style.color='#00233e'; this.style.transform='translateY(-2px)'"
                   onmouseout="this.style.background='transparent'; this.style.color='white'; this.style.transform='translateY(0)'">
                    <i class="fas fa-video me-2"></i>Explore Our Videos
                </a>
            </div>

        </div>
    </section>

    <!-- New Analysis Section -->
    <section class="analysis-section position-relative" style="
        background-image: url('{{ asset('images/backgrounds/Background Get your Analysis HP.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    ">
        <div class="container position-relative" style="z-index: 2;">
            <!-- Centered Title with Top Margin -->
            <div class="row mb-5" style="margin-top: 2rem;">
                <div class="col-12 text-center">
                    <h2 class="fw-bold" style="
                        font-size: 3rem;
                        color: #00233e;
                        font-family: 'Montserrat', sans-serif;
                    ">Get Your ANALYSIS Today!</h2>
                </div>
            </div>

            <div class="row align-items-center">
                <!-- Left: Content with proper spacing -->
                <div class="col-lg-6 mb-4 mb-lg-0 pe-5">
                    <h4 class="fw-bold mb-4" style="
                        color: #971020;
                        font-family: 'Montserrat', sans-serif;
                        font-size: 2.2rem;
                    ">Register on our Analysis</h4>

                    <p class="mb-4" style="
                        font-size: 1.4rem;
                        line-height: 1.7;
                        color: #45403f;
                        font-family: 'Calibri', Arial, sans-serif;
                    ">
                        Get personalized financial market analysis from our expert team. Our comprehensive analysis covers market trends, technical indicators, risk assessment, and strategic recommendations tailored to your investment goals. Join thousands of successful traders who rely on our professional insights to make informed trading decisions.
                    </p>
                </div>

                <!-- Right: Email Form with more spacing and smaller container -->
                <div class="col-lg-6 ps-5">
                    <div class="d-flex justify-content-center">
                        <div class="analysis-form-container" style="
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 20px;
                            padding: 25px;
                            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
                            backdrop-filter: blur(10px);
                            max-width: 400px;
                            width: 100%;
                        ">
                        <form class="analysis-form">
                            <div class="mb-3">
                                <input type="text" class="form-control" placeholder="Full Name" style="
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                " onfocus="this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'"
                                   onblur="this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'">
                            </div>
                            <div class="mb-3">
                                <input type="email" class="form-control" placeholder="Email Address" style="
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                " onfocus="this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'"
                                   onblur="this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'">
                            </div>
                            <div class="mb-3">
                                <input type="tel" class="form-control" placeholder="Phone Number" style="
                                    border: 2px solid #e0e0e0;
                                    border-radius: 15px;
                                    padding: 15px 20px;
                                    font-size: 1rem;
                                    transition: all 0.3s ease;
                                    font-family: 'Calibri', Arial, sans-serif;
                                " onfocus="this.style.borderColor='#00233e'; this.style.boxShadow='0 0 0 0.2rem rgba(0,35,62,0.25)'"
                                   onblur="this.style.borderColor='#e0e0e0'; this.style.boxShadow='none'">
                            </div>
                            <button type="submit" class="btn w-100 py-3 fw-bold" style="
                                background: #a90418;
                                color: white;
                                border: none;
                                border-radius: 15px;
                                font-size: 1.1rem;
                                transition: all 0.3s ease;
                                padding: 15px 20px;
                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 10px 25px rgba(169,4,24,0.4)'"
                               onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-chart-line me-2"></i>Get Analysis
                            </button>
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Trading Made Easy Section -->
    <section class="trading-made-easy-section position-relative" style="
        background-image: url('{{ asset('images/backgrounds/Background Trading Made Easy HP.png') }}');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        margin: 0;
        padding: 5rem 0;
        min-height: 100vh;
    ">
        <!-- Overlay -->
        <div class="position-absolute top-0 start-0 w-100 h-100" style="background: rgba(0, 0, 0, 0.3); z-index: 1;"></div>

        <div class="container position-relative" style="z-index: 2;">
            <!-- Centered Container with Top Margin -->
            <div class="row justify-content-start" style="margin-top: 3rem;">
                <div class="col-lg-8 col-xl-6">
                    <div class="glassmorphism-container" style="
                        background: rgba(255, 255, 255, 0.08);
                        backdrop-filter: blur(15px);
                        border-radius: 25px;
                        padding: 50px;
                        border: 2px solid rgba(255, 255, 255, 0.15);
                        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
                        text-align: left;
                        max-width: 600px;
                        margin: 0;
                    ">
                        <h2 class="fw-bold text-white" style="
                            font-size: 2.8rem;
                            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
                            font-family: 'Montserrat', sans-serif;
                            margin-bottom: 2rem;
                        ">Trading Made Easy!</h2>

                        <p class="text-white" style="
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 2rem;
                        ">
                            Learn Technical Analysis at your own pace with professional instructors.
                        </p>

                        <p class="text-white" style="
                            font-size: 1.3rem;
                            line-height: 1.8;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                            font-family: 'Calibri', Arial, sans-serif;
                            margin-bottom: 3rem;
                        ">
                            Register now and Discover Financial Freedom
                        </p>

                        <!-- Email Input with Integrated Button -->
                        <div class="email-input-container position-relative" style="max-width: 500px;">
                            <div class="input-group position-relative" style="border-radius: 25px; overflow: hidden; box-shadow: 0 8px 25px rgba(0,0,0,0.2);">
                                <input type="email" class="form-control" placeholder="Your Email Address ..." style="
                                    border: none;
                                    padding: 18px 120px 18px 20px;
                                    font-size: 1rem;
                                    background: white;
                                    border-radius: 25px;
                                    width: 100%;
                                ">
                                <a href="{{ path('app_contact_registration') }}" class="btn position-absolute" style="
                                    background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 20px;
                                    font-size: 0.9rem;
                                    font-weight: 600;
                                    text-decoration: none;
                                    border-radius: 20px;
                                    transition: all 0.3s ease;
                                    display: flex;
                                    align-items: center;
                                    right: 6px;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    z-index: 10;
                                " onmouseover="this.style.background='linear-gradient(135deg, #8b0314 0%, #6d0210 100%)'"
                                   onmouseout="this.style.background='linear-gradient(135deg, #a90418 0%, #8b0314 100%)'">
                                    <i class="fas fa-rocket me-2"></i>Start Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced seamless partner carousel animation */
@keyframes scroll-partners-seamless {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.partners-carousel {
    animation: scroll-partners-seamless 20s linear infinite;
    white-space: nowrap;
    will-change: transform;
    display: flex;
    align-items: center;
}

.partners-carousel-container {
    mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
    -webkit-mask: linear-gradient(90deg, transparent, white 10%, white 90%, transparent);
}

.partner-set {
    flex-shrink: 0;
}

.partner-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.partner-img {
    max-width: 100%;
    height: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects for partner logos */
.partner-logo:hover .partner-img {
    filter: grayscale(0%) brightness(1) !important;
    transform: scale(1.1) !important;
    opacity: 1 !important;
}

/* Responsive partner carousel */
@media (max-width: 768px) {
    .partner-logo {
        min-width: 100px !important;
        margin: 0 0.75rem !important;
    }

    .partner-img {
        height: 35px !important;
    }

    .partners-carousel {
        animation-duration: 15s !important;
    }
}

/* Floating animation for background elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Hero section responsive adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.1rem !important;
    }

    .stock-ticker .ticker-content {
        font-size: 0.8rem;
    }

    .countdown-timer {
        font-size: 0.8rem;
    }
}

/* Course cards hover effects */
.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.4) !important;
}

/* Video cards hover effects */
.video-card:hover .play-icon {
    transform: scale(1.1);
    background: rgba(255,255,255,0.3) !important;
}

/* Form focus effects */
.form-control:focus, .form-select:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Background parallax effect */
.hero-section,
.courses-section,
.trading-stream-section,
.past-streams-section,
.signup-section {
    background-attachment: fixed;
}

@media (max-width: 768px) {
    .hero-section,
    .courses-section,
    .trading-stream-section,
    .past-streams-section,
    .signup-section {
        background-attachment: scroll;
    }
}

/* Loading animation for images */
img {
    transition: opacity 0.3s ease;
}

/* Ensure footer social media icons are circular on homepage */
footer .footer-social-btn {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid #2c3e50 !important;
    color: #2c3e50 !important;
    background: transparent !important;
    transition: all 0.3s ease !important;
}

footer .footer-social-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
    background: #2c3e50 !important;
    border-color: #2c3e50 !important;
    color: white !important;
}

/* Gradient text animation */
@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.gradient-text {
    background: linear-gradient(-45deg, #00ff00, #ff0000, #00ff00, #ff0000);
    background-size: 400% 400%;
    animation: gradient-shift 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Our Courses Section Responsive Styles */
@media (max-width: 768px) {
    .courses-section h2 {
        font-size: 2.5rem !important;
    }

    .courses-section h3 {
        font-size: 1.5rem !important;
    }

    .courses-section p {
        font-size: 1rem !important;
    }

    .course-card {
        width: 260px !important;
        height: 300px !important;
        margin-bottom: 1.2rem !important;
    }

    .certificate-container {
        margin-top: 2rem;
        transform: rotate(0deg) !important;
    }

    .certificate-container:hover {
        transform: rotate(0deg) scale(1.02) !important;
    }

    .course-card {
        margin-bottom: 2rem !important;
        width: 320px !important;
        height: 320px !important;
    }

    .course-header {
        min-height: 45px !important;
        padding: 0.6rem !important;
    }

    .course-header h5 {
        font-size: 0.8rem !important;
    }

    .course-thumbnail {
        padding: 0.8rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 140px !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.75rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.7rem !important;
    }
}

@media (max-width: 576px) {
    .courses-section h2 {
        font-size: 2rem !important;
    }

    .courses-section h3 {
        font-size: 1.3rem !important;
    }

    .course-card {
        width: 240px !important;
        height: 280px !important;
        margin-bottom: 1rem !important;
    }

    .course-thumbnail img,
    .course-thumbnail div {
        height: 120px !important;
    }

    .course-header {
        min-height: 40px !important;
        padding: 0.5rem !important;
    }

    .course-header h5 {
        font-size: 0.75rem !important;
    }

    .course-footer {
        padding: 0.8rem !important;
    }

    .course-footer .row .col-6 div {
        font-size: 0.7rem !important;
        padding: 0.4rem 0.3rem !important;
    }

    .course-footer .row .col-6 div i {
        font-size: 0.65rem !important;
        margin-bottom: 0.1rem !important;
    }

    .course-footer .row .col-6 div div {
        font-size: 0.65rem !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Homepage specific functionality - countdown timer removed to avoid conflicts with promotional banner

// Enhanced seamless partner carousel
document.addEventListener('DOMContentLoaded', function() {
    const partnersCarousel = document.getElementById('partnersCarousel');

    if (partnersCarousel) {
        // Ensure seamless scrolling by monitoring animation
        partnersCarousel.addEventListener('animationiteration', function() {
            // This event fires each time the animation completes a cycle
            // The CSS animation automatically resets to 0, creating seamless loop
        });

        // Continuous scrolling - no pause on hover

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            partnersCarousel.style.animation = 'none';
            partnersCarousel.style.transform = 'translateX(0)';
        }
    }
});

// Form submission handlers
document.addEventListener('DOMContentLoaded', function() {
    // Analysis form submission
    const analysisForm = document.querySelector('.analysis-form');
    if (analysisForm) {
        analysisForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Success!';
                submitBtn.style.background = '#28a745';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = '#a90418';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }

    // Newsletter form submission (if any remaining)
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Success!';
                submitBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

                // Reset form
                setTimeout(() => {
                    this.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.style.background = 'linear-gradient(135deg, #a90418 0%, #011a2d 100%)';
                    submitBtn.disabled = false;
                }, 2000);
            }, 1500);
        });
    }
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.course-card, .video-card, .benefit-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Video play functionality
function playVideo(container) {
    const overlay = container.querySelector('.video-overlay');
    if (overlay) {
        overlay.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin text-white" style="font-size: 2rem;"></i><p class="text-white mt-2">Loading video...</p></div>';

        // Simulate loading
        setTimeout(() => {
            overlay.innerHTML = '<div class="text-center"><i class="fas fa-play text-white" style="font-size: 2rem;"></i><p class="text-white mt-2">Video would play here</p></div>';
        }, 1500);
    }
}

// Open video player in new tab/window
function openVideoPlayer(videoId) {
    if (videoId) {
        // Open video in new tab
        window.open('/videos/' + videoId, '_blank');
    } else {
        // Fallback for demo videos
        alert('Video player would open here. This is a demo video.');
    }
}

// Video play button interactions
document.querySelectorAll('.play-button, .play-icon').forEach(button => {
    button.addEventListener('click', function(e) {
        e.stopPropagation();
        playVideo(this.closest('.video-container, .video-card'));
    });
});

// Dynamic stats animation
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (stat.textContent.includes('+') || stat.textContent.includes('%')) {
            const originalText = stat.textContent;
            let counter = 0;
            const target = parseFloat(originalText.replace(/[^\d.-]/g, ''));
            const increment = target / 50;

            const timer = setInterval(() => {
                counter += increment;
                if (counter >= target) {
                    stat.textContent = originalText;
                    clearInterval(timer);
                } else {
                    stat.textContent = originalText.replace(target.toString(), Math.floor(counter).toString());
                }
            }, 50);
        }
    });
}

// Trigger stats animation when section is visible
const statsSection = document.querySelector('.trading-stream-section');
if (statsSection) {
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateStats();
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statsObserver.observe(statsSection);
}

// Course Cards Animation
document.addEventListener('DOMContentLoaded', function() {
    // Animate course cards on scroll
    const courseCards = document.querySelectorAll('.course-card');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
                cardObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    courseCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });
});
</script>
{% endblock %}