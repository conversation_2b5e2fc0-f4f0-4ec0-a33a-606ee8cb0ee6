<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Service for securely detecting client IP addresses
 */
class IpAddressService
{
    private RequestStack $requestStack;

    public function __construct(RequestStack $requestStack)
    {
        $this->requestStack = $requestStack;
    }

    /**
     * Get the real client IP address using secure methods
     */
    public function getClientIpAddress(?Request $request = null): string
    {
        if (!$request) {
            $request = $this->requestStack->getCurrentRequest();
        }

        if (!$request) {
            return '127.0.0.1';
        }

        // Use Symfony's built-in method which handles proxies securely
        $clientIp = $request->getClientIp();
        
        // Validate IP address format and exclude private/reserved ranges for public IPs
        if ($clientIp && $this->isValidPublicIp($clientIp)) {
            return $clientIp;
        }
        
        // For development/local environments, allow private IPs
        if ($clientIp && $this->isValidIp($clientIp)) {
            return $clientIp;
        }
        
        // Fallback to localhost
        return '127.0.0.1';
    }

    /**
     * Check if IP is valid and public
     */
    private function isValidPublicIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false;
    }

    /**
     * Check if IP is valid (including private ranges)
     */
    private function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * Get IP address with location info (for future enhancement)
     */
    public function getIpWithInfo(?Request $request = null): array
    {
        $ip = $this->getClientIpAddress($request);
        
        return [
            'ip' => $ip,
            'is_public' => $this->isValidPublicIp($ip),
            'is_local' => in_array($ip, ['127.0.0.1', '::1', 'localhost']),
            'timestamp' => new \DateTimeImmutable(),
        ];
    }
}
