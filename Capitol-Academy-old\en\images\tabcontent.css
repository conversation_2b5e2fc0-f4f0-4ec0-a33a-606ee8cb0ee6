﻿
ul.tabs
{
	
	
	padding:2px 0;
    font-size: 0;
    margin:0;
    list-style-type: none;
    text-align: left; /*set to left, center, or right to align the tabs as desired*/
}
        
ul.tabs li
{
    display: inline;
    margin: 0;
    margin-right:4px; /*distance between tabs*/
	
	
}
        
ul.tabs li a
{
	font-family:Tahoma;
	font-size:13px;
	font-style:normal;
	font-weight:bold;   
    text-decoration: none;
    position: relative;
    z-index: 1;
    padding:7px 12px 5px 12px;
    border: 1px solid #CCC;
    border-bottom-color:#B7B7B7;
	border-top:3px solid #626262;
	color: #626262;  
   border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -webkit-border-top-left-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    outline:none;
	margin-right:1px;
	
	
	
}
        
ul.tabs li a:visited
{
    color: #626262;
}
        
ul.tabs li a:hover
{
    border: 1px solid #B7B7B7;
	border-top:3px solid #626262;
   
}
        
ul.tabs li.selected a
{
    /*selected tab style */
    position: relative;
    top: 0px;
    font-weight:bold;
    background: white;
    border: 1px solid #B7B7B7;
    border-bottom-color: white;
	border-top:3px solid #8eb031;
	color:#8eb031;
}
        
        

        
div.tabcontent
{
    display: block;
}

div.tabcontents
{
    border: 1px solid #B7B7B7;
	 padding: 10px;
    background-color:#FFF;
    border-radius: 0 0 5px 5px;
    -moz-border-radius:0 0 5px 5px ;
    -webkit-border-bottom-left-radius: 5px;
    -webkit-border-bottom-right-radius: 5px;
	width:723px;
}