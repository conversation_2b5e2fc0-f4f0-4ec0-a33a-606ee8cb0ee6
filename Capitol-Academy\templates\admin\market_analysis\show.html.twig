{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Market Analysis',
    'entity_title': article.title,
    'entity_code': article.assetTypeLabel,
    'entity_icon': 'fas fa-chart-line',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_market_analysis_index', 'title': 'Market Analysis'},
        {'title': article.title, 'active': true}
    ],
    'edit_path': path('admin_market_analysis_edit_readable', {'slug': article.slug}),
    'back_path': path('admin_market_analysis_index'),
    'print_function': 'printMarketAnalysisDetails'
} %}

{% block preview_content %}

    <!-- Article Information -->
    <div class="row">
        <!-- Title and Asset Type -->
        <div class="col-md-8">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-heading text-primary mr-1"></i>
                    Title
                </label>
                <div class="enhanced-display-field">
                    {{ article.title }}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-chart-bar text-primary mr-1"></i>
                    Asset Type
                </label>
                <div class="enhanced-display-field">
                    <span class="badge" style="background: #011a2d; color: white; padding: 0.5rem 1rem; border-radius: 20px;">
                        {{ article.assetTypeLabel }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Author and Publish Date Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-user-edit text-primary mr-1"></i>
                    Author
                </label>
                <div class="enhanced-display-field">
                    {{ article.author ?? 'Capitol Academy Analyst' }}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar text-primary mr-1"></i>
                    Publish Date
                </label>
                <div class="enhanced-display-field">
                    {{ article.publishDate|date('F j, Y \\a\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Excerpt (Full Width) -->
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-quote-left text-primary mr-1"></i>
            Excerpt
        </label>
        <div class="enhanced-display-field" style="line-height: 1.6; min-height: 80px;">
            {{ article.excerpt|nl2br }}
        </div>
    </div>

    <!-- Content -->
    {% if article.content %}
    <div class="form-group">
        <label class="form-label">
            <i class="fas fa-file-text text-primary mr-1"></i>
            Content
        </label>
        <div class="enhanced-display-field" style="line-height: 1.6; min-height: 200px; max-height: 400px; overflow-y: auto;">
            {{ article.content|raw }}
        </div>
    </div>
    {% endif %}

    <!-- Images Row -->
    <div class="row">
        <!-- Thumbnail Image -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-image text-primary mr-1"></i>
                    Thumbnail Image
                </label>
                <div class="enhanced-display-field text-center" style="min-height: 150px; display: flex; align-items: center; justify-content: center;">
                    {% if article.thumbnailImage %}
                        <img src="{{ asset('uploads/market_analysis/' ~ article.thumbnailImage) }}" 
                             alt="Thumbnail" 
                             class="img-fluid rounded" 
                             style="max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;">
                    {% else %}
                        <div class="text-muted">
                            <i class="fas fa-image fa-3x mb-2"></i>
                            <br>No thumbnail image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Featured Image -->
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-star text-primary mr-1"></i>
                    Featured Image
                </label>
                <div class="enhanced-display-field text-center" style="min-height: 150px; display: flex; align-items: center; justify-content: center;">
                    {% if article.featuredImage %}
                        <img src="{{ asset('uploads/market_analysis/' ~ article.featuredImage) }}" 
                             alt="Featured" 
                             class="img-fluid rounded" 
                             style="max-height: 120px; max-width: 100%; object-fit: cover; border: 2px solid #dee2e6;">
                    {% else %}
                        <div class="text-muted">
                            <i class="fas fa-star fa-3x mb-2"></i>
                            <br>No featured image
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Date and Views Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar-plus text-primary mr-1"></i>
                    Publish Date
                </label>
                <div class="enhanced-display-field">
                    {{ article.publishDate|date('F j, Y \\a\\t g:i A') }}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-eye text-primary mr-1"></i>
                    Views
                </label>
                <div class="enhanced-display-field">
                    {{ article.views|number_format }}
                </div>
            </div>
        </div>
    </div>

    <!-- Status and Created Date Row (Last Line) -->
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-toggle-on text-primary mr-1"></i>
                    Status
                </label>
                <div class="enhanced-display-field">
                    {% if article.isActive %}
                        <span class="badge bg-success">Active</span>
                    {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar text-primary mr-1"></i>
                    Created Date
                </label>
                <div class="enhanced-display-field">
                    {% if article.createdAt %}
                        {{ article.createdAt|date('F j, Y \\a\\t g:i A') }}
                    {% else %}
                        Not available
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Updated Date (if needed) -->
    {% if article.updatedAt and article.updatedAt != article.createdAt %}
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-calendar-edit text-primary mr-1"></i>
                    Last Updated
                </label>
                <div class="enhanced-display-field">
                    {{ article.updatedAt|date('F j, Y \\a\\t g:i A') }}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

{% endblock %}
{% endembed %}

{% block stylesheets %}
<style>
/* Remove bold font-weight from enhanced-display-field elements */
.enhanced-display-field {
    font-weight: normal !important;
}

/* Remove bold font-weight from form labels */
.form-label {
    font-weight: normal !important;
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Print function for the preview layout
function printMarketAnalysisDetails() {
    window.print();
}
</script>
{% endblock %}
