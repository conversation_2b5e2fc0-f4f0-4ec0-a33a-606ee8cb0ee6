<?php

namespace App\Service;

use App\Entity\Course;
use App\Entity\User;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class StripeService
{
    private string $secretKey;
    private string $publishableKey;
    private string $webhookSecret;
    private UrlGeneratorInterface $urlGenerator;

    public function __construct(UrlGeneratorInterface $urlGenerator)
    {
        $this->secretKey = $_ENV['STRIPE_SECRET_KEY'] ?? '';
        $this->publishableKey = $_ENV['STRIPE_PUBLISHABLE_KEY'] ?? '';
        $this->webhookSecret = $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '';
        $this->urlGenerator = $urlGenerator;

        if ($this->secretKey) {
            Stripe::setApiKey($this->secretKey);
        }
    }

    public function getPublishableKey(): string
    {
        return $this->publishableKey;
    }

    public function createCheckoutSession(Course $course, User $user): Session
    {
        $successUrl = $this->urlGenerator->generate('payment_success', [], UrlGeneratorInterface::ABSOLUTE_URL);
        $cancelUrl = $this->urlGenerator->generate('payment_cancel', [], UrlGeneratorInterface::ABSOLUTE_URL);

        return Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => $course->getTitle(),
                        'description' => $course->getDescription() ? substr($course->getDescription(), 0, 500) : 'Capitol Academy Course',
                        'images' => $course->getThumbnailImage() ? [
                            $this->urlGenerator->generate('app_home', [], UrlGeneratorInterface::ABSOLUTE_URL) .
                            'images/uploads/courses/thumbnails/' . $course->getThumbnailImage()
                        ] : [],
                    ],
                    'unit_amount' => (int) ($course->getPrice() * 100), // Convert to cents
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => $successUrl . '?session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => $cancelUrl,
            'customer_email' => $user->getEmail(),
            'metadata' => [
                'course_id' => $course->getId(),
                'user_id' => $user->getId(),
                'course_code' => $course->getCode(),
            ],
            'payment_intent_data' => [
                'metadata' => [
                    'course_id' => $course->getId(),
                    'user_id' => $user->getId(),
                    'course_code' => $course->getCode(),
                ],
            ],
        ]);
    }

    public function retrieveCheckoutSession(string $sessionId): Session
    {
        return Session::retrieve($sessionId);
    }

    public function constructWebhookEvent(string $payload, string $signature): \Stripe\Event
    {
        return Webhook::constructEvent($payload, $signature, $this->webhookSecret);
    }

    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            $this->constructWebhookEvent($payload, $signature);
            return true;
        } catch (SignatureVerificationException $e) {
            return false;
        }
    }

    public function formatAmount(int $amountInCents): string
    {
        return number_format($amountInCents / 100, 2);
    }

    public function convertToStripeAmount(float $amount): int
    {
        return (int) ($amount * 100);
    }
}
