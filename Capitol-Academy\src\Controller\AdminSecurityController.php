<?php

namespace App\Controller;

use App\Entity\Admin;
use App\Repository\AdminRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class AdminSecurityController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AdminRepository $adminRepository
    ) {}

    #[Route('/admin/login', name: 'admin_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response
    {
        // Get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();

        // Last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render('admin/security/login.html.twig', [
            'last_username' => $lastUsername,
            'error' => $error,
        ]);
    }

    #[Route('/admin/login_check', name: 'admin_login_check')]
    public function loginCheck(): void
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the security system.');
    }

    #[Route('/admin/logout', name: 'admin_logout')]
    public function logout(): void
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the logout key on your firewall.');
    }

    #[Route('/admin/create-admin', name: 'admin_create_admin')]
    public function createAdmin(
        Request $request, 
        UserPasswordHasherInterface $passwordHasher
    ): Response {
        // This is a temporary route to create the first admin user
        // In production, this should be removed or protected
        
        if ($request->isMethod('POST')) {
            $email = $request->request->get('email');
            $password = $request->request->get('password');
            $firstName = $request->request->get('firstName');
            $lastName = $request->request->get('lastName');
            
            // Check if admin already exists
            $existingAdminByEmail = $this->adminRepository->findByEmail($email);
            $existingAdminByUsername = $this->adminRepository->findByUsername($request->request->get('username', ''));

            if ($existingAdminByEmail) {
                $this->addFlash('error', 'Admin with this email already exists.');
                return $this->redirectToRoute('admin_create_admin');
            }

            if ($existingAdminByUsername) {
                $this->addFlash('error', 'Admin with this username already exists.');
                return $this->redirectToRoute('admin_create_admin');
            }
            
            $admin = new Admin();
            $admin->setUsername($request->request->get('username'));
            $admin->setEmail($email);
            $admin->setFirstName($firstName);
            $admin->setLastName($lastName);
            $admin->setPassword($passwordHasher->hashPassword($admin, $password));
            $admin->setRoles(['ROLE_ADMIN']);
            $admin->setIpAddress($this->getRealIpAddress($request));
            $admin->setIsActive(true);
            
            $this->entityManager->persist($admin);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Admin account created successfully! You can now log in.');
            return $this->redirectToRoute('admin_login');
        }
        
        return $this->render('admin/security/create_admin.html.twig');
    }

    /**
     * Get real IP address, works behind proxies and load balancers
     */
    private function getRealIpAddress(Request $request): string
    {
        // Check for IP from shared internet
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        }
        // Check for IP passed from proxy
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            // Can contain multiple IPs, get the first one
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ips[0]);
        }
        // Check for IP from remote address
        elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
            return $_SERVER['HTTP_X_REAL_IP'];
        }
        // Check for IP from remote address
        elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            return $_SERVER['REMOTE_ADDR'];
        }
        // Fallback to Symfony's method
        else {
            return $request->getClientIp() ?? '0.0.0.0';
        }
    }
}
