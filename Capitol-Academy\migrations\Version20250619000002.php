<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Capitol Academy - Instructor Management System Migration
 * Creates instructor table for admin-managed instructor profiles
 */
final class Version20250619000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create instructor table for Capitol Academy instructor management system';
    }

    public function up(Schema $schema): void
    {
        // Create instructor table
        $this->addSql('CREATE TABLE instructor (
            id INT AUTO_INCREMENT NOT NULL, 
            name VARCHAR(255) NOT NULL, 
            bio LONGTEXT DEFAULT NULL, 
            specialization VARCHAR(255) DEFAULT NULL, 
            email VARCHAR(255) DEFAULT NULL, 
            phone VARCHAR(20) DEFAULT NULL, 
            linkedin_url VARCHAR(255) DEFAULT NULL, 
            profile_image VARCHAR(255) DEFAULT NULL, 
            display_order INT NOT NULL DEFAULT 0, 
            is_active TINYINT(1) NOT NULL DEFAULT 1, 
            qualifications JSON NOT NULL, 
            achievements JSON NOT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create indexes for better performance
        $this->addSql('CREATE INDEX IDX_instructor_active ON instructor (is_active)');
        $this->addSql('CREATE INDEX IDX_instructor_display_order ON instructor (display_order)');
        $this->addSql('CREATE INDEX IDX_instructor_name ON instructor (name)');

        // Insert default instructors
        $this->addSql("INSERT INTO instructor (
            name, 
            bio, 
            specialization, 
            email, 
            phone, 
            linkedin_url, 
            profile_image, 
            display_order, 
            is_active, 
            qualifications, 
            achievements, 
            created_at, 
            updated_at
        ) VALUES 
        (
            'Mourad HAMROUNI', 
            'Experienced financial markets educator with over 15 years in trading and market analysis. Specializes in technical analysis and risk management strategies for professional traders.', 
            'Technical Analysis & Risk Management', 
            '<EMAIL>', 
            '+216 70 123 456', 
            'https://tn.linkedin.com/in/mourad-hamrouni', 
            'instructor-default-pp.png', 
            1, 
            1, 
            '[\"CFA Charterholder\", \"FRM Certified\", \"Master in Finance\"]', 
            '[\"15+ years trading experience\", \"Published market research\", \"Trained 1000+ traders\"]', 
            NOW(), 
            NOW()
        ),
        (
            'Rabaa KHESSIBI', 
            'Expert in fundamental analysis and market economics with extensive experience in financial modeling and investment strategies. Passionate about educating the next generation of traders.', 
            'Fundamental Analysis & Economics', 
            '<EMAIL>', 
            '+216 70 123 457', 
            'https://tn.linkedin.com/in/rabaa-khessibi', 
            'instructor-default-pp.png', 
            2, 
            1, 
            '[\"PhD in Economics\", \"CPA Certified\", \"Investment Banking Background\"]', 
            '[\"10+ years in investment banking\", \"Economic research publications\", \"Financial modeling expert\"]', 
            NOW(), 
            NOW()
        ),
        (
            'Christine HAMROUNI', 
            'Specialist in forex markets and international trading with deep knowledge of currency analysis and global market dynamics. Focuses on practical trading strategies and market psychology.', 
            'Forex Trading & Market Psychology', 
            '<EMAIL>', 
            '+216 70 123 458', 
            'https://tn.linkedin.com/in/christine-hamrouni', 
            'instructor-default-pp.png', 
            3, 
            1, 
            '[\"Forex Trading Certification\", \"Psychology in Trading\", \"International Markets Specialist\"]', 
            '[\"12+ years forex trading\", \"Market psychology research\", \"International trading expertise\"]', 
            NOW(), 
            NOW()
        )");
    }

    public function down(Schema $schema): void
    {
        // Drop indexes first
        $this->addSql('DROP INDEX IDX_instructor_active ON instructor');
        $this->addSql('DROP INDEX IDX_instructor_display_order ON instructor');
        $this->addSql('DROP INDEX IDX_instructor_name ON instructor');
        
        // Drop table
        $this->addSql('DROP TABLE instructor');
    }
}
