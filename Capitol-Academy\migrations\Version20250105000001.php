<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Replace video_plan table with new plan table and plan_videos relationship
 */
final class Version20250105000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Replace video_plan table with new plan table and plan_videos relationship table';
    }

    public function up(Schema $schema): void
    {
        // Drop the old video_plan table if it exists
        $this->addSql('DROP TABLE IF EXISTS video_plan');

        // Create the new plan table
        $this->addSql('CREATE TABLE plan (
            id INT AUTO_INCREMENT NOT NULL,
            code VARCHAR(10) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description LONGTEXT DEFAULT NULL,
            category VARCHAR(100) DEFAULT NULL,
            banner_image VARCHAR(255) DEFAULT NULL,
            thumbnail_image VARCHAR(255) DEFAULT NULL,
            price NUMERIC(10, 2) DEFAULT NULL,
            enrolled_count INT NOT NULL DEFAULT 0,
            active_enrollments INT NOT NULL DEFAULT 0,
            completed_count INT NOT NULL DEFAULT 0,
            certified_count INT NOT NULL DEFAULT 0,
            average_rating NUMERIC(3, 2) NOT NULL DEFAULT "0.00",
            total_reviews INT NOT NULL DEFAULT 0,
            view_count INT NOT NULL DEFAULT 0,
            level VARCHAR(50) DEFAULT NULL,
            duration INT DEFAULT NULL,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            UNIQUE INDEX UNIQ_DD5A5B7D77153098 (code),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create the plan_videos relationship table
        $this->addSql('CREATE TABLE plan_videos (
            plan_id INT NOT NULL,
            video_id INT NOT NULL,
            INDEX IDX_PLAN_VIDEOS_PLAN (plan_id),
            INDEX IDX_PLAN_VIDEOS_VIDEO (video_id),
            PRIMARY KEY(plan_id, video_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints for plan_videos table
        $this->addSql('ALTER TABLE plan_videos ADD CONSTRAINT FK_PLAN_VIDEOS_PLAN FOREIGN KEY (plan_id) REFERENCES plan (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plan_videos ADD CONSTRAINT FK_PLAN_VIDEOS_VIDEO FOREIGN KEY (video_id) REFERENCES video (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // Drop the new tables
        $this->addSql('ALTER TABLE plan_videos DROP FOREIGN KEY FK_PLAN_VIDEOS_PLAN');
        $this->addSql('ALTER TABLE plan_videos DROP FOREIGN KEY FK_PLAN_VIDEOS_VIDEO');
        $this->addSql('DROP TABLE plan_videos');
        $this->addSql('DROP TABLE plan');

        // Recreate the old video_plan table structure (for rollback purposes)
        $this->addSql('CREATE TABLE video_plan (
            id INT AUTO_INCREMENT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description LONGTEXT DEFAULT NULL,
            video_ids JSON NOT NULL,
            price NUMERIC(10, 2) NOT NULL,
            thumbnail VARCHAR(255) DEFAULT NULL,
            access_duration_days INT DEFAULT NULL,
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            code VARCHAR(10) DEFAULT NULL,
            slug VARCHAR(255) DEFAULT NULL,
            features JSON DEFAULT NULL,
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }
}
