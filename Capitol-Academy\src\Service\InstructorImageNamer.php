<?php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Vich\UploaderBundle\Mapping\PropertyMapping;
use Vich\UploaderBundle\Naming\NamerInterface;
use Vich\UploaderBundle\Naming\ConfigurableInterface;

/**
 * Custom namer for instructor profile images with sequential naming convention
 * Generates filenames like: instructorimage0001.jpg, instructorimage0002.png, etc.
 */
class InstructorImageNamer implements NamerInterface, ConfigurableInterface
{
    private EntityManagerInterface $entityManager;
    private array $options = [];

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function configure(array $options): void
    {
        $this->options = $options;
    }

    public function name($object, PropertyMapping $mapping): string
    {
        $file = $mapping->getFile($object);
        
        if (!$file) {
            throw new \InvalidArgumentException('File cannot be null.');
        }

        // Get the file extension
        $extension = $file->getClientOriginalExtension() ?: $file->guessExtension();
        
        if (!$extension) {
            $extension = 'jpg'; // Default extension
        }

        // Get the next sequential number
        $nextNumber = $this->getNextSequentialNumber();

        // Format the filename with zero-padded number
        return sprintf('instructorimage%04d.%s', $nextNumber, $extension);
    }

    private function getNextSequentialNumber(): int
    {
        try {
            $uploadDir = $this->options['upload_destination'] ?? 'public/uploads/instructors';

            // Ensure the directory exists
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Get all existing instructor image files
            $files = glob($uploadDir . '/instructorimage*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);

            if ($files === false) {
                return 1; // Return 1 if glob fails
            }

            $maxNumber = 0;

            foreach ($files as $file) {
                $filename = basename($file);
                // Extract number from filename like "instructorimage0001.jpg"
                if (preg_match('/instructorimage(\d{4})\./', $filename, $matches)) {
                    $number = (int) $matches[1];
                    if ($number > $maxNumber) {
                        $maxNumber = $number;
                    }
                }
            }

            return $maxNumber + 1;
        } catch (\Exception $e) {
            // Fallback to timestamp-based naming if there's an error
            return (int) date('His'); // Use current time as fallback
        }
    }
}
