<!-- Professional Loading Spinner Component -->
<div id="loading-spinner" class="loading-spinner-overlay hidden" style="display: none;">
    <div class="loading-spinner-container">
        <div class="loading-spinner-content">
            <!-- Spinner Animation with Logo Inside -->
            <div class="loading-spinner-ring">
                <div class="loading-spinner-ring-inner"></div>
                <!-- Capitol Academy Logo Inside Circle -->
                <div class="loading-logo-container">
                    <img src="{{ asset('images/logos/logo-round.png') }}" alt="Capitol Academy" class="loading-logo" onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'loading-logo-fallback\'>CA</div>';">
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Loading Spinner Styles */
.loading-spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
}

.loading-spinner-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner-container {
    text-align: center;
    position: relative;
}

.loading-spinner-content {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Spinner Ring */
.loading-spinner-ring {
    width: 120px;
    height: 120px;
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logo Container - Positioned Inside Circle */
.loading-logo-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 110px;
    height: 110px;
    z-index: 10;
}

.loading-logo {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

.loading-logo-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 24px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
}

.loading-spinner-ring-inner {
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid #1e3c72;
    border-right: 4px solid #2a5298;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    position: relative;
}

.loading-spinner-ring-inner::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid transparent;
    border-top: 2px solid #dc3545;
    border-radius: 50%;
    animation: spin 2s linear infinite reverse;
}



/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}



/* Responsive Design */
@media (max-width: 768px) {
    .loading-spinner-ring {
        width: 100px;
        height: 100px;
    }

    .loading-logo-container {
        width: 55px;
        height: 55px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .loading-spinner-overlay {
        background: rgba(0, 0, 0, 0.95);
    }
}
</style>
