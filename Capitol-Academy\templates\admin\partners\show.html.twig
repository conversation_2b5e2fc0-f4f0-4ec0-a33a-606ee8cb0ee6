{% embed 'components/admin_preview_layout.html.twig' with {
    'entity_name': 'Partner',
    'entity_title': partner.name,
    'entity_code': partner.name,
    'entity_icon': 'fas fa-handshake',
    'breadcrumb_items': [
        {'path': 'admin_dashboard', 'title': 'Home'},
        {'path': 'admin_partners', 'title': 'Partners'},
        {'title': partner.name, 'active': true}
    ],
    'edit_path': path('admin_partner_edit', {'id': partner.id}),
    'back_path': path('admin_partners'),
    'print_function': 'printPartnerDetails'
} %}

{% block preview_content %}
            <!-- Line 1: Partner name and website URL (same line) -->
            <div class="row print-two-column clearfix mb-4">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-handshake text-primary mr-1"></i>
                            Partner Name
                        </label>
                        <div class="enhanced-display-field">
                            {{ partner.name }}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-globe text-primary mr-1"></i>
                            Website URL
                        </label>
                        <div class="enhanced-display-field">
                            {% if partner.websiteUrl %}
                                <a href="{{ partner.websiteUrl }}" target="_blank" class="text-decoration-none" style="color: #011a2d;">
                                    {{ partner.websiteUrl }}
                                    <i class="fas fa-external-link-alt ml-1" style="font-size: 0.8rem;"></i>
                                </a>
                            {% else %}
                                <span class="text-muted">No website URL provided</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 2: Display order and status (same line) -->
            <div class="row print-two-column clearfix mb-4">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-sort-numeric-up text-primary mr-1"></i>
                            Display Order
                        </label>
                        <div class="enhanced-display-field">
                            {{ partner.displayOrder }}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-toggle-on text-primary mr-1"></i>
                            Status
                        </label>
                        <div class="enhanced-display-field">
                            {% if partner.isActive %}
                                <span class="badge bg-success" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            {% else %}
                                <span class="badge bg-secondary" style="font-size: 0.9rem; padding: 0.5rem 1rem;">
                                    <i class="fas fa-pause-circle mr-1"></i>Inactive
                                </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Line 3: Description (full width) -->
            {% if partner.description %}
            <div class="form-group print-full-width mb-4">
                <label class="form-label">
                    <i class="fas fa-align-left text-primary mr-1"></i>
                    Description
                </label>
                <div class="enhanced-display-field" style="min-height: 100px; white-space: pre-wrap;">
                    {{ partner.description }}
                </div>
            </div>
            {% endif %}

            <!-- Line 4: Logo (centered display) -->
            {% if partner.logoPath %}
            <div class="form-group print-full-width mb-4">
                <label class="form-label">
                    <i class="fas fa-image text-primary mr-1"></i>
                    Partner Logo
                </label>
                <div class="logo-container" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 12px; padding: 2rem;">
                    <div class="logo-display text-center">
                        <img src="{{ partner.logoUrl }}" 
                             alt="{{ partner.name }}" 
                             style="max-width: 400px; max-height: 250px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); object-fit: contain;"
                             onerror="this.src='/images/placeholders/image-placeholder.png'">
                    </div>
                </div>
            </div>
            {% endif %}
{% endblock %}

{% block print_body %}
    <!-- Print-specific content for partner details -->
    <div class="print-section">
        <div class="print-section-title">Partner Information</div>
        <div class="print-info-grid">
            <div class="print-info-row">
                <div class="print-info-label">Partner Name:</div>
                <div class="print-info-value">{{ partner.name }}</div>
            </div>
            {% if partner.websiteUrl %}
            <div class="print-info-row">
                <div class="print-info-label">Website URL:</div>
                <div class="print-info-value">{{ partner.websiteUrl }}</div>
            </div>
            {% endif %}
            <div class="print-info-row">
                <div class="print-info-label">Display Order:</div>
                <div class="print-info-value">{{ partner.displayOrder }}</div>
            </div>
            <div class="print-info-row">
                <div class="print-info-label">Status:</div>
                <div class="print-info-value">{{ partner.isActive ? 'Active' : 'Inactive' }}</div>
            </div>
            {% if partner.description %}
            <div class="print-info-row">
                <div class="print-info-label">Description:</div>
                <div class="print-info-value">{{ partner.description }}</div>
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
{% endembed %}
