<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, Academy: All courses "/>
    <title>Academy: All courses</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/course.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="course course-1 notloggedin dir-ltr lang-en_utf8" id="course-index">
<div id="page">
<a class="skip" href="#maincontent">Skip to main content</a>
    <div id="header" class=" clearfix">        <h1 class="headermain">All courses</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a href="../moodle/login/index.html">Login</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">You are here</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../moodle.html">Academy</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> All courses</li></ul></div>
        <div class="navbutton">&nbsp;</div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix"><span id="maincontent"></span><div class="courseboxes box"><ul class="unlist"><li><div class="coursebox clearfix"><div class="info"><div class="name"><a title="Click to enter this course" href="/">Course ASP.net</a></div></div><div class="summary">If you select the custom download option, you will be presented with a series of options to include <br/>
</div></div></li>
</ul>
</div><div class="buttons"></div></div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">You are not logged in. (<a href="../moodle/login/index.html">Login</a>)</div><div class="homelink"><a href="../moodle.html">Home</a></div></div>
</div>
</body>
</html>