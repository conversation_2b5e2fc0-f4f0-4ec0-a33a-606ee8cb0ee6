{% extends 'base.html.twig' %}

{% block title %}Our Courses - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid px-0">
    <!-- Hero Section -->
    <section class="hero-section position-relative" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); min-height: 60vh;">
        <div class="container h-100">
            <div class="row h-100 align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold text-white mb-4">Our Courses</h1>
                    <p class="lead text-white-50 mb-4">
                        Discover our comprehensive range of trading and financial education courses designed to help you succeed in the markets.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="py-5">
        <div class="container">
            {% if courses|length > 0 %}
                <div class="row">
                    {% for course in courses %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm course-card">
                                {% if course.thumbnailImage %}
                                    <img src="{{ course.thumbnailUrl }}" class="card-img-top" alt="{{ course.title }}" style="height: 200px; object-fit: cover;">
                                {% else %}
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                        <i class="fas fa-graduation-cap text-muted fa-3x"></i>
                                    </div>
                                {% endif %}
                                
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-primary">{{ course.code }}</span>
                                        <span class="badge bg-secondary">{{ course.level|default('Beginner') }}</span>
                                    </div>
                                    
                                    <h5 class="card-title fw-bold text-dark">{{ course.title }}</h5>
                                    <p class="card-text text-muted flex-grow-1">
                                        {{ course.description|length > 150 ? course.description|slice(0, 150) ~ '...' : course.description }}
                                    </p>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>{{ course.category }}
                                        </small>
                                    </div>
                                    
                                    {% if course.learningOutcomes|length > 0 %}
                                        <div class="mb-3">
                                            <h6 class="fw-bold text-dark mb-2">What You'll Learn:</h6>
                                            <ul class="list-unstyled">
                                                {% for outcome in course.learningOutcomes|slice(0, 3) %}
                                                    <li class="mb-1">
                                                        <i class="fas fa-check text-success me-2"></i>
                                                        <small>{{ outcome }}</small>
                                                    </li>
                                                {% endfor %}
                                                {% if course.learningOutcomes|length > 3 %}
                                                    <li class="mb-1">
                                                        <small class="text-muted">+ {{ course.learningOutcomes|length - 3 }} more...</small>
                                                    </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="mt-auto">
                                        <a href="{{ path('public_course_detail', {code: course.code}) }}" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-graduation-cap text-muted fa-5x mb-4"></i>
                    <h3 class="text-muted">No Courses Available</h3>
                    <p class="text-muted">Please check back later for new courses.</p>
                </div>
            {% endif %}
        </div>
    </section>
</div>

<style>
.course-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/trading-bg.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}

.hero-section > .container {
    position: relative;
    z-index: 2;
}
</style>
{% endblock %}
