<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove duration field from Video entity
 */
final class Version20250705150000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove duration field from Video entity';
    }

    public function up(Schema $schema): void
    {
        // Remove duration column from video table
        $this->addSql('ALTER TABLE video DROP COLUMN duration');
    }

    public function down(Schema $schema): void
    {
        // Add duration column back to video table
        $this->addSql('ALTER TABLE video ADD COLUMN duration INT NULL COMMENT \'Duration in seconds\'');
    }
}
