<?php

namespace App\Repository;

use App\Entity\Order;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Order>
 *
 * @method Order|null find($id, $lockMode = null, $lockVersion = null)
 * @method Order|null findOneBy(array $criteria, array $orderBy = null)
 * @method Order[]    findAll()
 * @method Order[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Order::class);
    }

    /**
     * Find orders by user
     */
    public function findByUser(User $user, int $limit = null): array
    {
        $qb = $this->createQueryBuilder('o')
            ->where('o.user = :user')
            ->setParameter('user', $user)
            ->orderBy('o.createdAt', 'DESC');

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find completed orders by user
     */
    public function findCompletedOrdersByUser(User $user): array
    {
        return $this->createQueryBuilder('o')
            ->where('o.user = :user')
            ->andWhere('o.paymentStatus = :status')
            ->setParameter('user', $user)
            ->setParameter('status', Order::STATUS_COMPLETED)
            ->orderBy('o.completedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find order by order number
     */
    public function findByOrderNumber(string $orderNumber): ?Order
    {
        return $this->createQueryBuilder('o')
            ->where('o.orderNumber = :orderNumber')
            ->setParameter('orderNumber', $orderNumber)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find orders by payment status
     */
    public function findByPaymentStatus(string $status, int $limit = null): array
    {
        $qb = $this->createQueryBuilder('o')
            ->where('o.paymentStatus = :status')
            ->setParameter('status', $status)
            ->orderBy('o.createdAt', 'DESC');

        if ($limit) {
            $qb->setMaxResults($limit);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find pending orders older than specified minutes
     */
    public function findExpiredPendingOrders(int $minutesOld = 30): array
    {
        $expiredTime = new \DateTimeImmutable('-' . $minutesOld . ' minutes');
        
        return $this->createQueryBuilder('o')
            ->where('o.paymentStatus = :status')
            ->andWhere('o.createdAt < :expiredTime')
            ->setParameter('status', Order::STATUS_PENDING)
            ->setParameter('expiredTime', $expiredTime)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find orders by payment gateway
     */
    public function findByPaymentGateway(string $gateway): array
    {
        return $this->createQueryBuilder('o')
            ->where('o.paymentGateway = :gateway')
            ->setParameter('gateway', $gateway)
            ->orderBy('o.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find order by PayPal transaction ID
     */
    public function findByPaypalTransactionId(string $transactionId): ?Order
    {
        return $this->createQueryBuilder('o')
            ->where('o.paypalTransactionId = :transactionId')
            ->setParameter('transactionId', $transactionId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find order by Stripe payment intent ID
     */
    public function findByStripePaymentIntentId(string $paymentIntentId): ?Order
    {
        return $this->createQueryBuilder('o')
            ->where('o.stripePaymentIntentId = :paymentIntentId')
            ->setParameter('paymentIntentId', $paymentIntentId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Get orders statistics
     */
    public function getOrdersStatistics(): array
    {
        $result = $this->createQueryBuilder('o')
            ->select('
                COUNT(o.id) as total_orders,
                SUM(CASE WHEN o.paymentStatus = :completed THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN o.paymentStatus = :pending THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN o.paymentStatus = :failed THEN 1 ELSE 0 END) as failed_orders,
                SUM(CASE WHEN o.paymentStatus = :completed THEN o.totalPrice ELSE 0 END) as total_revenue
            ')
            ->setParameter('completed', Order::STATUS_COMPLETED)
            ->setParameter('pending', Order::STATUS_PENDING)
            ->setParameter('failed', Order::STATUS_FAILED)
            ->getQuery()
            ->getSingleResult();

        return [
            'total_orders' => (int)$result['total_orders'],
            'completed_orders' => (int)$result['completed_orders'],
            'pending_orders' => (int)$result['pending_orders'],
            'failed_orders' => (int)$result['failed_orders'],
            'total_revenue' => (float)$result['total_revenue']
        ];
    }

    /**
     * Get monthly revenue
     */
    public function getMonthlyRevenue(int $year, int $month): float
    {
        $startDate = new \DateTimeImmutable("$year-$month-01 00:00:00");
        $endDate = $startDate->modify('last day of this month')->setTime(23, 59, 59);

        $result = $this->createQueryBuilder('o')
            ->select('SUM(o.totalPrice) as revenue')
            ->where('o.paymentStatus = :status')
            ->andWhere('o.completedAt >= :startDate')
            ->andWhere('o.completedAt <= :endDate')
            ->setParameter('status', Order::STATUS_COMPLETED)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (float)($result ?? 0);
    }

    /**
     * Get recent orders
     */
    public function getRecentOrders(int $limit = 10): array
    {
        return $this->createQueryBuilder('o')
            ->orderBy('o.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Check if user has purchased specific item
     */
    public function hasUserPurchasedItem(User $user, string $itemType, int $itemId): bool
    {
        $orders = $this->createQueryBuilder('o')
            ->where('o.user = :user')
            ->andWhere('o.paymentStatus = :status')
            ->setParameter('user', $user)
            ->setParameter('status', Order::STATUS_COMPLETED)
            ->getQuery()
            ->getResult();

        foreach ($orders as $order) {
            foreach ($order->getItems() as $item) {
                if ($item['type'] === $itemType && $item['id'] === $itemId) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get user's purchased items
     */
    public function getUserPurchasedItems(User $user): array
    {
        $orders = $this->findCompletedOrdersByUser($user);
        $purchasedItems = [
            'videos' => [],
            'courses' => [],
            'video_plans' => []
        ];

        foreach ($orders as $order) {
            foreach ($order->getItems() as $item) {
                $type = $item['type'];
                if (isset($purchasedItems[$type])) {
                    $purchasedItems[$type][] = [
                        'id' => $item['id'],
                        'title' => $item['title'],
                        'price' => $item['price'],
                        'purchased_at' => $order->getCompletedAt()
                    ];
                }
            }
        }

        return $purchasedItems;
    }

    public function save(Order $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Order $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
