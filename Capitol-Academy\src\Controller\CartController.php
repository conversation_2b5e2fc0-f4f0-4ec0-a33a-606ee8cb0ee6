<?php

namespace App\Controller;

use App\Service\CartService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Psr\Log\LoggerInterface;

#[Route('/cart')]
class CartController extends AbstractController
{
    private CartService $cartService;
    private LoggerInterface $logger;

    public function __construct(CartService $cartService, LoggerInterface $logger)
    {
        $this->cartService = $cartService;
        $this->logger = $logger;
    }

    #[Route('', name: 'app_cart', methods: ['GET'])]
    public function index(): Response
    {
        $cart = $this->cartService->getCart();
        $removedItems = $this->cartService->validateCart();

        return $this->render('cart/index.html.twig', [
            'cart' => $cart,
            'removed_items' => $removedItems,
        ]);
    }

    #[Route('/add', name: 'app_cart_add', methods: ['POST'])]
    public function add(Request $request): JsonResponse
    {
        $type = $request->request->get('type');
        $id = (int) $request->request->get('id');
        $quantity = (int) $request->request->get('quantity', 1);

        if (!in_array($type, ['video', 'video_plan', 'course'])) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item type'
            ], 400);
        }

        if ($id <= 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item ID'
            ], 400);
        }

        if ($quantity <= 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid quantity'
            ], 400);
        }

        // Check if item already exists in cart
        if ($this->cartService->hasItem($type, $id)) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Item is already in your cart'
            ], 409);
        }

        $success = $this->cartService->addItem($type, $id, $quantity);

        if ($success) {
            $cartSummary = $this->cartService->getCartSummary();
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Item added to cart successfully',
                'cart' => $cartSummary
            ]);
        }

        return new JsonResponse([
            'success' => false,
            'message' => 'Failed to add item to cart. Item may not exist or be unavailable.'
        ], 400);
    }

    #[Route('/remove', name: 'app_cart_remove', methods: ['POST'])]
    public function remove(Request $request): JsonResponse
    {
        $type = $request->request->get('type');
        $id = (int) $request->request->get('id');

        if (!in_array($type, ['video', 'video_plan', 'course'])) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item type'
            ], 400);
        }

        if ($id <= 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item ID'
            ], 400);
        }

        $success = $this->cartService->removeItem($type, $id);

        if ($success) {
            $cartSummary = $this->cartService->getCartSummary();
            
            return new JsonResponse([
                'success' => true,
                'message' => 'Item removed from cart',
                'cart' => $cartSummary
            ]);
        }

        return new JsonResponse([
            'success' => false,
            'message' => 'Item not found in cart'
        ], 404);
    }

    #[Route('/update', name: 'app_cart_update', methods: ['POST'])]
    public function update(Request $request): JsonResponse
    {
        $type = $request->request->get('type');
        $id = (int) $request->request->get('id');
        $quantity = (int) $request->request->get('quantity');

        if (!in_array($type, ['video', 'video_plan', 'course'])) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item type'
            ], 400);
        }

        if ($id <= 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid item ID'
            ], 400);
        }

        if ($quantity < 0) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid quantity'
            ], 400);
        }

        $success = $this->cartService->updateQuantity($type, $id, $quantity);

        if ($success) {
            $cartSummary = $this->cartService->getCartSummary();
            
            return new JsonResponse([
                'success' => true,
                'message' => $quantity > 0 ? 'Cart updated' : 'Item removed from cart',
                'cart' => $cartSummary
            ]);
        }

        return new JsonResponse([
            'success' => false,
            'message' => 'Failed to update cart'
        ], 400);
    }

    #[Route('/clear', name: 'app_cart_clear', methods: ['POST'])]
    public function clear(): JsonResponse
    {
        $this->cartService->clear();

        return new JsonResponse([
            'success' => true,
            'message' => 'Cart cleared successfully',
            'cart' => $this->cartService->getCartSummary()
        ]);
    }

    #[Route('/count', name: 'app_cart_count', methods: ['GET'])]
    public function count(): JsonResponse
    {
        return new JsonResponse([
            'count' => $this->cartService->getItemCount(),
            'total' => $this->cartService->getTotal()
        ]);
    }

    #[Route('/summary', name: 'app_cart_summary', methods: ['GET'])]
    public function summary(): JsonResponse
    {
        return new JsonResponse([
            'success' => true,
            'cart' => $this->cartService->getCartSummary(),
            'items' => $this->cartService->getItems()
        ]);
    }

    #[Route('/widget', name: 'app_cart_widget', methods: ['GET'])]
    public function widget(): Response
    {
        $cartSummary = $this->cartService->getCartSummary();
        $items = $this->cartService->getItems();

        return $this->render('cart/widget.html.twig', [
            'cart_summary' => $cartSummary,
            'cart_items' => array_slice($items, 0, 3), // Show only first 3 items
            'total_items' => count($items)
        ]);
    }

    #[Route('/validate', name: 'app_cart_validate', methods: ['POST'])]
    public function validate(): JsonResponse
    {
        $removedItems = $this->cartService->validateCart();
        $cartSummary = $this->cartService->getCartSummary();

        return new JsonResponse([
            'success' => true,
            'removed_items' => $removedItems,
            'cart' => $cartSummary,
            'message' => empty($removedItems) ? 'Cart is valid' : count($removedItems) . ' item(s) were removed from your cart'
        ]);
    }
}
