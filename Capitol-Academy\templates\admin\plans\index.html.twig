{% extends 'admin/base.html.twig' %}

{% block title %}Plans Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Plans Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Plans</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Plans Management',
    'page_icon': 'fas fa-layer-group',
    'search_placeholder': 'Search...',
    'create_button': {
        'url': path('admin_plan_create'),
        'text': 'Add New Plan',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Plans',
            'value': plans|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': plans|filter(plan => plan.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': plans|filter(plan => not plan.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': plans|filter(plan => plan.createdAt and plan.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Videos'},
            {'text': 'Duration'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for plan in plans %}
            {% set row_cells = [
                {
                    'content': '<code class="plan-code bg-light text-dark" style="padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 600;">' ~ plan.code ~ '</code>'
                },
                {
                    'content': '<h6 class="plan-title mb-0 font-weight-bold text-dark">' ~ plan.title ~ '</h6>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ plan.videos|length ~ ' videos</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ plan.formattedDuration ~ '</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">$' ~ (plan.price|default('0')) ~ '</span>'
                },
                {
                    'content': plan.isActive ?
                        '<span class="badge" style="background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-check-circle mr-1"></i> Active</span>' :
                        '<span class="badge" style="background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;"><i class="fas fa-pause-circle mr-1"></i> Inactive</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_plan_preview', {'code': plan.code}) ~ '" class="btn btn-sm shadow-sm" style="background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Preview Plan"><i class="fas fa-eye"></i></a>
                        <a href="' ~ path('admin_plan_edit', {'code': plan.code}) ~ '" class="btn btn-sm shadow-sm" style="background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="Edit Plan"><i class="fas fa-edit"></i></a>
                        <button onclick="showPlanStatusModal(\'' ~ plan.code ~ '\', \'' ~ plan.title ~ '\', ' ~ (plan.isActive ? 'true' : 'false') ~ ')" class="btn btn-sm shadow-sm" style="background: ' ~ (plan.isActive ? '#ffc107' : '#28a745') ~ '; color: ' ~ (plan.isActive ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (plan.isActive ? 'Pause' : 'Activate') ~ ' Plan"><i class="fas fa-' ~ (plan.isActive ? 'pause' : 'play') ~ '"></i></button>
                        <button onclick="showPlanDeleteModal(\'' ~ plan.code ~ '\', \'' ~ plan.title ~ '\')" class="btn btn-sm shadow-sm" style="background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Plan"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'plan-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'plan-row',
            'empty_message': 'No plans found',
            'empty_icon': 'fas fa-layer-group',
            'empty_description': 'Get started by creating your first plan.',
            'search_config': {
                'fields': ['.plan-title']
            }
        } %}
    {% endblock %}
{% endembed %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.plan-row',
        ['.plan-title']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Plan management functions using standardized modals
function showPlanStatusModal(planCode, planTitle, isActive) {
    AdminPageUtils.showStatusModal(planCode, planTitle, isActive, function(code, newStatus) {
        togglePlanStatus(code, newStatus);
    });
}

function showPlanDeleteModal(planCode, planTitle) {
    AdminPageUtils.showDeleteModal(planCode, planTitle, deletePlan);
}

// Actual execution functions
function togglePlanStatus(planCode, newStatus) {
    fetch(`/admin/plans/${planCode}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while updating the plan status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the plan status.');
    });
}

function deletePlan(planCode) {
    fetch(`/admin/plans/${planCode}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the plan');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the plan.');
    });
}
</script>
{% endblock %}

{% endblock %}
