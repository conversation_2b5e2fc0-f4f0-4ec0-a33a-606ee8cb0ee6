<?php

namespace App\Repository;

use App\Entity\Course;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Course>
 */
class CourseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Course::class);
    }

    /**
     * Find all active courses
     */
    public function findActiveCourses(): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.category', 'ASC')
            ->addOrderBy('c.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find course by code
     */
    public function findByCode(string $code): ?Course
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.code = :code')
            ->andWhere('c.is_active = :active')
            ->setParameter('code', $code)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find courses by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.category = :category')
            ->andWhere('c.is_active = :active')
            ->setParameter('category', $category)
            ->setParameter('active', true)
            ->orderBy('c.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get popular courses (for homepage)
     */
    public function findPopularCourses(int $limit = 6): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    //    /**
    //     * @return Course[] Returns an array of Course objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('c')
    //            ->andWhere('c.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('c.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    /**
     * Find courses by delivery mode
     */
    public function findByMode(string $mode): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.mode = :mode')
            ->andWhere('c.is_active = :active')
            ->setParameter('mode', $mode)
            ->setParameter('active', true)
            ->orderBy('c.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search courses by title, description, or category for public search
     */
    public function searchByTitle(string $query, int $limit = 10): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.is_active = :isActive')
            ->andWhere('c.title LIKE :query OR c.description LIKE :query OR c.category LIKE :query OR c.code LIKE :query')
            ->setParameter('isActive', true)
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('c.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}
