{# SEO Meta Tags Component #}
{% set page_title = block('title') ?? 'Capitol Academy - Financial Markets Education' %}
{% set page_description = block('meta_description') ?? 'Capitol Academy offers exceptional education programs in financial markets, trading strategies, and professional development for traders worldwide.' %}
{% set page_keywords = block('meta_keywords') ?? 'financial markets, trading education, forex, technical analysis, fundamental analysis, capitol academy' %}
{% set page_url = app.request.uri %}
{% set site_name = 'Capitol Academy' %}

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ page_url }}">
<meta property="og:title" content="{{ page_title }}">
<meta property="og:description" content="{{ page_description }}">
<meta property="og:image" content="{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}">
<meta property="og:site_name" content="{{ site_name }}">
<meta property="og:locale" content="en_US">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ page_url }}">
<meta property="twitter:title" content="{{ page_title }}">
<meta property="twitter:description" content="{{ page_description }}">
<meta property="twitter:image" content="{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}">

<!-- LinkedIn -->
<meta property="linkedin:owner" content="capitol-academy-tunisie">

<!-- Additional SEO Meta Tags -->
<meta name="author" content="Capitol Academy">
<meta name="publisher" content="Capitol Academy">
<meta name="copyright" content="Capitol Academy {{ 'now'|date('Y') }}">
<meta name="robots" content="index, follow">
<meta name="googlebot" content="index, follow">
<meta name="revisit-after" content="7 days">
<meta name="rating" content="general">
<meta name="distribution" content="global">

<!-- Canonical URL -->
<link rel="canonical" href="{{ page_url }}">

<!-- Preconnect to external domains for performance -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preconnect" href="https://cdn.jsdelivr.net">
<link rel="preconnect" href="https://cdnjs.cloudflare.com">

<!-- DNS Prefetch for better performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//cdn.jsdelivr.net">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

<!-- Structured Data for Organization -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "EducationalOrganization",
  "name": "Capitol Academy",
  "description": "{{ page_description }}",
  "url": "{{ app.request.schemeAndHttpHost }}",
  "logo": "{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}",
  "image": "{{ absolute_url(asset('favicons/android-chrome-512x512.png')) }}",
  "sameAs": [
    "https://tn.linkedin.com/company/capitol-academy-tunisie"
  ],
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "TN"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "customer service",
    "email": "<EMAIL>"
  }
}
</script>
