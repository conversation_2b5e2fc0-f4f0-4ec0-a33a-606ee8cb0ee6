<?php

namespace App\Service;

use App\Entity\Video;
use App\Entity\VideoPlan;
use App\Entity\Course;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for managing shopping cart functionality
 */
class CartService
{
    private const CART_SESSION_KEY = 'shopping_cart';
    
    private SessionInterface $session;
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    public function __construct(
        RequestStack $requestStack,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger
    ) {
        $this->session = $requestStack->getSession();
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Add item to cart
     */
    public function addItem(string $type, int $id, int $quantity = 1): bool
    {
        try {
            $item = $this->getItemDetails($type, $id);
            if (!$item) {
                return false;
            }

            $cart = $this->getCart();
            $itemKey = $type . '_' . $id;

            if (isset($cart['items'][$itemKey])) {
                $cart['items'][$itemKey]['quantity'] += $quantity;
            } else {
                $cart['items'][$itemKey] = [
                    'type' => $type,
                    'id' => $id,
                    'title' => $item['title'],
                    'price' => $item['price'],
                    'quantity' => $quantity,
                    'thumbnail' => $item['thumbnail'] ?? null,
                    'slug' => $item['slug'] ?? null
                ];
            }

            $this->updateCartTotals($cart);
            $this->saveCart($cart);

            $this->logger->info('Item added to cart', [
                'type' => $type,
                'id' => $id,
                'quantity' => $quantity
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error adding item to cart', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Remove item from cart
     */
    public function removeItem(string $type, int $id): bool
    {
        try {
            $cart = $this->getCart();
            $itemKey = $type . '_' . $id;

            if (isset($cart['items'][$itemKey])) {
                unset($cart['items'][$itemKey]);
                $this->updateCartTotals($cart);
                $this->saveCart($cart);

                $this->logger->info('Item removed from cart', [
                    'type' => $type,
                    'id' => $id
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            $this->logger->error('Error removing item from cart', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Update item quantity in cart
     */
    public function updateQuantity(string $type, int $id, int $quantity): bool
    {
        try {
            if ($quantity <= 0) {
                return $this->removeItem($type, $id);
            }

            $cart = $this->getCart();
            $itemKey = $type . '_' . $id;

            if (isset($cart['items'][$itemKey])) {
                $cart['items'][$itemKey]['quantity'] = $quantity;
                $this->updateCartTotals($cart);
                $this->saveCart($cart);

                $this->logger->info('Cart item quantity updated', [
                    'type' => $type,
                    'id' => $id,
                    'quantity' => $quantity
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            $this->logger->error('Error updating cart item quantity', [
                'type' => $type,
                'id' => $id,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get cart contents
     */
    public function getCart(): array
    {
        $cart = $this->session->get(self::CART_SESSION_KEY, [
            'items' => [],
            'total_items' => 0,
            'subtotal' => 0.00,
            'tax' => 0.00,
            'total' => 0.00,
            'created_at' => time(),
            'updated_at' => time()
        ]);

        return $cart;
    }

    /**
     * Get cart item count
     */
    public function getItemCount(): int
    {
        $cart = $this->getCart();
        return $cart['total_items'] ?? 0;
    }

    /**
     * Get cart total
     */
    public function getTotal(): float
    {
        $cart = $this->getCart();
        return (float)($cart['total'] ?? 0.00);
    }

    /**
     * Check if cart is empty
     */
    public function isEmpty(): bool
    {
        $cart = $this->getCart();
        return empty($cart['items']);
    }

    /**
     * Clear cart
     */
    public function clear(): void
    {
        $this->session->remove(self::CART_SESSION_KEY);
        
        $this->logger->info('Cart cleared');
    }

    /**
     * Check if item exists in cart
     */
    public function hasItem(string $type, int $id): bool
    {
        $cart = $this->getCart();
        $itemKey = $type . '_' . $id;
        
        return isset($cart['items'][$itemKey]);
    }

    /**
     * Get cart items as array
     */
    public function getItems(): array
    {
        $cart = $this->getCart();
        return $cart['items'] ?? [];
    }

    /**
     * Convert cart to order format
     */
    public function getOrderItems(): array
    {
        $cart = $this->getCart();
        $orderItems = [];

        foreach ($cart['items'] as $item) {
            $orderItems[] = [
                'type' => $item['type'],
                'id' => $item['id'],
                'title' => $item['title'],
                'price' => $item['price'],
                'quantity' => $item['quantity'],
                'subtotal' => $item['price'] * $item['quantity']
            ];
        }

        return $orderItems;
    }

    /**
     * Get item details from database
     */
    private function getItemDetails(string $type, int $id): ?array
    {
        switch ($type) {
            case 'video':
                $video = $this->entityManager->getRepository(Video::class)->find($id);
                if ($video && $video->isActive()) {
                    return [
                        'title' => $video->getTitle(),
                        'price' => (float)$video->getPrice(),
                        'thumbnail' => $video->getThumbnail(),
                        'slug' => $video->getSlug()
                    ];
                }
                break;

            case 'video_plan':
                $videoPlan = $this->entityManager->getRepository(VideoPlan::class)->find($id);
                if ($videoPlan && $videoPlan->isActive()) {
                    return [
                        'title' => $videoPlan->getTitle(),
                        'price' => (float)$videoPlan->getPrice(),
                        'thumbnail' => $videoPlan->getThumbnail(),
                        'slug' => $videoPlan->getSlug()
                    ];
                }
                break;

            case 'course':
                $course = $this->entityManager->getRepository(Course::class)->find($id);
                if ($course && $course->getIsActive()) {
                    return [
                        'title' => $course->getTitle(),
                        'price' => (float)$course->getPrice(),
                        'thumbnail' => $course->getThumbnailImage(),
                        'slug' => $course->getSlug()
                    ];
                }
                break;
        }

        return null;
    }

    /**
     * Update cart totals
     */
    private function updateCartTotals(array &$cart): void
    {
        $totalItems = 0;
        $subtotal = 0.00;

        foreach ($cart['items'] as $item) {
            $totalItems += $item['quantity'];
            $subtotal += $item['price'] * $item['quantity'];
        }

        $cart['total_items'] = $totalItems;
        $cart['subtotal'] = $subtotal;
        $cart['tax'] = 0.00; // No tax for now
        $cart['total'] = $subtotal + $cart['tax'];
        $cart['updated_at'] = time();
    }

    /**
     * Save cart to session
     */
    private function saveCart(array $cart): void
    {
        $this->session->set(self::CART_SESSION_KEY, $cart);
    }

    /**
     * Get cart summary for display
     */
    public function getCartSummary(): array
    {
        $cart = $this->getCart();
        
        return [
            'item_count' => $cart['total_items'] ?? 0,
            'subtotal' => number_format($cart['subtotal'] ?? 0, 2),
            'tax' => number_format($cart['tax'] ?? 0, 2),
            'total' => number_format($cart['total'] ?? 0, 2),
            'is_empty' => empty($cart['items'])
        ];
    }

    /**
     * Validate cart items (check if items still exist and are active)
     */
    public function validateCart(): array
    {
        $cart = $this->getCart();
        $removedItems = [];
        $validItems = [];

        foreach ($cart['items'] as $itemKey => $item) {
            $itemDetails = $this->getItemDetails($item['type'], $item['id']);
            
            if ($itemDetails) {
                // Update price if it has changed
                if ($itemDetails['price'] !== $item['price']) {
                    $item['price'] = $itemDetails['price'];
                }
                $validItems[$itemKey] = $item;
            } else {
                $removedItems[] = $item;
            }
        }

        if (!empty($removedItems)) {
            $cart['items'] = $validItems;
            $this->updateCartTotals($cart);
            $this->saveCart($cart);

            $this->logger->info('Cart validated, removed invalid items', [
                'removed_count' => count($removedItems)
            ]);
        }

        return $removedItems;
    }
}
