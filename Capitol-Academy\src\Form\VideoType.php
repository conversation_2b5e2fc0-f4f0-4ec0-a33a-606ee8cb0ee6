<?php

namespace App\Form;

use App\Entity\Video;
use App\Repository\CategoryRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class VideoType extends AbstractType
{
    public function __construct(
        private CategoryRepository $categoryRepository
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Video Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter video title',
                    'maxlength' => '255'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => $this->getCategoryChoices(),
                'attr' => [
                    'class' => 'form-select'
                ],
                'required' => true,
                'placeholder' => 'Select Category'
            ])
            ->add('isFree', CheckboxType::class, [
                'label' => 'Free Video',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check if this video should be available for free'
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Price',
                'currency' => 'USD',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00'
                ],
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ],
                'help' => 'Leave empty for free videos'
            ])

            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Enter video description'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])
            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 5MB.'
                    ])
                ],
                'help' => 'Upload a thumbnail image (JPEG, PNG, WebP). Max size: 5MB'
            ])
            ->add('videoFile', FileType::class, [
                'label' => 'Video File',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'video/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '500M',
                        'mimeTypes' => [
                            'video/mp4',
                            'video/avi',
                            'video/mov',
                            'video/wmv',
                            'video/webm'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid video file (MP4, AVI, MOV, WMV, WebM)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 500MB.'
                    ])
                ],
                'help' => 'Upload a video file (MP4, AVI, MOV, WMV, WebM). Max size: 500MB'
            ])

;
    }

    private function getCategoryChoices(): array
    {
        try {
            $categories = $this->categoryRepository->findForVideos();
            $choices = [];

            foreach ($categories as $category) {
                $choices[$category->getName()] = $category->getName();
            }

            return $choices;
        } catch (\Exception $e) {
            // Fallback in case of database error
            return [
                'Technical Analysis' => 'Technical Analysis',
                'Market Analysis' => 'Market Analysis',
                'Trading Strategies' => 'Trading Strategies',
                'Live Trading' => 'Live Trading'
            ];
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Video::class,
        ]);
    }
}
