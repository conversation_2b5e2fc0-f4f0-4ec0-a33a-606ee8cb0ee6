<?php

namespace App\Form;

use App\Entity\Category;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class CategoryType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Category Name',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter category name'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Category name is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Category name cannot be longer than 255 characters'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 3,
                    'placeholder' => 'Enter category description (optional)'
                ],
                'constraints' => [
                    new Length(['max' => 1000, 'maxMessage' => 'Description cannot be longer than 1000 characters'])
                ]
            ])
            ->add('displayInCourses', CheckboxType::class, [
                'label' => 'Display in Course Creation',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check to make this category available in course creation forms'
            ])
            ->add('displayInVideos', CheckboxType::class, [
                'label' => 'Display in Video Creation',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check to make this category available in video creation forms'
            ])
            ->add('isActive', CheckboxType::class, [
                'label' => 'Active',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Uncheck to deactivate this category'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Category::class,
        ]);
    }
}
