<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Clean up Contact entity - remove duplicate fields and standardize naming
 */
final class Version20250618000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Clean up Contact entity by removing duplicate fields (fname/lname) and renaming first_name/last_name to firstName/lastName';
    }

    public function up(Schema $schema): void
    {
        // First, copy data from fname/lname to first_name/last_name if they exist and are different
        $this->addSql('UPDATE contact SET first_name = COALESCE(NULLIF(fname, ""), first_name) WHERE fname IS NOT NULL AND fname != ""');
        $this->addSql('UPDATE contact SET last_name = COALESCE(NULLIF(lname, ""), last_name) WHERE lname IS NOT NULL AND lname != ""');
        
        // Drop the duplicate columns if they exist
        $this->addSql('ALTER TABLE contact DROP COLUMN IF EXISTS fname');
        $this->addSql('ALTER TABLE contact DROP COLUMN IF EXISTS lname');
        
        // Rename columns to match entity properties (camelCase)
        $this->addSql('ALTER TABLE contact CHANGE first_name firstName VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE contact CHANGE last_name lastName VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE contact CHANGE created_at createdAt DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
        $this->addSql('ALTER TABLE contact CHANGE is_processed isProcessed TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // Revert column names back to snake_case
        $this->addSql('ALTER TABLE contact CHANGE firstName first_name VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE contact CHANGE lastName last_name VARCHAR(100) NOT NULL');
        $this->addSql('ALTER TABLE contact CHANGE createdAt created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
        $this->addSql('ALTER TABLE contact CHANGE isProcessed is_processed TINYINT(1) NOT NULL DEFAULT 0');
        
        // Re-add the duplicate columns (though this will lose data)
        $this->addSql('ALTER TABLE contact ADD fname VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE contact ADD lname VARCHAR(100) DEFAULT NULL');
        
        // Copy data back to the duplicate columns
        $this->addSql('UPDATE contact SET fname = first_name, lname = last_name');
    }
}
