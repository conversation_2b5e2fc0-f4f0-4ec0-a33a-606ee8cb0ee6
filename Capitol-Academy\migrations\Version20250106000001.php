<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250106000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add certifications table and update enrollment status field';
    }

    public function up(Schema $schema): void
    {
        // Create certifications table
        $this->addSql('CREATE TABLE certifications (
            id INT AUTO_INCREMENT NOT NULL, 
            enrollment_id INT NOT NULL, 
            student_id INT NOT NULL, 
            course_id INT NOT NULL, 
            certified_by_id INT NOT NULL, 
            certificate_number VARCHAR(20) NOT NULL, 
            certified_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            notes LONGTEXT DEFAULT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            INDEX IDX_82987F2E591CC992 (enrollment_id), 
            INDEX IDX_82987F2ECB944F1A (student_id), 
            INDEX IDX_82987F2E591CC992 (course_id), 
            INDEX IDX_82987F2E6B3CA4B (certified_by_id), 
            UNIQUE INDEX UNIQ_82987F2E8B1A9DC8 (certificate_number), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2E591CC992 FOREIGN KEY (enrollment_id) REFERENCES enrollments (id)');
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2ECB944F1A FOREIGN KEY (student_id) REFERENCES users (id)');
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2E591CC992_COURSE FOREIGN KEY (course_id) REFERENCES courses (id)');
        $this->addSql('ALTER TABLE certifications ADD CONSTRAINT FK_82987F2E6B3CA4B FOREIGN KEY (certified_by_id) REFERENCES admins (id)');

        // Update enrollment table - change is_active to status
        $this->addSql('ALTER TABLE enrollments ADD status VARCHAR(20) NOT NULL DEFAULT \'active\'');
        
        // Update existing data - convert boolean is_active to status
        $this->addSql('UPDATE enrollments SET status = CASE WHEN is_active = 1 THEN \'active\' ELSE \'blocked\' END');
        
        // Drop the old is_active column
        $this->addSql('ALTER TABLE enrollments DROP is_active');

        // Remove duration field from videos table if it exists
        $this->addSql('ALTER TABLE videos DROP COLUMN IF EXISTS duration');
    }

    public function down(Schema $schema): void
    {
        // Drop certifications table
        $this->addSql('DROP TABLE certifications');

        // Restore enrollment is_active field
        $this->addSql('ALTER TABLE enrollments ADD is_active TINYINT(1) NOT NULL DEFAULT 1');
        
        // Convert status back to boolean
        $this->addSql('UPDATE enrollments SET is_active = CASE WHEN status = \'active\' THEN 1 ELSE 0 END');
        
        // Drop status column
        $this->addSql('ALTER TABLE enrollments DROP status');

        // Add back duration field to videos (optional)
        $this->addSql('ALTER TABLE videos ADD duration INT DEFAULT NULL');
    }
}
