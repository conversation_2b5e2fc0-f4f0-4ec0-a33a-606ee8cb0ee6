vich_uploader:
    db_driver: orm

    mappings:
        admin_profiles:
            uri_prefix: /uploads/profiles
            upload_destination: '%kernel.project_dir%/public/uploads/profiles'
            namer: Vich\UploaderBundle\Naming\SmartUniqueNamer
            inject_on_load: false
            delete_on_update: true
            delete_on_remove: true

        homepage_sections:
            uri_prefix: /uploads/homepage
            upload_destination: '%kernel.project_dir%/public/uploads/homepage'
            namer: Vich\UploaderBundle\Naming\SmartUniqueNamer
            inject_on_load: false
            delete_on_update: true
            delete_on_remove: true

        instructor_profiles:
            uri_prefix: /uploads/instructors
            upload_destination: '%kernel.project_dir%/public/uploads/instructors'
            namer:
                service: instructor_image_namer
                options:
                    upload_destination: '%kernel.project_dir%/public/uploads/instructors'
            inject_on_load: false
            delete_on_update: true
            delete_on_remove: true
