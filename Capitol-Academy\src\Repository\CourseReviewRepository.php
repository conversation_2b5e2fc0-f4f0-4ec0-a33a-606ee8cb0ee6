<?php

namespace App\Repository;

use App\Entity\Course;
use App\Entity\CourseReview;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CourseReview>
 */
class CourseReviewRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CourseReview::class);
    }

    /**
     * Find approved reviews for a course
     */
    public function findApprovedByCourse(Course $course): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->orderBy('r.is_featured', 'DESC')
            ->addOrderBy('r.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find featured reviews for a course
     */
    public function findFeaturedByCourse(Course $course, int $limit = 3): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->andWhere('r.is_featured = :featured')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->setParameter('featured', true)
            ->orderBy('r.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find certified reviews for a course
     */
    public function findCertifiedByCourse(Course $course): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->andWhere('r.is_certified = :certified')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->setParameter('certified', true)
            ->orderBy('r.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Calculate average rating for a course
     */
    public function getAverageRating(Course $course): float
    {
        $result = $this->createQueryBuilder('r')
            ->select('AVG(r.rating) as avg_rating')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->getQuery()
            ->getSingleScalarResult();

        return $result ? round((float)$result, 2) : 0.0;
    }

    /**
     * Count total reviews for a course
     */
    public function countByCourse(Course $course): int
    {
        return $this->createQueryBuilder('r')
            ->select('COUNT(r.id)')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Check if user has already reviewed a course
     */
    public function hasUserReviewed(Course $course, User $user): bool
    {
        $count = $this->createQueryBuilder('r')
            ->select('COUNT(r.id)')
            ->andWhere('r.course = :course')
            ->andWhere('r.user = :user')
            ->setParameter('course', $course)
            ->setParameter('user', $user)
            ->getQuery()
            ->getSingleScalarResult();

        return $count > 0;
    }

    /**
     * Find pending reviews for admin approval
     */
    public function findPendingReviews(): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.is_approved = :approved')
            ->setParameter('approved', false)
            ->orderBy('r.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get rating distribution for a course
     */
    public function getRatingDistribution(Course $course): array
    {
        $results = $this->createQueryBuilder('r')
            ->select('r.rating, COUNT(r.id) as count')
            ->andWhere('r.course = :course')
            ->andWhere('r.is_approved = :approved')
            ->setParameter('course', $course)
            ->setParameter('approved', true)
            ->groupBy('r.rating')
            ->orderBy('r.rating', 'DESC')
            ->getQuery()
            ->getResult();

        $distribution = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
        foreach ($results as $result) {
            $distribution[$result['rating']] = (int)$result['count'];
        }

        return $distribution;
    }
}
