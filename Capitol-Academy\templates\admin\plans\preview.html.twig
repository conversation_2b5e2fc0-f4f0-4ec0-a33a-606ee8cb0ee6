{% extends 'admin/base.html.twig' %}

{% block title %}Plan Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Plan Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_plans') }}">Plans</a></li>
<li class="breadcrumb-item active">{{ plan.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-layer-group mr-3" style="font-size: 2rem;"></i>
                        Plan Details: {{ plan.code }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Plan Button (Icon Only) -->
                        <a href="{{ path('admin_plan_edit', {'code': plan.code}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Plan">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Plan Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Plan Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Plans Button -->
                        <a href="{{ path('admin_plans') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">

                        <!-- Plan Code and Title Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Plan Code -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-hashtag text-primary mr-1"></i>
                                        Plan Code
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ plan.code }}
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Title -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-layer-group text-primary mr-1"></i>
                                        Plan Title
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ plan.title }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Details Row -->
                        <div class="row print-four-column clearfix">
                            <!-- Duration -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-clock text-primary mr-1"></i>
                                        Duration (Days)
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        {{ plan.duration ?? 'Not specified' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                        Price (USD)
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                        ${{ plan.price ?? '0.00' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {% if plan.isActive %}
                                            <span class="badge bg-success" style="font-size: 0.75rem; padding: 0.4rem 0.6rem;">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary" style="font-size: 0.75rem; padding: 0.4rem 0.6rem;">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Created Date -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                        Created Date
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {{ plan.createdAt|date('M d, Y h:i A') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Description -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-alt text-primary mr-1"></i>
                                Description
                            </label>
                            <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;">
                                {{ plan.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Included Videos -->
                        {% if plan.videos|length > 0 %}
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-video text-primary mr-1"></i>
                                Included Videos ({{ plan.videos|length }})
                            </label>
                            <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                <div class="row">
                                    {% for video in plan.videos %}
                                    <div class="col-md-6 mb-3">
                                        <a href="{{ path('admin_video_show', {'id': video.id}) }}"
                                           class="video-item-link text-decoration-none">
                                            <div class="d-flex align-items-center p-3 video-item-card"
                                                 style="background: white; border-radius: 8px; border: 1px solid #e9ecef; transition: all 0.3s ease;">
                                                <div class="me-3">
                                                    {% if video.thumbnail %}
                                                        <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                                             alt="{{ video.title }}"
                                                             class="video-thumbnail"
                                                             style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #dee2e6;">
                                                    {% else %}
                                                        <div class="video-placeholder d-flex align-items-center justify-content-center"
                                                             style="width: 60px; height: 40px; background: #f8f9fa; border-radius: 4px; border: 1px solid #dee2e6;">
                                                            <i class="fas fa-play-circle" style="font-size: 1.2rem; color: #6c757d;"></i>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1 video-title" style="color: #011a2d; font-weight: 600;">{{ video.title }}</h6>
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <small class="text-muted">
                                                            {% if video.category %}
                                                                <i class="fas fa-tag me-1"></i>{{ video.category }}
                                                            {% endif %}
                                                        </small>
                                                        <div>
                                                            {% if video.isFree %}
                                                                <span class="badge bg-success">Free</span>
                                                            {% else %}
                                                                <span class="badge bg-primary">${{ video.price }}</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="ms-2">
                                                    <i class="fas fa-external-link-alt" style="color: #6c757d; font-size: 0.8rem;"></i>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                    </div>
                </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border: 2px solid #ced4da !important;
}

.enhanced-display-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background-color: #ffffff !important;
}

/* Enhanced Video Item Styling */
.video-item-link {
    display: block;
    color: inherit;
}

.video-item-card {
    cursor: pointer;
    border: 1px solid #e9ecef !important;
    background: white !important;
}

.video-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    border-color: #011a2d !important;
}

.video-item-card:hover .video-title {
    color: #011a2d !important;
}

.video-item-card:hover .fas.fa-external-link-alt {
    color: #011a2d !important;
}

.video-thumbnail {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-thumbnail {
    transform: scale(1.05);
}

.video-placeholder {
    transition: all 0.3s ease;
}

.video-item-card:hover .video-placeholder {
    background: #e9ecef !important;
}

.video-item-card:hover .video-placeholder i {
    color: #011a2d !important;
}

/* Print Styles */
@media print {
    .btn, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .enhanced-display-field {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}
</style>
{% endblock %}
