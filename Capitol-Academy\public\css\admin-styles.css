/**
 * Capitol Academy Admin Styles
 * Standardized styling for admin interface components
 */

/* ===== CAPITOL ACADEMY COLOR SCHEME ===== */
:root {
    --primary-navy: #011a2d;
    --primary-navy-light: #1a3461;
    --accent-red: #a90418;
    --accent-red-dark: #8b0314;
    --accent-red-light: #c82333;
    --success-green: #28a745;
    --warning-orange: #ffc107;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
    --text-dark: #343a40;
    --text-muted: #6c757d;
    --white: #ffffff;
}

/* ===== ACTION BUTTONS ===== */

/* Base action button styling */
.admin-action-btn {
    border: none !important;
    border-radius: 6px !important;
    padding: 0.5rem 0.75rem !important;
    margin-right: 0.25rem !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.admin-action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.admin-action-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* View/Details Button */
.admin-btn-view {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-btn-view:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
}

/* Edit Button */
.admin-btn-edit {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-btn-edit:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
}

/* Delete Button */
.admin-btn-delete {
    background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-dark) 100%) !important;
    color: white !important;
}

.admin-btn-delete:hover {
    background: linear-gradient(135deg, var(--accent-red-dark) 0%, var(--accent-red-light) 100%) !important;
    color: white !important;
}

/* Toggle/Status Buttons */
.admin-btn-activate {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-btn-activate:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
}

.admin-btn-deactivate {
    background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-dark) 100%) !important;
    color: white !important;
}

.admin-btn-deactivate:hover {
    background: linear-gradient(135deg, var(--accent-red-dark) 0%, var(--accent-red-light) 100%) !important;
    color: white !important;
}

/* Create/Add Button */
.admin-btn-create {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 0.75rem 1.5rem !important;
}

.admin-btn-create:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
    transform: translateY(-2px) !important;
}

/* Special action buttons */
.admin-btn-duplicate {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-btn-duplicate:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
}

.admin-btn-email {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-btn-email:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
}

/* ===== SEARCH FUNCTIONALITY ===== */

/* Professional search container */
.admin-search-container {
    position: relative;
}

.admin-search-input {
    border: 2px solid var(--primary-navy) !important;
    background: var(--white) !important;
    color: var(--text-dark) !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    border-radius: 8px 0 0 8px !important;
    outline: none !important;
}

.admin-search-input:focus {
    border-color: var(--primary-navy) !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    transform: scale(1.02) !important;
}

.admin-search-btn {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    border: 2px solid var(--primary-navy) !important;
    border-left: none !important;
    border-radius: 0 8px 8px 0 !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.admin-search-btn:hover {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--accent-red) 100%) !important;
    color: white !important;
    transform: scale(1.05) !important;
}

/* ===== TABLE STYLING ===== */

/* Professional table styling */
.admin-table {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.admin-table thead {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-table thead th {
    border: none !important;
    padding: 1rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    font-size: 0.85rem !important;
    letter-spacing: 0.5px !important;
}

.admin-table tbody tr {
    transition: all 0.3s ease !important;
    border-bottom: 1px solid rgba(0,0,0,0.05) !important;
}

.admin-table tbody tr:hover {
    background-color: rgba(1, 26, 45, 0.05) !important;
}

.admin-table tbody td {
    padding: 1rem !important;
    vertical-align: middle !important;
    border: none !important;
}

/* ===== STATISTICS CARDS ===== */

.admin-stat-card {
    border: 0 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
}

.admin-stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.admin-stat-icon {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== BADGES AND STATUS ===== */

.admin-badge {
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
}

.admin-badge-active {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
    color: white !important;
}

.admin-badge-inactive {
    background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-dark) 100%) !important;
    color: white !important;
}

.admin-badge-pending {
    background: linear-gradient(135deg, var(--primary-navy-light) 0%, var(--primary-navy) 100%) !important;
    color: white !important;
}

.admin-badge-danger {
    background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-dark) 100%) !important;
    color: white !important;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .admin-action-btn {
        margin-bottom: 0.25rem !important;
    }
    
    .admin-btn-create {
        width: 100% !important;
        margin-bottom: 1rem !important;
    }
    
    .admin-search-container .input-group {
        width: 100% !important;
    }
    
    .admin-table {
        font-size: 0.875rem !important;
    }
    
    .admin-table thead th,
    .admin-table tbody td {
        padding: 0.75rem 0.5rem !important;
    }
}

/* ===== LOADING STATES ===== */

.admin-loading {
    opacity: 0.6 !important;
    pointer-events: none !important;
    position: relative !important;
}

.admin-loading::after {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 20px !important;
    height: 20px !important;
    margin: -10px 0 0 -10px !important;
    border: 2px solid var(--primary-navy) !important;
    border-radius: 50% !important;
    border-top-color: transparent !important;
    animation: admin-spin 1s linear infinite !important;
}

@keyframes admin-spin {
    to {
        transform: rotate(360deg) !important;
    }
}

/* ===== MODAL STYLING ===== */

.admin-modal .modal-content {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    overflow: hidden !important;
}

.admin-modal .modal-header {
    border: none !important;
    padding: 1.5rem !important;
    position: relative !important;
}

.admin-modal .modal-header.bg-danger {
    background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-dark) 100%) !important;
}

.admin-modal .modal-header.bg-warning {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
}

.admin-modal .modal-header.bg-success {
    background: linear-gradient(135deg, var(--primary-navy) 0%, var(--primary-navy-light) 100%) !important;
}

.admin-modal .modal-body {
    padding: 2rem !important;
}

.admin-modal .modal-footer {
    border: none !important;
    padding: 1.5rem !important;
    background: #f8f9fa !important;
}

.admin-modal .btn {
    border-radius: 6px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.admin-modal .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Modal animations */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px) !important;
    transition: all 0.3s ease !important;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0) !important;
}

/* ===== FORM VALIDATION ===== */

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: var(--accent-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(169, 4, 24, 0.25) !important;
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: var(--primary-navy) !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
}

.invalid-feedback {
    display: block !important;
    color: var(--accent-red) !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

.valid-feedback {
    display: block !important;
    color: var(--primary-navy) !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

/* Form error styling */
.form-error {
    background: rgba(169, 4, 24, 0.1) !important;
    border: 1px solid var(--accent-red) !important;
    color: var(--accent-red) !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    margin-bottom: 1rem !important;
    font-size: 0.875rem !important;
}

.form-success {
    background: rgba(1, 26, 45, 0.1) !important;
    border: 1px solid var(--primary-navy) !important;
    color: var(--primary-navy) !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    margin-bottom: 1rem !important;
    font-size: 0.875rem !important;
}

/* ===== STANDARDIZED ADMIN FORM COMPONENTS ===== */

/* Standardized form card structure */
.admin-form-card {
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.admin-form-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Standardized form header */
.admin-form-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
    color: white !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.5rem !important;
}

.admin-form-header .card-title {
    margin-bottom: 0 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
}

.admin-form-header .card-title i {
    margin-right: 0.5rem !important;
}

.admin-form-header .card-tools .btn {
    border-radius: 8px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* Standardized form body */
.admin-form-body {
    padding: 2rem !important;
}

/* Standardized form groups */
.admin-form-group {
    margin-bottom: 1.5rem !important;
    transition: all 0.3s ease !important;
}

.admin-form-group.focused {
    transform: translateY(-2px) !important;
}

.admin-form-group.form-group-hover {
    background-color: rgba(30, 60, 114, 0.02) !important;
    border-radius: 8px !important;
    padding: 0.5rem !important;
    margin: -0.5rem !important;
    margin-bottom: 1rem !important;
}

/* Standardized form labels */
.admin-form-label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
}

.admin-form-label i {
    margin-right: 0.5rem !important;
    color: #1e3c72 !important;
}

.admin-form-label .text-danger {
    margin-left: 0.25rem !important;
}

/* Standardized form controls */
.admin-form-control {
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.admin-form-control:focus {
    border-color: var(--primary-navy) !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px) !important;
}

.admin-form-control:hover {
    border-color: #2a5298 !important;
}

/* Standardized form footer */
.admin-form-footer {
    background: #f8f9fa !important;
    border-top: 1px solid #dee2e6 !important;
    border-radius: 0 0 12px 12px !important;
    padding: 1.5rem 2rem !important;
}

.admin-form-footer .row {
    align-items: center !important;
}

.admin-form-footer .col-md-6:last-child {
    display: flex !important;
    justify-content: flex-end !important;
}

/* Standardized form buttons */
.admin-form-btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
}

.admin-form-btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%) !important;
}

.admin-form-btn-secondary {
    background: #6c757d !important;
    color: white !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    transition: all 0.3s ease !important;
}

.admin-form-btn-secondary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: #5a6268 !important;
}

/* Standardized validation feedback */
.admin-invalid-feedback,
.invalid-feedback {
    display: none !important;
    color: var(--accent-red) !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

/* Show validation feedback only after form submission */
.was-validated .admin-invalid-feedback,
.was-validated .invalid-feedback {
    display: block !important;
}

.admin-valid-feedback {
    display: block !important;
    color: var(--primary-navy) !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
}

/* Standardized input groups */
.admin-input-group {
    margin-bottom: 0.5rem !important;
}

.admin-input-group .form-control {
    border-right: none !important;
}

.admin-input-group-append .btn {
    border-left: none !important;
    border-radius: 0 8px 8px 0 !important;
}

.admin-input-group-append .btn:first-child {
    border-radius: 0 !important;
}

.admin-input-group-append .btn:last-child {
    border-radius: 0 8px 8px 0 !important;
}

/* Standardized form text helpers */
.admin-form-text {
    font-size: 0.875rem !important;
    color: #6c757d !important;
    margin-top: 0.25rem !important;
}

/* ===== ACCESSIBILITY ===== */

.admin-action-btn:focus,
.admin-search-input:focus,
.admin-search-btn:focus,
.form-control:focus,
.form-select:focus,
.admin-form-control:focus {
    outline: 2px solid var(--primary-navy) !important;
    outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .admin-action-btn,
    .admin-badge,
    .admin-form-btn-primary,
    .admin-form-btn-secondary {
        border: 2px solid currentColor !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .admin-action-btn,
    .admin-table tbody tr,
    .admin-stat-card {
        transition: none !important;
    }

    .admin-loading::after {
        animation: none !important;
    }

    .modal.fade .modal-dialog {
        transform: none !important;
        transition: none !important;
    }
}
