<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Create partners table for database-driven partner management
 */
final class Version20250624120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create partners table for managing partner logos and information';
    }

    public function up(Schema $schema): void
    {
        // Create partners table
        $this->addSql('CREATE TABLE partner (
            id INT AUTO_INCREMENT NOT NULL,
            name VARCHAR(255) NOT NULL,
            logo_path VARCHAR(500) NOT NULL,
            display_order INT NOT NULL DEFAULT 0,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            PRIMARY KEY(id),
            INDEX idx_display_order (display_order),
            INDEX idx_active (is_active)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Insert existing partner data
        $this->addSql("INSERT INTO partner (name, logo_path, display_order, is_active, created_at, updated_at) VALUES
            ('World Bank', 'worldbank.png', 1, 1, NOW(), NOW()),
            ('CNBC', 'cnbc.png', 2, 1, NOW(), NOW()),
            ('UIB', 'uib.png', 3, 1, NOW(), NOW()),
            ('MG', 'MG.png', 4, 1, NOW(), NOW()),
            ('BIAT', 'biat.png', 5, 1, NOW(), NOW()),
            ('QNB', 'qnb.png', 6, 1, NOW(), NOW()),
            ('Aramco', 'aramco.png', 7, 1, NOW(), NOW()),
            ('Ooredoo', 'ooredoo.png', 8, 1, NOW(), NOW())
        ");
    }

    public function down(Schema $schema): void
    {
        // Drop partners table
        $this->addSql('DROP TABLE IF EXISTS partner');
    }
}
