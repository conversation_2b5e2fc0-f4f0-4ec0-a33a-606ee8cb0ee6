<?php

namespace App\Controller;

use App\Repository\MarketAnalysisRepository;
use App\Repository\VideoRepository;
use App\Repository\CourseRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class SearchController extends AbstractController
{
    public function __construct(
        private MarketAnalysisRepository $marketAnalysisRepository,
        private VideoRepository $videoRepository,
        private CourseRepository $courseRepository
    ) {}

    #[Route('/search', name: 'app_search', methods: ['GET'])]
    public function search(Request $request): Response
    {
        $query = $request->query->get('q', '');
        $results = [];

        if (strlen($query) >= 2) {
            // Check if query is a specific course code first
            $courseByCode = $this->courseRepository->findOneBy(['code' => strtoupper($query), 'is_active' => true]);
            if ($courseByCode) {
                // Redirect directly to the course page for exact code matches
                return $this->redirectToRoute('app_course_show', ['code' => $courseByCode->getCode()]);
            }

            // Handle general course searches
            if (strtolower($query) === 'courses' || strtolower($query) === 'course') {
                return $this->redirectToRoute('app_courses');
            }

            // Search courses by title, description, and code
            $courses = $this->courseRepository->searchByTitle($query, 10);
            foreach ($courses as $course) {
                $results[] = [
                    'type' => 'course',
                    'title' => $course->getTitle(),
                    'excerpt' => $course->getDescription() ? substr($course->getDescription(), 0, 100) . '...' : '',
                    'url' => $this->generateUrl('app_course_show', ['code' => $course->getCode()]),
                    'category' => 'Trading Courses'
                ];
            }

            // Search market analysis articles
            $articles = $this->marketAnalysisRepository->searchByTitle($query, 10);
            foreach ($articles as $article) {
                $results[] = [
                    'type' => 'article',
                    'title' => $article->getTitle(),
                    'excerpt' => $article->getShortExcerpt(100),
                    'url' => $this->generateUrl('app_market_analysis_show_seo', ['slug' => $article->getSlug()]),
                    'category' => 'Market Analysis'
                ];
            }

            // Search videos
            $videos = $this->videoRepository->searchByTitle($query, 10);
            foreach ($videos as $video) {
                $results[] = [
                    'type' => 'video',
                    'title' => $video->getTitle(),
                    'excerpt' => $video->getDescription() ? substr($video->getDescription(), 0, 100) . '...' : '',
                    'url' => $this->generateUrl('app_video_show', ['id' => $video->getId()]),
                    'category' => 'Trading Videos'
                ];
            }
        }

        return $this->render('search/results.html.twig', [
            'query' => $query,
            'results' => $results,
            'total' => count($results)
        ]);
    }

    #[Route('/api/search/autocomplete', name: 'api_search_autocomplete', methods: ['GET'])]
    public function autocomplete(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        $suggestions = [];

        if (strlen($query) >= 2) {
            // Check for exact course code match first
            $courseByCode = $this->courseRepository->findOneBy(['code' => strtoupper($query), 'is_active' => true]);
            if ($courseByCode) {
                $suggestions[] = [
                    'type' => 'course',
                    'title' => $courseByCode->getCode() . ' - ' . $courseByCode->getTitle(),
                    'url' => $this->generateUrl('app_course_show', ['code' => $courseByCode->getCode()]),
                    'category' => 'Exact Match',
                    'icon' => 'fas fa-bullseye',
                    'priority' => true
                ];
            }

            // Search courses by title, description, and code
            $courses = $this->courseRepository->searchByTitle($query, 5);
            foreach ($courses as $course) {
                // Skip if already added as exact match
                if ($courseByCode && $course->getId() === $courseByCode->getId()) {
                    continue;
                }

                $suggestions[] = [
                    'type' => 'course',
                    'title' => $course->getCode() . ' - ' . $course->getTitle(),
                    'url' => $this->generateUrl('app_course_show', ['code' => $course->getCode()]),
                    'category' => 'Trading Courses',
                    'icon' => 'fas fa-graduation-cap'
                ];
            }

            // Search market analysis articles
            $articles = $this->marketAnalysisRepository->searchByTitle($query, 5);
            foreach ($articles as $article) {
                $suggestions[] = [
                    'type' => 'article',
                    'title' => $article->getTitle(),
                    'url' => $this->generateUrl('app_market_analysis_show_seo', ['slug' => $article->getSlug()]),
                    'category' => 'Market Analysis',
                    'icon' => 'fas fa-chart-line'
                ];
            }

            // Search videos
            $videos = $this->videoRepository->searchByTitle($query, 5);
            foreach ($videos as $video) {
                $suggestions[] = [
                    'type' => 'video',
                    'title' => $video->getTitle(),
                    'url' => $this->generateUrl('app_video_show', ['id' => $video->getId()]),
                    'category' => 'Trading Videos',
                    'icon' => 'fas fa-video'
                ];
            }

            // Add helpful navigation suggestions for common terms
            $commonTerms = [
                'courses' => ['url' => $this->generateUrl('app_courses'), 'title' => 'Browse All Courses', 'icon' => 'fas fa-graduation-cap', 'category' => 'Navigation'],
                'course' => ['url' => $this->generateUrl('app_courses'), 'title' => 'Browse All Courses', 'icon' => 'fas fa-graduation-cap', 'category' => 'Navigation'],
                'videos' => ['url' => $this->generateUrl('app_videos'), 'title' => 'Browse All Videos', 'icon' => 'fas fa-video', 'category' => 'Navigation'],
                'video' => ['url' => $this->generateUrl('app_videos'), 'title' => 'Browse All Videos', 'icon' => 'fas fa-video', 'category' => 'Navigation'],
                'analysis' => ['url' => $this->generateUrl('app_market_analysis'), 'title' => 'Market Analysis', 'icon' => 'fas fa-chart-line', 'category' => 'Navigation'],
                'market' => ['url' => $this->generateUrl('app_market_analysis'), 'title' => 'Market Analysis', 'icon' => 'fas fa-chart-line', 'category' => 'Navigation']
            ];

            $lowerQuery = strtolower($query);
            foreach ($commonTerms as $term => $data) {
                if (strpos($term, $lowerQuery) === 0 && count($suggestions) < 8) {
                    $suggestions[] = [
                        'type' => 'navigation',
                        'title' => $data['title'],
                        'url' => $data['url'],
                        'category' => $data['category'],
                        'icon' => $data['icon']
                    ];
                }
            }
        }

        return $this->json([
            'success' => true,
            'suggestions' => array_slice($suggestions, 0, 8) // Limit to 8 suggestions
        ]);
    }
}
