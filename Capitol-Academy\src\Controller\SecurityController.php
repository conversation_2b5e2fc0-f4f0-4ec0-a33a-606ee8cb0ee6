<?php

namespace App\Controller;

use App\Entity\User;
use App\Form\RegistrationFormType;
use App\Repository\CountryRepository;
use App\Service\IpAddressService;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends AbstractController
{
    public function __construct(
        private IpAddressService $ipAddressService
    ) {}
    #[Route('/login', name: 'app_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response
    {
        // If user is already logged in, redirect based on role
        if ($this->getUser()) {
            if (in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
                return $this->redirectToRoute('admin_dashboard');
            }
            return $this->redirectToRoute('app_home');
        }

        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();
        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render('security/login.html.twig', [
            'last_username' => $lastUsername,
            'error' => $error
        ]);
    }

    #[Route('/register', name: 'app_register')]
    public function register(Request $request, UserPasswordHasherInterface $userPasswordHasher, EntityManagerInterface $entityManager, CountryRepository $countryRepository): Response
    {
        // If user is already logged in, redirect based on role
        if ($this->getUser()) {
            if (in_array('ROLE_ADMIN', $this->getUser()->getRoles())) {
                return $this->redirectToRoute('admin_dashboard');
            }
            return $this->redirectToRoute('app_home');
        }

        $user = new User();
        $form = $this->createForm(RegistrationFormType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // encode the plain password
            $user->setPassword(
                $userPasswordHasher->hashPassword(
                    $user,
                    $form->get('plainPassword')->getData()
                )
            );

            // Handle profile picture upload
            $profilePictureFile = $form->get('profilePicture')->getData();
            if ($profilePictureFile instanceof UploadedFile) {
                $profilePicturePath = $this->handleProfilePictureUpload($profilePictureFile, $user);
                $user->setProfilePicture($profilePicturePath);
            } else {
                // Set default profile picture path
                $user->setProfilePicture('/uploads/users/default-user-profile.png');
            }

            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $user->setIpAddress($ipAddress);

            $user->setIsVerified(true); // Auto-verify for now
            $user->setUpdatedAt(new \DateTimeImmutable());

            $entityManager->persist($user);
            $entityManager->flush();

            $this->addFlash('success', 'Your account has been created successfully! You can now log in.');

            return $this->redirectToRoute('app_login');
        }

        // Get country data for JavaScript
        $countryData = $countryRepository->getCountryDataForJs();

        return $this->render('security/register_new.html.twig', [
            'registrationForm' => $form,
            'countryData' => $countryData,
        ]);
    }

    #[Route('/logout', name: 'app_logout')]
    public function logout(): void
    {
        throw new \LogicException('This method can be blank - it will be intercepted by the logout key on your firewall.');
    }

    private function handleProfilePictureUpload(UploadedFile $file, User $user): string
    {
        // Create uploads/users directory if it doesn't exist
        $uploadsDir = $this->getParameter('kernel.project_dir') . '/public/uploads/users';
        if (!is_dir($uploadsDir)) {
            mkdir($uploadsDir, 0755, true);
        }

        // Generate a unique filename
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = preg_replace('/[^a-zA-Z0-9\-_]/', '', $originalFilename);
        $extension = $file->guessExtension();
        $newFilename = $safeFilename . '-' . uniqid() . '.' . $extension;

        try {
            // Move the file to the uploads directory
            $file->move($uploadsDir, $newFilename);
            return '/uploads/users/' . $newFilename;
        } catch (\Exception $e) {
            // If upload fails, return default profile picture path
            return '/uploads/users/default-user-profile.png';
        }
    }
}
