<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* contact/index.html.twig */
class __TwigTemplate_af589ccc2c5c94f37b1222b46cbdb224 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'body' => [$this, 'block_body'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "contact/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Contact Us - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 6
        yield "    <!-- Section: Contact Form (Two-Column Layout with Background Image) -->
    <section class=\"py-5 position-relative\" style=\"background: url('";
        // line 7
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/backgrounds/Background Any Question Contact Us.png"), "html", null, true);
        yield "') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-white pe-lg-5\" style=\"margin-right: 3rem;\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: white; font-family: 'Montserrat', sans-serif; font-size: 2.3rem;\">
                            <span>Any Question ?</span> <span>Contact us</span>
                        </h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.4rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;\">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class=\"col-lg-5 offset-lg-1\">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);\">
                        <div class=\"card-body p-4\">
                            <form action=\"";
        // line 25
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_contact_unified");
        yield "\" method=\"POST\" class=\"needs-validation\" novalidate>
                                <input type=\"hidden\" name=\"source_page\" value=\"contact\">

                                <div class=\"mb-3\">
                                    <input type=\"text\" name=\"name\" class=\"form-control glassmorphism-input\"
                                           placeholder=\"Name ...\" required
                                           style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide your name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <input type=\"email\" name=\"email\" class=\"form-control glassmorphism-input\"
                                           placeholder=\"Email ...\" required
                                           style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <textarea name=\"message\" class=\"form-control glassmorphism-input\" rows=\"4\"
                                              placeholder=\"Message ...\" required
                                              style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;\"></textarea>
                                    <div class=\"invalid-feedback\">Please provide your message.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);\">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn\"
                                            style=\"background: #28a745; border: 2px solid white; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                                            onmouseover=\"this.style.background='#218838'; this.style.transform='translateY(-2px)'\"
                                            onmouseout=\"this.style.background='#28a745'; this.style.transform='translateY(0)'\">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Contact Form Specific Styles */
.glassmorphism-input {
    color: white !important;
}

.glassmorphism-input:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none !important;
    color: white !important;
}

.glassmorphism-input::placeholder {
    color: rgba(255,255,255,0.7) !important;
    opacity: 1 !important;
}

.form-control:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-check-input:checked {
    background-color: #011a2d;
    border-color: #011a2d;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #011a2d;
}

.was-validated .form-control:invalid {
    border-color: #a90418;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .card-body {
        padding: 2rem !important;
    }

    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Adjust contact section height for mobile */
    section[style*=\"calc(100vh - 80px)\"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = '#011a2d';
            this.style.boxShadow = '0 0 0 0.2rem rgba(1, 26, 45, 0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = '#dee2e6';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*=\"message\"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type=\"submit\"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "contact/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  124 => 25,  103 => 7,  100 => 6,  87 => 5,  64 => 3,  41 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Contact Us - Capitol Academy{% endblock %}

{% block body %}
    <!-- Section: Contact Form (Two-Column Layout with Background Image) -->
    <section class=\"py-5 position-relative\" style=\"background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: calc(100vh - 80px);\">
        <div class=\"container h-100\">
            <div class=\"row align-items-center h-100\">
                <div class=\"col-lg-6\">
                    <!-- Left Column: Title and Text -->
                    <div class=\"text-white pe-lg-5\" style=\"margin-right: 3rem;\">
                        <h2 class=\"h1 fw-bold mb-4\" style=\"color: white; font-family: 'Montserrat', sans-serif; font-size: 2.3rem;\">
                            <span>Any Question ?</span> <span>Contact us</span>
                        </h2>
                        <p class=\"lead mb-4\" style=\"font-size: 1.4rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;\">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class=\"col-lg-5 offset-lg-1\">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class=\"card border-0 shadow-lg\" style=\"background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);\">
                        <div class=\"card-body p-4\">
                            <form action=\"{{ path('app_contact_unified') }}\" method=\"POST\" class=\"needs-validation\" novalidate>
                                <input type=\"hidden\" name=\"source_page\" value=\"contact\">

                                <div class=\"mb-3\">
                                    <input type=\"text\" name=\"name\" class=\"form-control glassmorphism-input\"
                                           placeholder=\"Name ...\" required
                                           style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide your name.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <input type=\"email\" name=\"email\" class=\"form-control glassmorphism-input\"
                                           placeholder=\"Email ...\" required
                                           style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;\">
                                    <div class=\"invalid-feedback\">Please provide a valid email address.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <textarea name=\"message\" class=\"form-control glassmorphism-input\" rows=\"4\"
                                              placeholder=\"Message ...\" required
                                              style=\"border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;\"></textarea>
                                    <div class=\"invalid-feedback\">Please provide your message.</div>
                                </div>

                                <div class=\"mb-3\">
                                    <div class=\"form-check\">
                                        <input class=\"form-check-input\" type=\"checkbox\" id=\"privacyConsent\" name=\"privacy_consent\" checked required>
                                        <label class=\"form-check-label\" for=\"privacyConsent\" style=\"font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);\">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class=\"d-grid\">
                                    <button type=\"submit\" class=\"btn\"
                                            style=\"background: #28a745; border: 2px solid white; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;\"
                                            onmouseover=\"this.style.background='#218838'; this.style.transform='translateY(-2px)'\"
                                            onmouseout=\"this.style.background='#28a745'; this.style.transform='translateY(0)'\">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<style>
/* Contact Form Specific Styles */
.glassmorphism-input {
    color: white !important;
}

.glassmorphism-input:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none !important;
    color: white !important;
}

.glassmorphism-input::placeholder {
    color: rgba(255,255,255,0.7) !important;
    opacity: 1 !important;
}

.form-control:focus {
    border-color: #011a2d !important;
    box-shadow: 0 0 0 0.2rem rgba(1, 26, 45, 0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-check-input:checked {
    background-color: #011a2d;
    border-color: #011a2d;
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #011a2d;
}

.was-validated .form-control:invalid {
    border-color: #a90418;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .display-3 {
        font-size: 2.5rem;
    }

    .lead {
        font-size: 1.1rem;
    }

    .card-body {
        padding: 2rem !important;
    }

    .py-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Adjust contact section height for mobile */
    section[style*=\"calc(100vh - 80px)\"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}
</style>

<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = '#011a2d';
            this.style.boxShadow = '0 0 0 0.2rem rgba(1, 26, 45, 0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = '#dee2e6';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*=\"message\"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type=\"submit\"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin me-2\"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
", "contact/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\contact\\index.html.twig");
    }
}
