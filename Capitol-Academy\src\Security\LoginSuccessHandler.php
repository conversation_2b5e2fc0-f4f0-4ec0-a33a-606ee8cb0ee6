<?php

namespace App\Security;

use App\Entity\Admin;
use App\Entity\User;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;

class LoginSuccessHandler implements AuthenticationSuccessHandlerInterface
{
    public function __construct(
        private RouterInterface $router
    ) {}

    public function onAuthenticationSuccess(Request $request, TokenInterface $token): RedirectResponse
    {
        $user = $token->getUser();

        // Check if user is an admin
        if ($user instanceof Admin) {
            // Update last login time for admin
            $user->setLastLoginAt(new \DateTimeImmutable());

            // Redirect to admin dashboard
            return new RedirectResponse($this->router->generate('admin_dashboard'));
        }

        // Check if user is a regular user
        if ($user instanceof User) {
            // Check for redirect parameter
            $redirectUrl = $request->query->get('redirect');
            if ($redirectUrl) {
                // Validate the redirect URL to ensure it's safe
                $decodedUrl = urldecode($redirectUrl);
                if (filter_var($decodedUrl, FILTER_VALIDATE_URL) &&
                    (strpos($decodedUrl, $request->getSchemeAndHttpHost()) === 0)) {
                    return new RedirectResponse($decodedUrl);
                }
            }

            // Default redirect to homepage
            return new RedirectResponse($this->router->generate('app_home'));
        }

        // Fallback redirect
        return new RedirectResponse($this->router->generate('app_home'));
    }
}
