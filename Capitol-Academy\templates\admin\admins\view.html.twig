{% extends 'admin/base.html.twig' %}

{% block title %}View Administrator - Capitol Academy Admin{% endblock %}

{% block page_title %}Administrator Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_admins') }}">Administrators</a></li>
<li class="breadcrumb-item active">{{ admin.fullName }}</li>
{% endblock %}

{% block content %}
<!-- Professional Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-0" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
            <div class="card-body py-4">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="d-flex align-items-center">
                            {% if admin.profileImage %}
                                <img src="{{ asset('uploads/profiles/' ~ admin.profileImage) }}" 
                                     alt="{{ admin.fullName }}" 
                                     class="rounded-circle me-4"
                                     style="width: 80px; height: 80px; object-fit: cover; border: 3px solid rgba(255,255,255,0.3);">
                            {% else %}
                                <div class="rounded-circle me-4 d-flex align-items-center justify-content-center"
                                     style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border: 3px solid rgba(255,255,255,0.3);">
                                    <span class="fs-2 fw-bold">{{ admin.fullName|slice(0,1)|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <h1 class="h3 mb-2 fw-bold">{{ admin.fullName }}</h1>
                                <p class="mb-0 opacity-90">
                                    <i class="fas fa-user-shield me-2"></i>Administrator Profile
                                    {% if admin.isMasterAdmin %}
                                        <span class="badge bg-warning text-dark ms-2">
                                            <i class="fas fa-crown me-1"></i>Master Admin
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <div class="d-flex flex-wrap gap-2 justify-content-lg-end">
                            {% if not admin.isMasterAdmin %}
                                <a href="{{ path('admin_admin_edit', {'id': admin.id}) }}" class="btn btn-light btn-lg">
                                    <i class="fas fa-edit me-2"></i>Edit Admin
                                </a>
                            {% endif %}
                            <a href="{{ path('admin_admins') }}" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Information -->
<div class="row">
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-info-circle me-2 text-primary"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Full Name</label>
                            <p class="mb-0">{{ admin.fullName }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Username</label>
                            <p class="mb-0">{{ admin.username }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Email Address</label>
                            <p class="mb-0">
                                <a href="mailto:{{ admin.email }}" class="text-decoration-none">
                                    {{ admin.email }}
                                </a>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Account Status</label>
                            <p class="mb-0">
                                {% if admin.isActive %}
                                    <span class="badge bg-success px-3 py-2">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                {% else %}
                                    <span class="badge bg-danger px-3 py-2">
                                        <i class="fas fa-ban me-1"></i>Blocked
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Account Type</label>
                            <p class="mb-0">
                                {% if admin.isMasterAdmin %}
                                    <span class="badge bg-warning text-dark px-3 py-2">
                                        <i class="fas fa-crown me-1"></i>Master Administrator
                                    </span>
                                {% else %}
                                    <span class="badge bg-primary px-3 py-2">
                                        <i class="fas fa-user-shield me-1"></i>Administrator
                                    </span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold text-muted">Created Date</label>
                            <p class="mb-0">{{ admin.createdAt|date('F j, Y \\a\\t g:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-shield-alt me-2 text-primary"></i>Permissions
                </h5>
            </div>
            <div class="card-body">
                {% if admin.isMasterAdmin %}
                    <div class="alert alert-warning border-0">
                        <i class="fas fa-crown me-2"></i>
                        <strong>Master Administrator:</strong> This account has full access to all system features and cannot be restricted.
                    </div>
                {% else %}
                    {% set permissions = admin.permissions %}
                    {% if permissions|length > 0 %}
                        <div class="row">
                            {% for permission in permissions %}
                                <div class="col-md-6 mb-2">
                                    <span class="badge bg-light text-dark border px-3 py-2">
                                        <i class="fas fa-check text-success me-2"></i>{{ permission }}
                                    </span>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info border-0">
                            <i class="fas fa-info-circle me-2"></i>
                            No specific permissions assigned. Contact the master administrator to assign permissions.
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                {% if not admin.isMasterAdmin %}
                    <div class="d-grid gap-2">
                        <a href="{{ path('admin_admin_edit', {'id': admin.id}) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Administrator
                        </a>
                        {% if admin.isActive %}
                            <button type="button" class="btn btn-warning" onclick="toggleAdminStatus({{ admin.id }}, '{{ admin.fullName }}', false)">
                                <i class="fas fa-lock me-2"></i>Block Administrator
                            </button>
                        {% else %}
                            <button type="button" class="btn btn-success" onclick="toggleAdminStatus({{ admin.id }}, '{{ admin.fullName }}', true)">
                                <i class="fas fa-unlock me-2"></i>Unblock Administrator
                            </button>
                        {% endif %}
                        <button type="button" class="btn btn-danger" onclick="confirmDeleteAdmin({{ admin.id }}, '{{ admin.username }}', '{{ admin.fullName }}')">
                            <i class="fas fa-trash me-2"></i>Delete Administrator
                        </button>
                    </div>
                {% else %}
                    <div class="alert alert-info border-0 mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Master administrator account cannot be modified.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Account Statistics -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom py-3">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>Account Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="fw-bold text-primary mb-1">{{ (date().timestamp - admin.createdAt.timestamp) // 86400 }}</h4>
                            <small class="text-muted">Days Active</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="fw-bold text-success mb-1">{{ admin.permissions|length }}</h4>
                        <small class="text-muted">Permissions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
// Toggle admin status (block/unblock)
function toggleAdminStatus(adminId, fullName, activate) {
    const action = activate ? 'activate' : 'block';
    
    if (confirm(`Are you sure you want to ${action} ${fullName}?`)) {
        $.ajax({
            url: `/admin/admin/${adminId}/toggle-status`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    showAdminNotification(response.message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showAdminNotification(response.message, 'error');
                }
            },
            error: function() {
                showAdminNotification('An error occurred while updating the administrator status.', 'error');
            }
        });
    }
}

// Delete admin confirmation
function confirmDeleteAdmin(adminId, username, fullName) {
    if (confirm(`Are you sure you want to delete the administrator account for ${fullName}? This action cannot be undone.`)) {
        $.ajax({
            url: `/admin/admin/${adminId}/delete`,
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.success) {
                    showAdminNotification(response.message, 'success');
                    setTimeout(() => {
                        window.location.href = '{{ path('admin_admins') }}';
                    }, 1000);
                } else {
                    showAdminNotification(response.message, 'error');
                }
            },
            error: function() {
                showAdminNotification('An error occurred while deleting the administrator.', 'error');
            }
        });
    }
}
</script>
{% endblock %}
