<?php

namespace App\Controller;

use App\Entity\User;
use App\Repository\OrderRepository;
use App\Service\AccessControlService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/user/orders')]
#[IsGranted('ROLE_USER')]
class UserOrderController extends AbstractController
{
    private OrderRepository $orderRepository;
    private AccessControlService $accessControlService;

    public function __construct(
        OrderRepository $orderRepository,
        AccessControlService $accessControlService
    ) {
        $this->orderRepository = $orderRepository;
        $this->accessControlService = $accessControlService;
    }

    #[Route('', name: 'app_user_orders', methods: ['GET'])]
    public function index(): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }

        $orders = $this->orderRepository->findByUser($user);
        $accessStats = $this->accessControlService->getUserAccessStatistics($user);

        return $this->render('user/orders/index.html.twig', [
            'orders' => $orders,
            'access_stats' => $accessStats,
        ]);
    }

    #[Route('/{orderNumber}', name: 'app_user_order_show', methods: ['GET'])]
    public function show(string $orderNumber): Response
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }

        $order = $this->orderRepository->findByOrderNumber($orderNumber);

        if (!$order || $order->getUser() !== $user) {
            throw $this->createNotFoundException('Order not found');
        }

        return $this->render('user/orders/show.html.twig', [
            'order' => $order,
        ]);
    }
}
