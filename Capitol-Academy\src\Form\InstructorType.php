<?php

namespace App\Form;

use App\Entity\Instructor;
use Symfony\Component\Form\AbstractType;

use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TelType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Count;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Url;
use Vich\UploaderBundle\Form\Type\VichImageType;

class InstructorType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Full Name',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter instructor full name'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Name is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Name cannot be longer than 255 characters'])
                ]
            ])
            ->add('specialization', TextType::class, [
                'label' => 'Specialization',
                'required' => true,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., Technical Analysis & Risk Management'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Specialization is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Specialization cannot be longer than 255 characters'])
                ]
            ])
            ->add('bio', TextareaType::class, [
                'label' => 'Biography',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 5,
                    'placeholder' => 'Enter instructor biography and background'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Bio cannot be longer than 2000 characters'])
                ]
            ])
            ->add('email', EmailType::class, [
                'label' => 'Email Address',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '<EMAIL>'
                ],
                'constraints' => [
                    new Email(['message' => 'Please enter a valid email address'])
                ]
            ])
            ->add('phone', TelType::class, [
                'label' => 'Phone Number',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '+216 70 123 456'
                ],
                'constraints' => [
                    new Length(['max' => 20, 'maxMessage' => 'Phone cannot be longer than 20 characters'])
                ]
            ])
            ->add('linkedinUrl', UrlType::class, [
                'label' => 'LinkedIn Profile URL',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'https://tn.linkedin.com/in/instructor-name'
                ],
                'constraints' => [
                    new Url(['message' => 'Please enter a valid LinkedIn URL'])
                ]
            ])
            ->add('profileImageFile', VichImageType::class, [
                'label' => 'Profile Image',
                'required' => false,
                'allow_delete' => true,
                'delete_label' => 'Remove current image',
                'download_uri' => false,
                'image_uri' => true,
                'asset_helper' => true,
                'attr' => [
                    'class' => 'form-control'
                ],
                'help' => 'Upload a professional profile image (JPG, PNG, max 5MB)'
            ])
            ->add('displayOrder', IntegerType::class, [
                'label' => 'Display Order',
                'attr' => [
                    'class' => 'form-control',
                    'min' => 1,
                    'placeholder' => 'Order in which instructor appears'
                ],
                'help' => 'Lower numbers appear first'
            ])
            ->add('isActive', CheckboxType::class, [
                'label' => 'Active',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Whether this instructor is active and visible'
            ])


            ->add('qualifications', CollectionType::class, [
                'label' => 'Qualifications',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control mb-2',
                        'placeholder' => 'e.g., CFA Charterholder'
                    ],
                    'required' => false
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'qualification-collection'
                ],
                'help' => 'Add professional qualifications and certifications (optional)',
                'constraints' => [
                    new Count(['max' => 10, 'maxMessage' => 'You can add maximum 10 qualifications'])
                ]
            ])
            ->add('achievements', CollectionType::class, [
                'label' => 'Achievements',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control mb-2',
                        'placeholder' => 'e.g., 15+ years trading experience'
                    ]
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'achievement-collection'
                ],
                'help' => 'Add notable achievements and accomplishments',
                'constraints' => [
                    new Count(['max' => 10, 'maxMessage' => 'You can add maximum 10 achievements'])
                ]
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Save Instructor',
                'attr' => [
                    'class' => 'btn btn-primary btn-lg px-5'
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Instructor::class,
        ]);
    }
}
