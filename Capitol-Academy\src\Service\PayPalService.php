<?php

namespace App\Service;

use App\Entity\Order;
use App\Entity\User;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

/**
 * Service for integrating with PayPal API for payment processing
 */
class PayPalService
{
    private const SANDBOX_API_URL = 'https://api-m.sandbox.paypal.com';
    private const LIVE_API_URL = 'https://api-m.paypal.com';
    
    private HttpClientInterface $httpClient;
    private LoggerInterface $logger;
    private ParameterBagInterface $params;
    private string $clientId;
    private string $clientSecret;
    private bool $isSandbox;
    private ?string $accessToken = null;

    public function __construct(
        HttpClientInterface $httpClient,
        LoggerInterface $logger,
        ParameterBagInterface $params
    ) {
        $this->httpClient = $httpClient;
        $this->logger = $logger;
        $this->params = $params;
        
        // TODO: Add PayPal credentials to environment variables
        $this->clientId = $params->get('paypal.client_id') ?? '';
        $this->clientSecret = $params->get('paypal.client_secret') ?? '';
        $this->isSandbox = $params->get('paypal.sandbox') ?? true;
    }

    /**
     * Get PayPal API base URL
     */
    private function getApiUrl(): string
    {
        return $this->isSandbox ? self::SANDBOX_API_URL : self::LIVE_API_URL;
    }

    /**
     * Get access token for PayPal API
     */
    private function getAccessToken(): ?string
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        try {
            $response = $this->httpClient->request('POST', $this->getApiUrl() . '/v1/oauth2/token', [
                'headers' => [
                    'Accept' => 'application/json',
                    'Accept-Language' => 'en_US',
                ],
                'auth_basic' => [$this->clientId, $this->clientSecret],
                'body' => 'grant_type=client_credentials'
            ]);

            if ($response->getStatusCode() === 200) {
                $data = $response->toArray();
                $this->accessToken = $data['access_token'];
                
                $this->logger->info('PayPal access token obtained successfully');
                
                return $this->accessToken;
            }

            $this->logger->error('Failed to obtain PayPal access token', [
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('PayPal access token error', [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Create PayPal order
     */
    public function createOrder(Order $order): ?array
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            return null;
        }

        try {
            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $order->getOrderNumber(),
                        'amount' => [
                            'currency_code' => 'USD',
                            'value' => $order->getTotalPrice()
                        ],
                        'description' => 'Capitol Academy - Order #' . $order->getOrderNumber(),
                        'items' => $this->formatOrderItems($order)
                    ]
                ],
                'application_context' => [
                    'return_url' => $this->params->get('app.base_url') . '/checkout/success',
                    'cancel_url' => $this->params->get('app.base_url') . '/checkout/cancel',
                    'brand_name' => 'Capitol Academy',
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW'
                ]
            ];

            $response = $this->httpClient->request('POST', $this->getApiUrl() . '/v2/checkout/orders', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $accessToken,
                ],
                'json' => $orderData
            ]);

            if ($response->getStatusCode() === 201) {
                $data = $response->toArray();
                
                $this->logger->info('PayPal order created successfully', [
                    'order_id' => $order->getId(),
                    'paypal_order_id' => $data['id']
                ]);

                return $data;
            }

            $this->logger->error('Failed to create PayPal order', [
                'order_id' => $order->getId(),
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('PayPal order creation error', [
                'order_id' => $order->getId(),
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Capture PayPal order
     */
    public function captureOrder(string $paypalOrderId): ?array
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            return null;
        }

        try {
            $response = $this->httpClient->request('POST', $this->getApiUrl() . '/v2/checkout/orders/' . $paypalOrderId . '/capture', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $accessToken,
                ]
            ]);

            if ($response->getStatusCode() === 201) {
                $data = $response->toArray();
                
                $this->logger->info('PayPal order captured successfully', [
                    'paypal_order_id' => $paypalOrderId
                ]);

                return $data;
            }

            $this->logger->error('Failed to capture PayPal order', [
                'paypal_order_id' => $paypalOrderId,
                'status_code' => $response->getStatusCode()
            ]);

            return null;

        } catch (\Exception $e) {
            $this->logger->error('PayPal order capture error', [
                'paypal_order_id' => $paypalOrderId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Get PayPal order details
     */
    public function getOrderDetails(string $paypalOrderId): ?array
    {
        $accessToken = $this->getAccessToken();
        if (!$accessToken) {
            return null;
        }

        try {
            $response = $this->httpClient->request('GET', $this->getApiUrl() . '/v2/checkout/orders/' . $paypalOrderId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                return $response->toArray();
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('PayPal order details error', [
                'paypal_order_id' => $paypalOrderId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, array $headers): bool
    {
        // TODO: Implement webhook signature verification
        // This is crucial for security in production
        
        $this->logger->info('PayPal webhook signature verification (stub)', [
            'headers' => $headers
        ]);

        return true; // Temporary - implement proper verification
    }

    /**
     * Process webhook event
     */
    public function processWebhookEvent(array $eventData): bool
    {
        try {
            $eventType = $eventData['event_type'] ?? '';
            
            $this->logger->info('Processing PayPal webhook event', [
                'event_type' => $eventType,
                'event_id' => $eventData['id'] ?? null
            ]);

            switch ($eventType) {
                case 'CHECKOUT.ORDER.APPROVED':
                    return $this->handleOrderApproved($eventData);
                
                case 'PAYMENT.CAPTURE.COMPLETED':
                    return $this->handlePaymentCompleted($eventData);
                
                case 'PAYMENT.CAPTURE.DENIED':
                    return $this->handlePaymentDenied($eventData);
                
                default:
                    $this->logger->info('Unhandled PayPal webhook event type', [
                        'event_type' => $eventType
                    ]);
                    return true;
            }

        } catch (\Exception $e) {
            $this->logger->error('PayPal webhook processing error', [
                'error' => $e->getMessage(),
                'event_data' => $eventData
            ]);

            return false;
        }
    }

    /**
     * Format order items for PayPal
     */
    private function formatOrderItems(Order $order): array
    {
        $items = [];
        
        foreach ($order->getItems() as $item) {
            $items[] = [
                'name' => $item['title'],
                'unit_amount' => [
                    'currency_code' => 'USD',
                    'value' => number_format($item['price'], 2, '.', '')
                ],
                'quantity' => (string)$item['quantity'],
                'category' => 'DIGITAL_GOODS'
            ];
        }

        return $items;
    }

    /**
     * Handle order approved webhook
     */
    private function handleOrderApproved(array $eventData): bool
    {
        // TODO: Implement order approved handling
        $this->logger->info('PayPal order approved', $eventData);
        return true;
    }

    /**
     * Handle payment completed webhook
     */
    private function handlePaymentCompleted(array $eventData): bool
    {
        // TODO: Implement payment completed handling
        $this->logger->info('PayPal payment completed', $eventData);
        return true;
    }

    /**
     * Handle payment denied webhook
     */
    private function handlePaymentDenied(array $eventData): bool
    {
        // TODO: Implement payment denied handling
        $this->logger->info('PayPal payment denied', $eventData);
        return true;
    }

    /**
     * Check if PayPal service is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->clientId) && !empty($this->clientSecret);
    }

    /**
     * Test PayPal API connection
     */
    public function testConnection(): bool
    {
        return $this->getAccessToken() !== null;
    }
}
