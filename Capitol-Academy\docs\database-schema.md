# Capitol Academy Database Schema Documentation

## Overview

This document provides a comprehensive overview of the Capitol Academy database schema, including all tables, fields, relationships, data types, and constraints. The database is designed to support a professional trading education platform with course management, user enrollment, reviews, and administrative features.

## Database Information

- **Database Engine**: MySQL 8.0+
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_unicode_ci
- **Framework**: Symfony 6.4 with Doctrine ORM

## Core Tables

### 1. User Table (`user`)

The central user management table storing all user accounts including students and administrators.

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique user identifier |
| `email` | VARCHAR(180) | UNIQUE, NOT NULL | User email address (used for login) |
| `roles` | JSON | NOT NULL | User roles array (ROLE_USER, ROLE_ADMIN, etc.) |
| `password` | VARCHAR(255) | NOT NULL | Hashed password |
| `first_name` | VARCHAR(100) | NOT NULL | User's first name |
| `last_name` | VARCHAR(100) | NOT NULL | User's last name |
| `phone` | VARCHAR(20) | NULLABLE | User's phone number |
| `country` | VARCHAR(100) | NULLABLE | User's country |
| `profile_picture` | VARCHAR(255) | NULLABLE | Path to profile picture |
| `is_verified` | BOOLEAN | DEFAULT FALSE | Email verification status |
| `is_blocked` | BOOLEAN | DEFAULT FALSE | Account blocking status |
| `ip_address` | VARCHAR(45) | NULLABLE | Last known IP address |
| `created_at` | DATETIME | NOT NULL | Account creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

**Indexes:**
- PRIMARY KEY (`id`)
- UNIQUE KEY (`email`)

### 2. Course Table (`course`)

Main course information table storing all trading courses offered by Capitol Academy.

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique course identifier |
| `code` | VARCHAR(10) | UNIQUE, NOT NULL | Course code (e.g., FMA, TEC, TRS) |
| `title` | VARCHAR(255) | NOT NULL | Course title |
| `description` | LONGTEXT | NULLABLE | Detailed course description |
| `category` | VARCHAR(100) | NULLABLE | Course category |
| `level` | VARCHAR(50) | NULLABLE | Difficulty level (Beginner, Intermediate, Advanced) |
| `duration` | INT | NULLABLE | Course duration in minutes |
| `price` | DECIMAL(10,2) | NULLABLE | Course price in USD |
| `thumbnail_image` | VARCHAR(255) | NULLABLE | Path to thumbnail image (300x200px) |
| `banner_image` | VARCHAR(255) | NULLABLE | Path to banner image (1200x400px) |
| `learning_outcomes` | JSON | NULLABLE | Array of learning outcomes |
| `features` | JSON | NULLABLE | Array of course features |
| `has_modules` | BOOLEAN | DEFAULT FALSE | Whether course uses modular structure |
| `is_active` | BOOLEAN | DEFAULT TRUE | Course availability status |
| `enrolled_count` | INT | DEFAULT 0 | Total number of enrolled students |
| `active_enrollments` | INT | DEFAULT 0 | Currently active enrollments |
| `completed_count` | INT | DEFAULT 0 | Number of students who completed |
| `certified_count` | INT | DEFAULT 0 | Number of certified students |
| `average_rating` | DECIMAL(3,2) | DEFAULT 0.00 | Average course rating (0.00-5.00) |
| `total_reviews` | INT | DEFAULT 0 | Total number of reviews |
| `view_count` | INT | DEFAULT 0 | Number of course page views |
| `created_at` | DATETIME | NOT NULL | Course creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

**Indexes:**
- PRIMARY KEY (`id`)
- UNIQUE KEY (`code`)
- INDEX (`category`)
- INDEX (`is_active`)

### 3. Course Module Table (`course_module`)

Stores individual modules for courses that use modular structure.

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique module identifier |
| `course_id` | INT | FOREIGN KEY, NOT NULL | Reference to parent course |
| `title` | VARCHAR(255) | NOT NULL | Module title |
| `description` | LONGTEXT | NULLABLE | Module description |
| `duration` | INT | NULLABLE | Module duration in minutes |
| `display_order` | INT | NOT NULL | Module order within course |
| `is_active` | BOOLEAN | DEFAULT TRUE | Module availability status |
| `created_at` | DATETIME | NOT NULL | Module creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

**Indexes:**
- PRIMARY KEY (`id`)
- FOREIGN KEY (`course_id`) REFERENCES `course(id)` ON DELETE CASCADE
- INDEX (`course_id`, `display_order`)

### 4. Course Review Table (`course_review`)

Stores student reviews and ratings for courses.

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique review identifier |
| `course_id` | INT | FOREIGN KEY, NOT NULL | Reference to reviewed course |
| `user_id` | INT | FOREIGN KEY, NOT NULL | Reference to reviewing user |
| `rating` | SMALLINT | NOT NULL | Rating (1-5 stars) |
| `comment` | LONGTEXT | NULLABLE | Review comment (max 1000 chars) |
| `is_certified` | BOOLEAN | DEFAULT FALSE | Whether reviewer is certified |
| `is_approved` | BOOLEAN | DEFAULT FALSE | Admin approval status |
| `is_featured` | BOOLEAN | DEFAULT FALSE | Featured review status |
| `created_at` | DATETIME | NOT NULL | Review creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last update timestamp |

**Indexes:**
- PRIMARY KEY (`id`)
- FOREIGN KEY (`course_id`) REFERENCES `course(id)` ON DELETE CASCADE
- FOREIGN KEY (`user_id`) REFERENCES `user(id)` ON DELETE CASCADE
- INDEX (`course_id`, `is_approved`)
- INDEX (`is_featured`)

### 5. Contact Table (`contact`)

Stores contact form submissions from website visitors.

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `id` | INT | PRIMARY KEY, AUTO_INCREMENT | Unique contact identifier |
| `full_name` | VARCHAR(255) | NOT NULL | Contact's full name |
| `email` | VARCHAR(255) | NOT NULL | Contact's email address |
| `phone` | VARCHAR(20) | NULLABLE | Contact's phone number |
| `country` | VARCHAR(100) | NULLABLE | Contact's country |
| `message` | LONGTEXT | NOT NULL | Contact message |
| `source_page` | VARCHAR(255) | NULLABLE | Page where form was submitted |
| `ip_address` | VARCHAR(45) | NULLABLE | Submitter's IP address |
| `is_read` | BOOLEAN | DEFAULT FALSE | Admin read status |
| `created_at` | DATETIME | NOT NULL | Submission timestamp |

**Indexes:**
- PRIMARY KEY (`id`)
- INDEX (`is_read`)
- INDEX (`created_at`)

## Relationships

### One-to-Many Relationships

1. **Course → Course Modules**
   - One course can have multiple modules
   - Foreign key: `course_module.course_id` → `course.id`
   - Cascade delete: When course is deleted, all modules are deleted

2. **Course → Course Reviews**
   - One course can have multiple reviews
   - Foreign key: `course_review.course_id` → `course.id`
   - Cascade delete: When course is deleted, all reviews are deleted

3. **User → Course Reviews**
   - One user can write multiple reviews (for different courses)
   - Foreign key: `course_review.user_id` → `user.id`
   - Cascade delete: When user is deleted, all their reviews are deleted

### Business Rules

1. **Course Codes**: Must be unique, typically 3-4 character codes (FMA, TEC, TRS, etc.)
2. **Course Ratings**: Must be between 1 and 5 stars
3. **Review Approval**: Only approved reviews are displayed publicly
4. **Certified Reviews**: Only certified students can leave reviews
5. **Module Ordering**: Modules are ordered by `display_order` field
6. **Active Status**: Inactive courses/modules are hidden from public view

## Data Types and Constraints

### String Fields
- **VARCHAR(255)**: Standard text fields (titles, names, emails)
- **VARCHAR(100)**: Medium text fields (categories, countries)
- **VARCHAR(50)**: Short text fields (levels, codes)
- **VARCHAR(20)**: Very short fields (phone numbers)
- **LONGTEXT**: Large text content (descriptions, messages, comments)

### Numeric Fields
- **INT**: Primary keys, counters, durations
- **SMALLINT**: Ratings (1-5)
- **DECIMAL(10,2)**: Prices (up to $99,999,999.99)
- **DECIMAL(3,2)**: Ratings (0.00-5.00)

### Boolean Fields
- **BOOLEAN**: Status flags (is_active, is_approved, etc.)
- Default values provided for all boolean fields

### Date/Time Fields
- **DATETIME**: All timestamps use DATETIME type
- **NOT NULL**: All timestamps are required
- Automatic timezone handling through Symfony

### JSON Fields
- **JSON**: Arrays of learning outcomes and features
- Stored as JSON for flexible querying and updates

## Performance Considerations

### Indexes
- Primary keys on all tables for fast lookups
- Foreign key indexes for efficient joins
- Composite indexes on frequently queried combinations
- Unique indexes on business keys (email, course code)

### Query Optimization
- Use of appropriate data types to minimize storage
- Proper indexing strategy for common query patterns
- Cascade deletes to maintain referential integrity
- Boolean flags for efficient filtering

## Security Considerations

### Data Protection
- Passwords are hashed using Symfony's security component
- Email addresses are unique and validated
- IP addresses stored for security tracking
- User roles managed through JSON arrays

### Access Control
- Role-based permissions (ROLE_USER, ROLE_ADMIN)
- Admin approval required for public reviews
- User blocking capability for security
- Email verification system

## Migration History

### Recent Changes (Version **************)
- **Removed Fields**: `slug` and `content` from `course` and `course_module` tables
- **Added Fields**: Enrollment statistics and rating fields to `course` table
- **New Table**: `course_review` for student feedback system
- **Enhanced Features**: View counting, rating system, review management

### Field Removals
- **course.slug**: Replaced with code-based routing
- **course.content**: Simplified content management
- **course_module.slug**: Streamlined module identification
- **course_module.content**: Focused on description-based modules

### Field Additions
- **course.enrolled_count**: Track total enrollments
- **course.active_enrollments**: Track current students
- **course.completed_count**: Track completion rates
- **course.certified_count**: Track certification success
- **course.average_rating**: Aggregate rating calculation
- **course.total_reviews**: Review count tracking
- **course.view_count**: Page view analytics

This schema supports Capitol Academy's mission to provide professional trading education with comprehensive course management, student tracking, and feedback systems while maintaining data integrity and performance.
