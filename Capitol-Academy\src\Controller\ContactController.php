<?php

namespace App\Controller;

use App\Entity\Contact;
use App\Entity\Message;
use App\Form\ContactType;
use App\Form\MessageType;
use App\Service\IpAddressService;
use App\Service\UnifiedContactService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Attribute\Route;

class ContactController extends AbstractController
{
    public function __construct(
        private IpAddressService $ipAddressService,
        private UnifiedContactService $unifiedContactService
    ) {}
    #[Route('/contact', name: 'app_contact')]
    public function index(Request $request, EntityManagerInterface $entityManager, MailerInterface $mailer): Response
    {
        $contact = new Contact();
        
        // Pre-fill subject if provided in query parameters
        $subject = $request->query->get('subject');
        if ($subject) {
            $contact->setSubject($subject);
        } else {
            $contact->setSubject('General Inquiry - Capitol Academy');
        }

        $form = $this->createForm(ContactType::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $contact->setIpAddress($ipAddress);

            // Save to database
            $entityManager->persist($contact);
            $entityManager->flush();

            // Send email notification (optional)
            try {
                $email = (new Email())
                    ->from('<EMAIL>')
                    ->to('<EMAIL>')
                    ->subject('New Contact Form Submission: ' . ($contact->getSubject() ?: 'General Inquiry'))
                    ->html($this->renderView('emails/contact_notification.html.twig', [
                        'contact' => $contact
                    ]));

                $mailer->send($email);
            } catch (\Exception $e) {
                // Log error but don't fail the form submission
                // In production, you might want to log this properly
            }

            return $this->redirectToRoute('app_contact');
        }

        return $this->render('contact/index.html.twig', [
            'form' => $form,
            'subject' => $subject,
        ]);
    }

    #[Route('/contact/registration', name: 'app_contact_registration')]
    public function registration(Request $request, EntityManagerInterface $entityManager): Response
    {
        $contact = new Contact();
        $contact->setSubject('Student Registration Application - Capitol Academy');

        $form = $this->createForm(ContactType::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $contact->setIpAddress($ipAddress);

            $entityManager->persist($contact);
            $entityManager->flush();

            return $this->redirectToRoute('app_contact_registration');
        }

        return $this->render('contact/registration.html.twig', [
            'form' => $form,
        ]);
    }

    #[Route('/contact/instructor', name: 'app_contact_instructor')]
    public function instructor(Request $request, EntityManagerInterface $entityManager): Response
    {
        $contact = new Contact();
        $contact->setSubject('Instructor Application - Capitol Academy');

        $form = $this->createForm(ContactType::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $contact->setIpAddress($ipAddress);

            $entityManager->persist($contact);
            $entityManager->flush();

            return $this->redirectToRoute('app_contact_instructor');
        }

        return $this->render('contact/instructor.html.twig', [
            'form' => $form,
        ]);
    }

    #[Route('/contact/webinar', name: 'app_contact_webinar')]
    public function webinar(Request $request, EntityManagerInterface $entityManager): Response
    {
        $contact = new Contact();
        $contact->setSubject('Live Trading Webinar Inquiry - Capitol Academy');

        $form = $this->createForm(ContactType::class, $contact);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $contact->setIpAddress($ipAddress);

            $entityManager->persist($contact);
            $entityManager->flush();

            $this->addFlash('success', 'Thank you for your interest in our live trading webinars! We will contact you soon with more information.');
            return $this->redirectToRoute('app_contact_webinar');
        }

        return $this->render('contact/webinar.html.twig', [
            'form' => $form,
        ]);
    }

    #[Route('/message', name: 'app_message')]
    public function message(Request $request, EntityManagerInterface $entityManager, MailerInterface $mailer): Response
    {
        $message = new Message();
        $form = $this->createForm(MessageType::class, $message);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Capture real IP address using secure service
            $ipAddress = $this->ipAddressService->getClientIpAddress($request);
            $message->setIpAddress($ipAddress);

            // Save to database
            $entityManager->persist($message);
            $entityManager->flush();

            // Send email notification (optional)
            try {
                $email = (new Email())
                    ->from('<EMAIL>')
                    ->to('<EMAIL>')
                    ->subject('New Message from Capitol Academy Website')
                    ->html($this->renderView('emails/message_notification.html.twig', [
                        'message' => $message
                    ]));

                $mailer->send($email);
            } catch (\Exception $e) {
                // Log error but don't fail the form submission
                // In production, you might want to log this properly
            }

            return $this->redirectToRoute('app_about');
        }

        return $this->redirectToRoute('app_about');
    }

    #[Route('/contact/unified', name: 'app_contact_unified', methods: ['POST'])]
    public function unifiedContact(Request $request): Response
    {
        $formData = $request->request->all();
        $sourcePage = $request->request->get('source_page', 'contact');

        // Validate form data
        $errors = $this->unifiedContactService->validateFormData($formData);

        if (!empty($errors)) {
            foreach ($errors as $error) {
                $this->addFlash('error', $error);
            }
            return $this->getRedirectResponse($sourcePage);
        }

        try {
            // Process the contact form
            $this->unifiedContactService->processContactForm($formData, $sourcePage, $request);
        } catch (\Exception $e) {
            $this->addFlash('error', 'There was an error processing your request. Please try again.');
        }

        // Redirect back to the source page
        return $this->getRedirectResponse($sourcePage);
    }

    /**
     * Get redirect response based on source page
     */
    private function getRedirectResponse(string $sourcePage): Response
    {
        return match ($sourcePage) {
            'contact' => $this->redirectToRoute('app_contact'),
            'about' => $this->redirectToRoute('app_about'),
            'partnership' => $this->redirectToRoute('app_partnership'),
            'instructor' => $this->redirectToRoute('app_instructors'),
            default => $this->redirectToRoute('app_contact')
        };
    }
}
