<?php

namespace App\Controller;

use App\Repository\CourseRepository;
use App\Repository\InstructorRepository;
use App\Repository\VideoRepository;
use App\Repository\PartnerRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class HomeController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    public function index(
        CourseRepository $courseRepository,
        VideoRepository $videoRepository,
        PartnerRepository $partnerRepository
    ): Response
    {
        // Get data for homepage sections
        $popularCourses = $courseRepository->findPopularCourses(4); // 4 courses for section 3
        $partners = $partnerRepository->findActivePartnersOrdered(); // Partners for section 2
        $latestVideo = $videoRepository->findPopularVideos(1)[0] ?? null; // Latest video for section 4
        $recentVideos = $videoRepository->findPopularVideos(5); // Get 5 videos, skip first one for past streams

        // Remove the latest video from recent videos to avoid duplication
        if ($latestVideo && count($recentVideos) > 1) {
            $pastVideos = array_slice($recentVideos, 1, 4); // Get next 4 videos
        } else {
            $pastVideos = array_slice($recentVideos, 0, 4); // Get first 4 if no latest video
        }

        return $this->render('home/index.html.twig', [
            'popular_courses' => $popularCourses,
            'partners' => $partners,
            'latest_video' => $latestVideo,
            'past_videos' => $pastVideos,
        ]);
    }

    #[Route('/about', name: 'app_about')]
    public function about(CourseRepository $courseRepository): Response
    {
        $popularCourses = $courseRepository->findPopularCourses(6);
        
        return $this->render('home/about.html.twig', [
            'popular_courses' => $popularCourses,
        ]);
    }

    #[Route('/partnership', name: 'app_partnership')]
    public function partnership(): Response
    {
        return $this->render('home/partnership.html.twig');
    }

    #[Route('/diplomas', name: 'app_diplomas')]
    public function diplomas(): Response
    {
        return $this->render('home/diplomas.html.twig');
    }

    #[Route('/instructors', name: 'app_instructors')]
    public function instructors(InstructorRepository $instructorRepository): Response
    {
        $instructors = $instructorRepository->findActiveInstructors();

        return $this->render('home/instructors.html.twig', [
            'instructors' => $instructors,
        ]);
    }

    #[Route('/executive-program', name: 'app_executive_program')]
    public function executiveProgram(): Response
    {
        return $this->render('home/executive_program.html.twig');
    }
}
