<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Capitol Academy - Promotional Banner System Migration
 * Creates promotional_banner table for admin-managed site-wide promotional banners
 */
final class Version20250619000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create promotional_banner table for Capitol Academy promotional banner system';
    }

    public function up(Schema $schema): void
    {
        // Create promotional_banner table
        $this->addSql('CREATE TABLE promotional_banner (
            id INT AUTO_INCREMENT NOT NULL, 
            title VARCHAR(255) NOT NULL, 
            description LONGTEXT DEFAULT NULL, 
            end_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            cta_text VARCHAR(100) DEFAULT NULL, 
            cta_link VARCHAR(500) DEFAULT NULL, 
            is_active TINYINT(1) NOT NULL DEFAULT 1, 
            background_color VARCHAR(7) DEFAULT \'#001427\', 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create indexes for better performance
        $this->addSql('CREATE INDEX IDX_promotional_banner_active ON promotional_banner (is_active)');
        $this->addSql('CREATE INDEX IDX_promotional_banner_end_date ON promotional_banner (end_date)');
        $this->addSql('CREATE INDEX IDX_promotional_banner_created_at ON promotional_banner (created_at)');

        // Insert default promotional banner
        $this->addSql("INSERT INTO promotional_banner (
            title, 
            description, 
            end_date, 
            cta_text, 
            cta_link, 
            is_active, 
            background_color, 
            created_at, 
            updated_at
        ) VALUES (
            'Cyber Friday Sale—Save up to 25%', 
            'Join our Analysis Group Today & Save Over \$150', 
            DATE_ADD(NOW(), INTERVAL 7 DAY), 
            'GET YOUR SALE NOW!', 
            '/register', 
            1, 
            '#001427', 
            NOW(), 
            NOW()
        )");
    }

    public function down(Schema $schema): void
    {
        // Drop indexes first
        $this->addSql('DROP INDEX IDX_promotional_banner_active ON promotional_banner');
        $this->addSql('DROP INDEX IDX_promotional_banner_end_date ON promotional_banner');
        $this->addSql('DROP INDEX IDX_promotional_banner_created_at ON promotional_banner');
        
        // Drop table
        $this->addSql('DROP TABLE promotional_banner');
    }
}
