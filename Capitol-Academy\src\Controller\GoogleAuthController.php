<?php

namespace App\Controller;

use App\Entity\User;
use App\Service\IpAddressService;
use Doctrine\ORM\EntityManagerInterface;
use KnpU\OAuth2ClientBundle\Client\ClientRegistry;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;


class GoogleAuthController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private IpAddressService $ipAddressService
    ) {}

    #[Route('/connect/google', name: 'connect_google_start')]
    public function connectAction(ClientRegistry $clientRegistry): Response
    {
        // If user is already logged in, redirect to home
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        // Check if Google OAuth is properly configured
        $clientId = $_ENV['GOOGLE_OAUTH_CLIENT_ID'] ?? '';
        $clientSecret = $_ENV['GOOGLE_OAUTH_CLIENT_SECRET'] ?? '';

        if (empty($clientId) || empty($clientSecret) ||
            $clientId === 'your_google_oauth_client_id_here' ||
            $clientSecret === 'your_google_oauth_client_secret_here') {

            $this->addFlash('error', 'Google OAuth is not properly configured. Please contact the administrator.');
            return $this->redirectToRoute('app_login');
        }

        try {
            // Redirect to Google OAuth
            return $clientRegistry
                ->getClient('google')
                ->redirect([
                    'email', 'profile'
                ], []);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Google OAuth service is currently unavailable. Please try logging in with your email and password.');
            return $this->redirectToRoute('app_login');
        }
    }

    #[Route('/connect/google/check', name: 'connect_google_check')]
    public function connectCheckAction(Request $request, ClientRegistry $clientRegistry): Response
    {
        // If user is already logged in, redirect to home
        if ($this->getUser()) {
            return $this->redirectToRoute('app_home');
        }

        try {
            $client = $clientRegistry->getClient('google');
            $accessToken = $client->getAccessToken();

            // Get user info from Google
            $googleUser = $client->fetchUserFromToken($accessToken);

            $email = $googleUser->getEmail();
            $firstName = $googleUser->getFirstName();
            $lastName = $googleUser->getLastName();

            if (!$email) {
                $this->addFlash('error', 'Unable to get email from Google. Please try again.');
                return $this->redirectToRoute('app_login');
            }

            // Check if user already exists
            $existingUser = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

            if ($existingUser) {
                // User exists, log them in
                // Note: This would typically be handled by a custom authenticator
                // For now, we'll redirect to login with a message
                $this->addFlash('success', 'Account found! Please log in with your email and password.');
                return $this->redirectToRoute('app_login');
            } else {
                // User doesn't exist, create new account
                $user = new User();
                $user->setEmail($email);
                $user->setFirstName($firstName ?: 'Google');
                $user->setLastName($lastName ?: 'User');
                
                // Set a random password (user can reset it later if needed)
                $randomPassword = bin2hex(random_bytes(16));
                $user->setPassword(password_hash($randomPassword, PASSWORD_DEFAULT));
                
                // Capture IP address
                $ipAddress = $this->ipAddressService->getClientIpAddress($request);
                $user->setIpAddress($ipAddress);
                
                $user->setIsVerified(true); // Auto-verify Google users
                $user->setUpdatedAt(new \DateTimeImmutable());

                $this->entityManager->persist($user);
                $this->entityManager->flush();

                $this->addFlash('success', 'Account created successfully! Please log in with your email and the password reset option if needed.');
                return $this->redirectToRoute('app_login');
            }

        } catch (IdentityProviderException $e) {
            // Handle specific OAuth errors
            $errorMessage = $e->getMessage();
            if (strpos($errorMessage, 'invalid_client') !== false) {
                $this->addFlash('error', 'Google OAuth is not properly configured. Please contact the administrator.');
            } else {
                $this->addFlash('error', 'Google authentication failed. Please try logging in with your email and password.');
            }
            return $this->redirectToRoute('app_login');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Google OAuth service is currently unavailable. Please try logging in with your email and password.');
            return $this->redirectToRoute('app_login');
        }
    }
}
