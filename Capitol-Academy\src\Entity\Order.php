<?php

namespace App\Entity;

use App\Repository\OrderRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: OrderRepository::class)]
#[ORM\Table(name: 'orders')]
class Order
{
    public const STATUS_PENDING = 'pending';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';
    public const STATUS_REFUNDED = 'refunded';
    public const STATUS_CANCELLED = 'cancelled';

    public const GATEWAY_PAYPAL = 'paypal';
    public const GATEWAY_STRIPE = 'stripe';
    public const GATEWAY_MANUAL = 'manual';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\Column(type: 'json')]
    #[Assert\NotBlank(message: 'Order items are required')]
    private array $items = [];

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Assert\NotBlank(message: 'Total price is required')]
    #[Assert\PositiveOrZero(message: 'Total price must be a positive number')]
    private ?string $totalPrice = null;

    #[ORM\Column(length: 20)]
    #[Assert\Choice(choices: [self::STATUS_PENDING, self::STATUS_COMPLETED, self::STATUS_FAILED, self::STATUS_REFUNDED, self::STATUS_CANCELLED])]
    private string $paymentStatus = self::STATUS_PENDING;

    #[ORM\Column(length: 50)]
    #[Assert\Choice(choices: [self::GATEWAY_PAYPAL, self::GATEWAY_STRIPE, self::GATEWAY_MANUAL])]
    private ?string $paymentGateway = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $paypalTransactionId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $stripePaymentIntentId = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\Column(length: 10, unique: true)]
    #[Assert\NotBlank(message: 'Order number is required')]
    private ?string $orderNumber = null;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $paymentDetails = [];

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $completedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
        $this->items = [];
        $this->paymentDetails = [];
        $this->generateOrderNumber();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setItems(array $items): static
    {
        $this->items = $items;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function addItem(string $type, int $id, string $title, float $price, int $quantity = 1): static
    {
        $this->items[] = [
            'type' => $type, // 'video', 'course', 'video_plan'
            'id' => $id,
            'title' => $title,
            'price' => $price,
            'quantity' => $quantity,
            'subtotal' => $price * $quantity
        ];
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getTotalPrice(): ?string
    {
        return $this->totalPrice;
    }

    public function setTotalPrice(string $totalPrice): static
    {
        $this->totalPrice = $totalPrice;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getPaymentStatus(): string
    {
        return $this->paymentStatus;
    }

    public function setPaymentStatus(string $paymentStatus): static
    {
        $this->paymentStatus = $paymentStatus;
        $this->updatedAt = new \DateTimeImmutable();
        
        if ($paymentStatus === self::STATUS_COMPLETED && !$this->completedAt) {
            $this->completedAt = new \DateTimeImmutable();
        }
        
        return $this;
    }

    public function getPaymentGateway(): ?string
    {
        return $this->paymentGateway;
    }

    public function setPaymentGateway(string $paymentGateway): static
    {
        $this->paymentGateway = $paymentGateway;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getPaypalTransactionId(): ?string
    {
        return $this->paypalTransactionId;
    }

    public function setPaypalTransactionId(?string $paypalTransactionId): static
    {
        $this->paypalTransactionId = $paypalTransactionId;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getStripePaymentIntentId(): ?string
    {
        return $this->stripePaymentIntentId;
    }

    public function setStripePaymentIntentId(?string $stripePaymentIntentId): static
    {
        $this->stripePaymentIntentId = $stripePaymentIntentId;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    public function getOrderNumber(): ?string
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(string $orderNumber): static
    {
        $this->orderNumber = $orderNumber;
        return $this;
    }

    public function getPaymentDetails(): ?array
    {
        return $this->paymentDetails;
    }

    public function setPaymentDetails(?array $paymentDetails): static
    {
        $this->paymentDetails = $paymentDetails ?? [];
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        $this->updatedAt = new \DateTimeImmutable();
        return $this;
    }

    public function getCompletedAt(): ?\DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(?\DateTimeImmutable $completedAt): static
    {
        $this->completedAt = $completedAt;
        return $this;
    }

    // Helper methods

    public function getFormattedTotalPrice(): string
    {
        return '$' . number_format((float)$this->totalPrice, 2);
    }

    public function getStatusBadgeClass(): string
    {
        return match($this->paymentStatus) {
            self::STATUS_COMPLETED => 'badge-success',
            self::STATUS_PENDING => 'badge-warning',
            self::STATUS_FAILED => 'badge-danger',
            self::STATUS_REFUNDED => 'badge-info',
            self::STATUS_CANCELLED => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    public function getStatusLabel(): string
    {
        return match($this->paymentStatus) {
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_PENDING => 'Pending',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_REFUNDED => 'Refunded',
            self::STATUS_CANCELLED => 'Cancelled',
            default => 'Unknown'
        };
    }

    public function isCompleted(): bool
    {
        return $this->paymentStatus === self::STATUS_COMPLETED;
    }

    public function isPending(): bool
    {
        return $this->paymentStatus === self::STATUS_PENDING;
    }

    public function isFailed(): bool
    {
        return $this->paymentStatus === self::STATUS_FAILED;
    }

    public function getItemCount(): int
    {
        return array_sum(array_column($this->items, 'quantity'));
    }

    public function calculateTotal(): float
    {
        return array_sum(array_column($this->items, 'subtotal'));
    }

    private function generateOrderNumber(): void
    {
        $this->orderNumber = 'CA' . date('Ymd') . strtoupper(substr(uniqid(), -4));
    }
}
