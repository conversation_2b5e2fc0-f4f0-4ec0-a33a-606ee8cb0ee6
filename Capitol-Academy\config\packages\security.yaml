security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        App\Entity\User:
            algorithm: auto
        App\Entity\Admin:
            algorithm: auto

    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
        app_admin_provider:
            entity:
                class: App\Entity\Admin
                property: username
        app_unified_provider:
            chain:
                providers: ['app_user_provider', 'app_admin_provider']

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        admin:
            pattern: ^/admin
            lazy: true
            provider: app_admin_provider
            form_login:
                login_path: admin_login
                check_path: admin_login_check
                default_target_path: admin_dashboard
                always_use_default_target_path: true
                username_parameter: _username
                password_parameter: _password
                csrf_parameter: _token
                csrf_token_id: authenticate
                enable_csrf: true
            logout:
                path: admin_logout
                target: admin_login
                invalidate_session: true
                delete_cookies:
                    PHPSESSID: { path: null, domain: null }
        main:
            lazy: true
            provider: app_unified_provider
            user_checker: App\Security\UserChecker
            form_login:
                login_path: app_login
                check_path: app_login
                success_handler: App\Security\LoginSuccessHandler
                username_parameter: _username
                password_parameter: _password
                csrf_parameter: _token
                csrf_token_id: authenticate
                enable_csrf: true
            logout:
                path: app_logout
                target: app_home
                invalidate_session: true
                delete_cookies:
                    PHPSESSID: { path: null, domain: null }
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800 # 1 week
                path: /
                always_remember_me: false
                remember_me_parameter: _remember_me

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/admin/create-admin, roles: PUBLIC_ACCESS }
        - { path: ^/admin/login, roles: PUBLIC_ACCESS }
        - { path: ^/admin/login_check, roles: PUBLIC_ACCESS }
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/forgot-password, roles: PUBLIC_ACCESS }
        - { path: ^/reset-password, roles: PUBLIC_ACCESS }
        - { path: ^/verify-reset-code, roles: PUBLIC_ACCESS }
        - { path: ^/connect/google, roles: PUBLIC_ACCESS }
        # - { path: ^/register, roles: PUBLIC_ACCESS } # Registration temporarily disabled
        - { path: ^/user, roles: ROLE_USER }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
