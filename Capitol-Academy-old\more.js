//MooTools More, <http://mootools.net/more>. Copyright (c) 2006-2008 <PERSON><PERSON>, <http://mad4milk.net>, MIT Style License.
/*
Script: Fx.Slide.js
Effect to slide an element in and out of view.
License:
MIT-style license.
*/
Fx.Slide = new Class({
Extends: Fx,
options: {
mode: 'vertical'
},
initialize: function(element, options){
this.addEvent('complete', function(){
this.open = (this.wrapper['offset' + this.layout.capitalize()] != 0);
if (this.open && Browser.Engine.webkit419) this.element.dispose().inject(this.wrapper);
}, true);
this.element = this.subject = $(element);
this.parent(options);
var wrapper = this.element.retrieve('wrapper');
this.wrapper = wrapper || new Element('div', {
styles: $extend(this.element.getStyles('margin', 'position'), {'overflow': 'hidden'})
}).wraps(this.element);
this.element.store('wrapper', this.wrapper).setStyle('margin', 0);
this.now = [];
this.open = true;
},
vertical: function(){
this.margin = 'margin-top';
this.layout = 'height';
this.offset = this.element.offsetHeight;
},
horizontal: function(){
this.margin = 'margin-left';
this.layout = 'width';
this.offset = this.element.offsetWidth;
},
set: function(now){
this.element.setStyle(this.margin, now[0]);
this.wrapper.setStyle(this.layout, now[1]);
return this;
},
compute: function(from, to, delta){
var now = [];
var x = 2;
x.times(function(i){
now[i] = Fx.compute(from[i], to[i], delta);
});
return now;
},
start: function(how, mode){
if (!this.check(arguments.callee, how, mode)) return this;
this[mode || this.options.mode]();
var margin = this.element.getStyle(this.margin).toInt();
var layout = this.wrapper.getStyle(this.layout).toInt();
var caseIn = [[margin, layout], [0, this.offset]];
var caseOut = [[margin, layout], [-this.offset, 0]];
var start;
switch (how){
case 'in': start = caseIn; break;
case 'out': start = caseOut; break;
case 'toggle': start = (this.wrapper['offset' + this.layout.capitalize()] == 0) ? caseIn : caseOut;
}
return this.parent(start[0], start[1]);
},
slideIn: function(mode){
return this.start('in', mode);
},
slideOut: function(mode){
return this.start('out', mode);
},
hide: function(mode){
this[mode || this.options.mode]();
this.open = false;
return this.set([-this.offset, 0]);
},
show: function(mode){
this[mode || this.options.mode]();
this.open = true;
return this.set([0, this.offset]);
},
toggle: function(mode){
return this.start('toggle', mode);
}
});
Element.Properties.slide = {
set: function(options){
var slide = this.retrieve('slide');
if (slide) slide.cancel();
return this.eliminate('slide').store('slide:options', $extend({link: 'cancel'}, options));
},
get: function(options){
if (options || !this.retrieve('slide')){
if (options || !this.retrieve('slide:options')) this.set('slide', options);
this.store('slide', new Fx.Slide(this, this.retrieve('slide:options')));
}
return this.retrieve('slide');
}
};
Element.implement({
slide: function(how, mode){
how = how || 'toggle';
var slide = this.get('slide'), toggle;
switch (how){
case 'hide': slide.hide(mode); break;
case 'show': slide.show(mode); break;
case 'toggle':
var flag = this.retrieve('slide:flag', slide.open);
slide[(flag) ? 'slideOut' : 'slideIn'](mode);
this.store('slide:flag', !flag);
toggle = true;
break;
default: slide.start(how, mode);
}
if (!toggle) this.eliminate('slide:flag');
return this;
}
});
/*
Script: Fx.Scroll.js
Effect to smoothly scroll any element, including the window.
License:
MIT-style license.
*/
Fx.Scroll = new Class({
Extends: Fx,
options: {
offset: {'x': 0, 'y': 0},
wheelStops: true
},
initialize: function(element, options){
this.element = this.subject = $(element);
this.parent(options);
var cancel = this.cancel.bind(this, false);
if ($type(this.element) != 'element') this.element = $(this.element.getDocument().body);
var stopper = this.element;
if (this.options.wheelStops){
this.addEvent('start', function(){
stopper.addEvent('mousewheel', cancel);
}, true);
this.addEvent('complete', function(){
stopper.removeEvent('mousewheel', cancel);
}, true);
}
},
set: function(){
var now = Array.flatten(arguments);
this.element.scrollTo(now[0], now[1]);
},
compute: function(from, to, delta){
var now = [];
var x = 2;
x.times(function(i){
now.push(Fx.compute(from[i], to[i], delta));
});
return now;
},
start: function(x, y){
if (!this.check(arguments.callee, x, y)) return this;
var offsetSize = this.element.getSize(), scrollSize = this.element.getScrollSize();
var scroll = this.element.getScroll(), values = {x: x, y: y};
for (var z in values){
var max = scrollSize[z] - offsetSize[z];
if ($chk(values[z])) values[z] = ($type(values[z]) == 'number') ? values[z].limit(0, max) : max;
else values[z] = scroll[z];
values[z] += this.options.offset[z];
}
return this.parent([scroll.x, scroll.y], [values.x, values.y]);
},
toTop: function(){
return this.start(false, 0);
},
toLeft: function(){
return this.start(0, false);
},
toRight: function(){
return this.start('right', false);
},
toBottom: function(){
return this.start(false, 'bottom');
},
toElement: function(el){
var position = $(el).getPosition(this.element);
return this.start(position.x, position.y);
}
});
/*
Script: Fx.Elements.js
Effect to change any number of CSS properties of any number of Elements.
License:
MIT-style license.
*/
Fx.Elements = new Class({
Extends: Fx.CSS,
initialize: function(elements, options){
this.elements = this.subject = $$(elements);
this.parent(options);
},
compute: function(from, to, delta){
var now = {};
for (var i in from){
var iFrom = from[i], iTo = to[i], iNow = now[i] = {};
for (var p in iFrom) iNow[p] = this.parent(iFrom[p], iTo[p], delta);
}
return now;
},
set: function(now){
for (var i in now){
var iNow = now[i];
for (var p in iNow) this.render(this.elements[i], p, iNow[p], this.options.unit);
}
return this;
},
start: function(obj){
if (!this.check(arguments.callee, obj)) return this;
var from = {}, to = {};
for (var i in obj){
var iProps = obj[i], iFrom = from[i] = {}, iTo = to[i] = {};
for (var p in iProps){
var parsed = this.prepare(this.elements[i], p, iProps[p]);
iFrom[p] = parsed.from;
iTo[p] = parsed.to;
}
}
return this.parent(from, to);
}
});
/*
Script: Drag.js
The base Drag Class. Can be used to drag and resize Elements using mouse events.
License:
MIT-style license.
*/
var Drag = new Class({
Implements: [Events, Options],
options: {/*
onBeforeStart: $empty,
onStart: $empty,
onDrag: $empty,
onCancel: $empty,
onComplete: $empty,*/
snap: 6,
unit: 'px',
grid: false,
style: true,
limit: false,
handle: false,
invert: false,
preventDefault: false,
modifiers: {x: 'left', y: 'top'}
},
initialize: function(){
var params = Array.link(arguments, {'options': Object.type, 'element': $defined});
this.element = $(params.element);
this.document = this.element.getDocument();
this.setOptions(params.options || {});
var htype = $type(this.options.handle);
this.handles = (htype == 'array' || htype == 'collection') ? $$(this.options.handle) : $(this.options.handle) || this.element;
this.mouse = {'now': {}, 'pos': {}};
this.value = {'start': {}, 'now': {}};
this.selection = (Browser.Engine.trident) ? 'selectstart' : 'mousedown';
this.bound = {
start: this.start.bind(this),
check: this.check.bind(this),
drag: this.drag.bind(this),
stop: this.stop.bind(this),
cancel: this.cancel.bind(this),
eventStop: $lambda(false)
};
this.attach();
},
attach: function(){
this.handles.addEvent('mousedown', this.bound.start);
return this;
},
detach: function(){
this.handles.removeEvent('mousedown', this.bound.start);
return this;
},
start: function(event){
if (this.options.preventDefault) event.preventDefault();
this.fireEvent('beforeStart', this.element);
this.mouse.start = event.page;
var limit = this.options.limit;
this.limit = {'x': [], 'y': []};
for (var z in this.options.modifiers){
if (!this.options.modifiers[z]) continue;
if (this.options.style) this.value.now[z] = this.element.getStyle(this.options.modifiers[z]).toInt();
else this.value.now[z] = this.element[this.options.modifiers[z]];
if (this.options.invert) this.value.now[z] *= -1;
this.mouse.pos[z] = event.page[z] - this.value.now[z];
if (limit && limit[z]){
for (var i = 2; i--; i){
if ($chk(limit[z][i])) this.limit[z][i] = $lambda(limit[z][i])();
}
}
}
if ($type(this.options.grid) == 'number') this.options.grid = {'x': this.options.grid, 'y': this.options.grid};
this.document.addEvents({mousemove: this.bound.check, mouseup: this.bound.cancel});
this.document.addEvent(this.selection, this.bound.eventStop);
},
check: function(event){
if (this.options.preventDefault) event.preventDefault();
var distance = Math.round(Math.sqrt(Math.pow(event.page.x - this.mouse.start.x, 2) + Math.pow(event.page.y - this.mouse.start.y, 2)));
if (distance > this.options.snap){
this.cancel();
this.document.addEvents({
mousemove: this.bound.drag,
mouseup: this.bound.stop
});
this.fireEvent('start', this.element).fireEvent('snap', this.element);
}
},
drag: function(event){
if (this.options.preventDefault) event.preventDefault();
this.mouse.now = event.page;
for (var z in this.options.modifiers){
if (!this.options.modifiers[z]) continue;
this.value.now[z] = this.mouse.now[z] - this.mouse.pos[z];
if (this.options.invert) this.value.now[z] *= -1;
if (this.options.limit && this.limit[z]){
if ($chk(this.limit[z][1]) && (this.value.now[z] > this.limit[z][1])){
this.value.now[z] = this.limit[z][1];
} else if ($chk(this.limit[z][0]) && (this.value.now[z] < this.limit[z][0])){
this.value.now[z] = this.limit[z][0];
}
}
if (this.options.grid[z]) this.value.now[z] -= (this.value.now[z] % this.options.grid[z]);
if (this.options.style) this.element.setStyle(this.options.modifiers[z], this.value.now[z] + this.options.unit);
else this.element[this.options.modifiers[z]] = this.value.now[z];
}
this.fireEvent('drag', this.element);
},
cancel: function(event){
this.document.removeEvent('mousemove', this.bound.check);
this.document.removeEvent('mouseup', this.bound.cancel);
if (event){
this.document.removeEvent(this.selection, this.bound.eventStop);
this.fireEvent('cancel', this.element);
}
},
stop: function(event){
this.document.removeEvent(this.selection, this.bound.eventStop);
this.document.removeEvent('mousemove', this.bound.drag);
this.document.removeEvent('mouseup', this.bound.stop);
if (event) this.fireEvent('complete', this.element);
}
});
Element.implement({
makeResizable: function(options){
return new Drag(this, $merge({modifiers: {'x': 'width', 'y': 'height'}}, options));
}
});
/*
Script: Drag.Move.js
A Drag extension that provides support for the constraining of draggables to containers and droppables.
License:
MIT-style license.
*/
Drag.Move = new Class({
Extends: Drag,
options: {
droppables: [],
container: false
},
initialize: function(element, options){
this.parent(element, options);
this.droppables = $$(this.options.droppables);
this.container = $(this.options.container);
if (this.container && $type(this.container) != 'element') this.container = $(this.container.getDocument().body);
element = this.element;
var current = element.getStyle('position');
var position = (current != 'static') ? current : 'absolute';
if (element.getStyle('left') == 'auto' || element.getStyle('top') == 'auto') element.position(element.getPosition(element.offsetParent));
element.setStyle('position', position);
this.addEvent('start', function(){
this.checkDroppables();
}, true);
},
start: function(event){
if (this.container){
var el = this.element, cont = this.container, ccoo = cont.getCoordinates(el.offsetParent), cps = {}, ems = {};
['top', 'right', 'bottom', 'left'].each(function(pad){
cps[pad] = cont.getStyle('padding-' + pad).toInt();
ems[pad] = el.getStyle('margin-' + pad).toInt();
}, this);
var width = el.offsetWidth + ems.left + ems.right, height = el.offsetHeight + ems.top + ems.bottom;
var x = [ccoo.left + cps.left, ccoo.right - cps.right - width];
var y = [ccoo.top + cps.top, ccoo.bottom - cps.bottom - height];
this.options.limit = {x: x, y: y};
}
this.parent(event);
},
checkAgainst: function(el){
el = el.getCoordinates();
var now = this.mouse.now;
return (now.x > el.left && now.x < el.right && now.y < el.bottom && now.y > el.top);
},
checkDroppables: function(){
var overed = this.droppables.filter(this.checkAgainst, this).getLast();
if (this.overed != overed){
if (this.overed) this.fireEvent('leave', [this.element, this.overed]);
if (overed){
this.overed = overed;
this.fireEvent('enter', [this.element, overed]);
} else {
this.overed = null;
}
}
},
drag: function(event){
this.parent(event);
if (this.droppables.length) this.checkDroppables();
},
stop: function(event){
this.checkDroppables();
this.fireEvent('drop', [this.element, this.overed]);
this.overed = null;
return this.parent(event);
}
});
Element.implement({
makeDraggable: function(options){
return new Drag.Move(this, options);
}
});
/*
Script: Hash.Cookie.js
Class for creating, reading, and deleting Cookies in JSON format.
License:
MIT-style license.
*/
Hash.Cookie = new Class({
Extends: Cookie,
options: {
autoSave: true
},
initialize: function(name, options){
this.parent(name, options);
this.load();
},
save: function(){
var value = JSON.encode(this.hash);
if (!value || value.length > 4096) return false; //cookie would be truncated!
if (value == '{}') this.dispose();
else this.write(value);
return true;
},
load: function(){
this.hash = new Hash(JSON.decode(this.read(), true));
return this;
}
});
Hash.Cookie.implement((function(){
var methods = {};
Hash.each(Hash.prototype, function(method, name){
methods[name] = function(){
var value = method.apply(this.hash, arguments);
if (this.options.autoSave) this.save();
return value;
};
});
return methods;
})());
/*
Script: Color.js
Class for creating and manipulating colors in JavaScript. Supports HSB -> RGB Conversions and vice versa.
License:
MIT-style license.
*/
var Color = new Native({
initialize: function(color, type){
if (arguments.length >= 3){
type = "rgb"; color = Array.slice(arguments, 0, 3);
} else if (typeof color == 'string'){
if (color.match(/rgb/)) color = color.rgbToHex().hexToRgb(true);
else if (color.match(/hsb/)) color = color.hsbToRgb();
else color = color.hexToRgb(true);
}
type = type || 'rgb';
switch (type){
case 'hsb':
var old = color;
color = color.hsbToRgb();
color.hsb = old;
break;
case 'hex': color = color.hexToRgb(true); break;
}
color.rgb = color.slice(0, 3);
color.hsb = color.hsb || color.rgbToHsb();
color.hex = color.rgbToHex();
return $extend(color, this);
}
});
Color.implement({
mix: function(){
var colors = Array.slice(arguments);
var alpha = ($type(colors.getLast()) == 'number') ? colors.pop() : 50;
var rgb = this.slice();
colors.each(function(color){
color = new Color(color);
for (var i = 0; i < 3; i++) rgb[i] = Math.round((rgb[i] / 100 * (100 - alpha)) + (color[i] / 100 * alpha));
});
return new Color(rgb, 'rgb');
},
invert: function(){
return new Color(this.map(function(value){
return 255 - value;
}));
},
setHue: function(value){
return new Color([value, this.hsb[1], this.hsb[2]], 'hsb');
},
setSaturation: function(percent){
return new Color([this.hsb[0], percent, this.hsb[2]], 'hsb');
},
setBrightness: function(percent){
return new Color([this.hsb[0], this.hsb[1], percent], 'hsb');
}
});
function $RGB(r, g, b){
return new Color([r, g, b], 'rgb');
};
function $HSB(h, s, b){
return new Color([h, s, b], 'hsb');
};
function $HEX(hex){
return new Color(hex, 'hex');
};
Array.implement({
rgbToHsb: function(){
var red = this[0], green = this[1], blue = this[2];
var hue, saturation, brightness;
var max = Math.max(red, green, blue), min = Math.min(red, green, blue);
var delta = max - min;
brightness = max / 255;
saturation = (max != 0) ? delta / max : 0;
if (saturation == 0){
hue = 0;
} else {
var rr = (max - red) / delta;
var gr = (max - green) / delta;
var br = (max - blue) / delta;
if (red == max) hue = br - gr;
else if (green == max) hue = 2 + rr - br;
else hue = 4 + gr - rr;
hue /= 6;
if (hue < 0) hue++;
}
return [Math.round(hue * 360), Math.round(saturation * 100), Math.round(brightness * 100)];
},
hsbToRgb: function(){
var br = Math.round(this[2] / 100 * 255);
if (this[1] == 0){
return [br, br, br];
} else {
var hue = this[0] % 360;
var f = hue % 60;
var p = Math.round((this[2] * (100 - this[1])) / 10000 * 255);
var q = Math.round((this[2] * (6000 - this[1] * f)) / 600000 * 255);
var t = Math.round((this[2] * (6000 - this[1] * (60 - f))) / 600000 * 255);
switch (Math.floor(hue / 60)){
case 0: return [br, t, p];
case 1: return [q, br, p];
case 2: return [p, br, t];
case 3: return [p, q, br];
case 4: return [t, p, br];
case 5: return [br, p, q];
}
}
return false;
}
});
String.implement({
rgbToHsb: function(){
var rgb = this.match(/\d{1,3}/g);
return (rgb) ? hsb.rgbToHsb() : null;
},
hsbToRgb: function(){
var hsb = this.match(/\d{1,3}/g);
return (hsb) ? hsb.hsbToRgb() : null;
}
});
/*
Script: Group.js
Class for monitoring collections of events
License:
MIT-style license.
*/
var Group = new Class({
initialize: function(){
this.instances = Array.flatten(arguments);
this.events = {};
this.checker = {};
},
addEvent: function(type, fn){
this.checker[type] = this.checker[type] || {};
this.events[type] = this.events[type] || [];
if (this.events[type].contains(fn)) return false;
else this.events[type].push(fn);
this.instances.each(function(instance, i){
instance.addEvent(type, this.check.bind(this, [type, instance, i]));
}, this);
return this;
},
check: function(type, instance, i){
this.checker[type][i] = true;
var every = this.instances.every(function(current, j){
return this.checker[type][j] || false;
}, this);
if (!every) return;
this.checker[type] = {};
this.events[type].each(function(event){
event.call(this, this.instances, instance);
}, this);
}
});
/*
Script: Assets.js
Provides methods to dynamically load JavaScript, CSS, and Image files into the document.
License:
MIT-style license.
*/
var Asset = new Hash({
javascript: function(source, properties){
properties = $extend({
onload: $empty,
document: document,
check: $lambda(true)
}, properties);
var script = new Element('script', {'src': source, 'type': 'text/javascript'});
var load = properties.onload.bind(script), check = properties.check, doc = properties.document;
delete properties.onload; delete properties.check; delete properties.document;
script.addEvents({
load: load,
readystatechange: function(){
if (['loaded', 'complete'].contains(this.readyState)) load();
}
}).setProperties(properties);
if (Browser.Engine.webkit419) var checker = (function(){
if (!$try(check)) return;
$clear(checker);
load();
}).periodical(50);
return script.inject(doc.head);
},
css: function(source, properties){
return new Element('link', $merge({
'rel': 'stylesheet', 'media': 'screen', 'type': 'text/css', 'href': source
}, properties)).inject(document.head);
},
image: function(source, properties){
properties = $merge({
'onload': $empty,
'onabort': $empty,
'onerror': $empty
}, properties);
var image = new Image();
var element = $(image) || new Element('img');
['load', 'abort', 'error'].each(function(name){
var type = 'on' + name;
var event = properties[type];
delete properties[type];
image[type] = function(){
if (!image) return;
if (!element.parentNode){
element.width = image.width;
element.height = image.height;
}
image = image.onload = image.onabort = image.onerror = null;
event.delay(1, element, element);
element.fireEvent(name, element, 1);
};
});
image.src = element.src = source;
if (image && image.complete) image.onload.delay(1);
return element.setProperties(properties);
},
images: function(sources, options){
options = $merge({
onComplete: $empty,
onProgress: $empty
}, options);
if (!sources.push) sources = [sources];
var images = [];
var counter = 0;
sources.each(function(source){
var img = new Asset.image(source, {
'onload': function(){
options.onProgress.call(this, counter, sources.indexOf(source));
counter++;
if (counter == sources.length) options.onComplete();
}
});
images.push(img);
});
return new Elements(images);
}
});
/*
Script: Sortables.js
Class for creating a drag and drop sorting interface for lists of items.
License:
MIT-style license.
*/
var Sortables = new Class({
Implements: [Events, Options],
options: {/*
onSort: $empty,
onStart: $empty,
onComplete: $empty,*/
snap: 4,
opacity: 1,
clone: false,
revert: false,
handle: false,
constrain: false
},
initialize: function(lists, options){
this.setOptions(options);
this.elements = [];
this.lists = [];
this.idle = true;
this.addLists($$($(lists) || lists));
if (!this.options.clone) this.options.revert = false;
if (this.options.revert) this.effect = new Fx.Morph(null, $merge({duration: 250, link: 'cancel'}, this.options.revert));
},
attach: function(){
this.addLists(this.lists);
return this;
},
detach: function(){
this.lists = this.removeLists(this.lists);
return this;
},
addItems: function(){
Array.flatten(arguments).each(function(element){
this.elements.push(element);
var start = element.retrieve('sortables:start', this.start.bindWithEvent(this, element));
(this.options.handle ? element.getElement(this.options.handle) || element : element).addEvent('mousedown', start);
}, this);
return this;
},
addLists: function(){
Array.flatten(arguments).each(function(list){
this.lists.push(list);
this.addItems(list.getChildren());
}, this);
return this;
},
removeItems: function(){
var elements = [];
Array.flatten(arguments).each(function(element){
elements.push(element);
this.elements.erase(element);
var start = element.retrieve('sortables:start');
(this.options.handle ? element.getElement(this.options.handle) || element : element).removeEvent('mousedown', start);
}, this);
return $$(elements);
},
removeLists: function(){
var lists = [];
Array.flatten(arguments).each(function(list){
lists.push(list);
this.lists.erase(list);
this.removeItems(list.getChildren());
}, this);
return $$(lists);
},
getClone: function(event, element){
if (!this.options.clone) return new Element('div').inject(document.body);
if ($type(this.options.clone) == 'function') return this.options.clone.call(this, event, element, this.list);
return element.clone(true).setStyles({
'margin': '0px',
'position': 'absolute',
'visibility': 'hidden',
'width': element.getStyle('width')
}).inject(this.list).position(element.getPosition(element.getOffsetParent()));
},
getDroppables: function(){
var droppables = this.list.getChildren();
if (!this.options.constrain) droppables = this.lists.concat(droppables).erase(this.list);
return droppables.erase(this.clone).erase(this.element);
},
insert: function(dragging, element){
var where = 'inside';
if (this.lists.contains(element)){
this.list = element;
this.drag.droppables = this.getDroppables();
} else {
where = this.element.getAllPrevious().contains(element) ? 'before' : 'after';
}
this.element.inject(element, where);
this.fireEvent('sort', [this.element, this.clone]);
},
start: function(event, element){
if (!this.idle) return;
this.idle = false;
this.element = element;
this.opacity = element.get('opacity');
this.list = element.getParent();
this.clone = this.getClone(event, element);
this.drag = new Drag.Move(this.clone, {
snap: this.options.snap,
container: this.options.constrain && this.element.getParent(),
droppables: this.getDroppables(),
onSnap: function(){
event.stop();
this.clone.setStyle('visibility', 'visible');
this.element.set('opacity', this.options.opacity || 0);
this.fireEvent('start', [this.element, this.clone]);
}.bind(this),
onEnter: this.insert.bind(this),
onCancel: this.reset.bind(this),
onComplete: this.end.bind(this)
});
this.clone.inject(this.element, 'before');
this.drag.start(event);
},
end: function(){
this.drag.detach();
this.element.set('opacity', this.opacity);
if (this.effect){
var dim = this.element.getStyles('width', 'height');
var pos = this.clone.computePosition(this.element.getPosition(this.clone.offsetParent));
this.effect.element = this.clone;
this.effect.start({
top: pos.top,
left: pos.left,
width: dim.width,
height: dim.height,
opacity: 0.25
}).chain(this.reset.bind(this));
} else {
this.reset();
}
},
reset: function(){
this.idle = true;
this.clone.destroy();
this.fireEvent('complete', this.element);
},
serialize: function(){
var params = Array.link(arguments, {modifier: Function.type, index: $defined});
var serial = this.lists.map(function(list){
return list.getChildren().map(params.modifier || function(element){
return element.get('id');
}, this);
}, this);
var index = params.index;
if (this.lists.length == 1) index = 0;
return $chk(index) && index >= 0 && index < this.lists.length ? serial[index] : serial;
}
});
/*
Script: Tips.js
Class for creating nice tips that follow the mouse cursor when hovering an element.
License:
MIT-style license.
*/
var Tips = new Class({
Implements: [Events, Options],
options: {
onShow: function(tip){
tip.setStyle('visibility', 'visible');
},
onHide: function(tip){
tip.setStyle('visibility', 'hidden');
},
showDelay: 100,
hideDelay: 100,
className: null,
offsets: {x: 16, y: 16},
fixed: false
},
initialize: function(){
var params = Array.link(arguments, {options: Object.type, elements: $defined});
this.setOptions(params.options || null);
this.tip = new Element('div').inject(document.body);
if (this.options.className) this.tip.addClass(this.options.className);
var top = new Element('div', {'class': 'tip-top'}).inject(this.tip);
this.container = new Element('div', {'class': 'tip'}).inject(this.tip);
var bottom = new Element('div', {'class': 'tip-bottom'}).inject(this.tip);
this.tip.setStyles({position: 'absolute', top: 0, left: 0, visibility: 'hidden'});
if (params.elements) this.attach(params.elements);
},
attach: function(elements){
$$(elements).each(function(element){
var title = element.retrieve('tip:title', element.get('title'));
var text = element.retrieve('tip:text', element.get('rel') || element.get('href'));
var enter = element.retrieve('tip:enter', this.elementEnter.bindWithEvent(this, element));
var leave = element.retrieve('tip:leave', this.elementLeave.bindWithEvent(this, element));
element.addEvents({mouseenter: enter, mouseleave: leave});
if (!this.options.fixed){
var move = element.retrieve('tip:move', this.elementMove.bindWithEvent(this, element));
element.addEvent('mousemove', move);
}
element.store('tip:native', element.get('title'));
element.erase('title');
}, this);
return this;
},
detach: function(elements){
$$(elements).each(function(element){
element.removeEvent('mouseenter', element.retrieve('tip:enter') || $empty);
element.removeEvent('mouseleave', element.retrieve('tip:leave') || $empty);
element.removeEvent('mousemove', element.retrieve('tip:move') || $empty);
element.eliminate('tip:enter').eliminate('tip:leave').eliminate('tip:move');
var original = element.retrieve('tip:native');
if (original) element.set('title', original);
});
return this;
},
elementEnter: function(event, element){
$A(this.container.childNodes).each(Element.dispose);
var title = element.retrieve('tip:title');
if (title){
this.titleElement = new Element('div', {'class': 'tip-title'}).inject(this.container);
this.fill(this.titleElement, title);
}
var text = element.retrieve('tip:text');
if (text){
this.textElement = new Element('div', {'class': 'tip-text'}).inject(this.container);
this.fill(this.textElement, text);
}
this.timer = $clear(this.timer);
this.timer = this.show.delay(this.options.showDelay, this);
this.position((!this.options.fixed) ? event : {page: element.getPosition()});
},
elementLeave: function(event){
$clear(this.timer);
this.timer = this.hide.delay(this.options.hideDelay, this);
},
elementMove: function(event){
this.position(event);
},
position: function(event){
var size = window.getSize(), scroll = window.getScroll();
var tip = {x: this.tip.offsetWidth, y: this.tip.offsetHeight};
var props = {x: 'left', y: 'top'};
for (var z in props){
var pos = event.page[z] + this.options.offsets[z];
if ((pos + tip[z] - scroll[z]) > size[z]) pos = event.page[z] - this.options.offsets[z] - tip[z];
this.tip.setStyle(props[z], pos);
}
},
fill: function(element, contents){
(typeof contents == 'string') ? element.set('html', contents) : element.adopt(contents);
},
show: function(){
this.fireEvent('show', this.tip);
},
hide: function(){
this.fireEvent('hide', this.tip);
}
});
/*
Script: SmoothScroll.js
Class for creating a smooth scrolling effect to all internal links on the page.
License:
MIT-style license.
*/
var SmoothScroll = new Class({
Extends: Fx.Scroll,
initialize: function(options, context){
context = context || document;
var doc = context.getDocument(), win = context.getWindow();
this.parent(doc, options);
this.links = (this.options.links) ? $$(this.options.links) : $$(doc.links);
var location = win.location.href.match(/^[^#]*/)[0] + '#';
this.links.each(function(link){
if (link.href.indexOf(location) != 0) return;
var anchor = link.href.substr(location.length);
if (anchor && $(anchor)) this.useLink(link, anchor);
}, this);
if (!Browser.Engine.webkit419) this.addEvent('complete', function(){
win.location.hash = this.anchor;
}, true);
},
useLink: function(link, anchor){
link.addEvent('click', function(event){
this.anchor = anchor;
this.toElement(anchor);
event.stop();
}.bind(this));
}
});
/*
Script: Slider.js
Class for creating horizontal and vertical slider controls.
License:
MIT-style license.
*/
var Slider = new Class({
Implements: [Events, Options],
options: {/*
onChange: $empty,
onComplete: $empty,*/
onTick: function(position){
if(this.options.snap) position = this.toPosition(this.step);
this.knob.setStyle(this.property, position);
},
snap: false,
offset: 0,
range: false,
wheel: false,
steps: 100,
mode: 'horizontal'
},
initialize: function(element, knob, options){
this.setOptions(options);
this.element = $(element);
this.knob = $(knob);
this.previousChange = this.previousEnd = this.step = -1;
this.element.addEvent('mousedown', this.clickedElement.bind(this));
if (this.options.wheel) this.element.addEvent('mousewheel', this.scrolledElement.bindWithEvent(this));
var offset, limit = {}, modifiers = {'x': false, 'y': false};
switch (this.options.mode){
case 'vertical':
this.axis = 'y';
this.property = 'top';
offset = 'offsetHeight';
break;
case 'horizontal':
this.axis = 'x';
this.property = 'left';
offset = 'offsetWidth';
}
this.half = this.knob[offset] / 2;
this.full = this.element[offset] - this.knob[offset] + (this.options.offset * 2);
this.min = $chk(this.options.range[0]) ? this.options.range[0] : 0;
this.max = $chk(this.options.range[1]) ? this.options.range[1] : this.options.steps;
this.range = this.max - this.min;
this.steps = this.options.steps || this.full;
this.stepSize = Math.abs(this.range) / this.steps;
this.stepWidth = this.stepSize * this.full / Math.abs(this.range) ;
this.knob.setStyle('position', 'relative').setStyle(this.property, - this.options.offset);
modifiers[this.axis] = this.property;
limit[this.axis] = [- this.options.offset, this.full - this.options.offset];
this.drag = new Drag(this.knob, {
snap: 0,
limit: limit,
modifiers: modifiers,
onDrag: this.draggedKnob.bind(this),
onStart: this.draggedKnob.bind(this),
onComplete: function(){
this.draggedKnob();
this.end();
}.bind(this)
});
if (this.options.snap) {
this.drag.options.grid = Math.ceil(this.stepWidth);
this.drag.options.limit[this.axis][1] = this.full;
}
},
set: function(step){
if (!((this.range > 0) ^ (step < this.min))) step = this.min;
if (!((this.range > 0) ^ (step > this.max))) step = this.max;
this.step = Math.round(step);
this.checkStep();
this.end();
this.fireEvent('tick', this.toPosition(this.step));
return this;
},
clickedElement: function(event){
var dir = this.range < 0 ? -1 : 1;
var position = event.page[this.axis] - this.element.getPosition()[this.axis] - this.half;
position = position.limit(-this.options.offset, this.full -this.options.offset);
this.step = Math.round(this.min + dir * this.toStep(position));
this.checkStep();
this.end();
this.fireEvent('tick', position);
},
scrolledElement: function(event){
var mode = (this.options.mode == 'horizontal') ? (event.wheel < 0) : (event.wheel > 0);
this.set(mode ? this.step - this.stepSize : this.step + this.stepSize);
event.stop();
},
draggedKnob: function(){
var dir = this.range < 0 ? -1 : 1;
var position = this.drag.value.now[this.axis];
position = position.limit(-this.options.offset, this.full -this.options.offset);
this.step = Math.round(this.min + dir * this.toStep(position));
this.checkStep();
},
checkStep: function(){
if (this.previousChange != this.step){
this.previousChange = this.step;
this.fireEvent('change', this.step);
}
},
end: function(){
if (this.previousEnd !== this.step){
this.previousEnd = this.step;
this.fireEvent('complete', this.step + '');
}
},
toStep: function(position){
var step = (position + this.options.offset) * this.stepSize / this.full * this.steps;
return this.options.steps ? Math.round(step -= step % this.stepSize) : step;
},
toPosition: function(step){
return (this.full * Math.abs(this.min - step)) / (this.steps * this.stepSize) - this.options.offset;
}
});
/*
Script: Scroller.js
Class which scrolls the contents of any Element (including the window) when the mouse reaches the Element's boundaries.
License:
MIT-style license.
*/
var Scroller = new Class({
Implements: [Events, Options],
options: {
area: 20,
velocity: 1,
onChange: function(x, y){
this.element.scrollTo(x, y);
}
},
initialize: function(element, options){
this.setOptions(options);
this.element = $(element);
this.listener = ($type(this.element) != 'element') ? $(this.element.getDocument().body) : this.element;
this.timer = null;
this.coord = this.getCoords.bind(this);
},
start: function(){
this.listener.addEvent('mousemove', this.coord);
},
stop: function(){
this.listener.removeEvent('mousemove', this.coord);
this.timer = $clear(this.timer);
},
getCoords: function(event){
this.page = (this.listener.get('tag') == 'body') ? event.client : event.page;
if (!this.timer) this.timer = this.scroll.periodical(50, this);
},
scroll: function(){
var size = this.element.getSize(), scroll = this.element.getScroll(), pos = this.element.getPosition(), change = {'x': 0, 'y': 0};
for (var z in this.page){
if (this.page[z] < (this.options.area + pos[z]) && scroll[z] != 0)
change[z] = (this.page[z] - this.options.area - pos[z]) * this.options.velocity;
else if (this.page[z] + this.options.area > (size[z] + pos[z]) && size[z] + size[z] != scroll[z])
change[z] = (this.page[z] - size[z] + this.options.area - pos[z]) * this.options.velocity;
}
if (change.y || change.x) this.fireEvent('change', [scroll.x + change.x, scroll.y + change.y]);
}
});
/*
Script: Accordion.js
An Fx.Elements extension which allows you to easily create accordion type controls.
License:
MIT-style license.
*/
var Accordion = new Class({
Extends: Fx.Elements,
options: {/*
onActive: $empty,
onBackground: $empty,*/
display: 0,
show: false,
height: true,
width: false,
opacity: true,
fixedHeight: false,
fixedWidth: false,
wait: false,
alwaysHide: false
},
initialize: function(){
var params = Array.link(arguments, {'container': Element.type, 'options': Object.type, 'togglers': $defined, 'elements': $defined});
this.parent(params.elements, params.options);
this.togglers = $$(params.togglers);
this.container = $(params.container);
this.previous = -1;
if (this.options.alwaysHide) this.options.wait = true;
if ($chk(this.options.show)){
this.options.display = false;
this.previous = this.options.show;
}
if (this.options.start){
this.options.display = false;
this.options.show = false;
}
this.effects = {};
if (this.options.opacity) this.effects.opacity = 'fullOpacity';
if (this.options.width) this.effects.width = this.options.fixedWidth ? 'fullWidth' : 'offsetWidth';
if (this.options.height) this.effects.height = this.options.fixedHeight ? 'fullHeight' : 'scrollHeight';
for (var i = 0, l = this.togglers.length; i < l; i++) this.addSection(this.togglers[i], this.elements[i]);
this.elements.each(function(el, i){
if (this.options.show === i){
this.fireEvent('active', [this.togglers[i], el]);
} else {
for (var fx in this.effects) el.setStyle(fx, 0);
}
}, this);
if ($chk(this.options.display)) this.display(this.options.display);
},
addSection: function(toggler, element, pos){
toggler = $(toggler);
element = $(element);
var test = this.togglers.contains(toggler);
var len = this.togglers.length;
this.togglers.include(toggler);
this.elements.include(element);
if (len && (!test || pos)){
pos = $pick(pos, len - 1);
toggler.inject(this.togglers[pos], 'before');
element.inject(toggler, 'after');
} else if (this.container && !test){
toggler.inject(this.container);
element.inject(this.container);
}
var idx = this.togglers.indexOf(toggler);
toggler.addEvent('click', this.display.bind(this, idx));
if (this.options.height) element.setStyles({'padding-top': 0, 'border-top': 'none', 'padding-bottom': 0, 'border-bottom': 'none'});
if (this.options.width) element.setStyles({'padding-left': 0, 'border-left': 'none', 'padding-right': 0, 'border-right': 'none'});
element.fullOpacity = 1;
if (this.options.fixedWidth) element.fullWidth = this.options.fixedWidth;
if (this.options.fixedHeight) element.fullHeight = this.options.fixedHeight;
element.setStyle('overflow', 'hidden');
if (!test){
for (var fx in this.effects) element.setStyle(fx, 0);
}
return this;
},
display: function(index){
index = ($type(index) == 'element') ? this.elements.indexOf(index) : index;
if ((this.timer && this.options.wait) || (index === this.previous && !this.options.alwaysHide)) return this;
this.previous = index;
var obj = {};
this.elements.each(function(el, i){
obj[i] = {};
var hide = (i != index) || (this.options.alwaysHide && (el.offsetHeight > 0));
this.fireEvent(hide ? 'background' : 'active', [this.togglers[i], el]);
for (var fx in this.effects) obj[i][fx] = hide ? 0 : el[this.effects[fx]];
}, this);
return this.start(obj);
}
});