<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\String\Slugger\SluggerInterface;

class FileUploadService
{
    private string $uploadsDirectory;
    private SluggerInterface $slugger;

    public function __construct(string $uploadsDirectory, SluggerInterface $slugger)
    {
        $this->uploadsDirectory = $uploadsDirectory;
        $this->slugger = $slugger;
    }

    /**
     * Upload file with meaningful naming and organized folder structure
     */
    public function uploadFile(
        UploadedFile $file, 
        string $entityType, 
        string $fieldName, 
        ?string $entityName = null,
        ?int $entityId = null
    ): string {
        // Create subfolder based on entity type
        $subfolder = $this->getSubfolderForEntity($entityType);
        $targetDirectory = $this->uploadsDirectory . '/' . $subfolder;
        
        // Ensure directory exists
        if (!is_dir($targetDirectory)) {
            mkdir($targetDirectory, 0755, true);
        }

        // Generate meaningful filename
        $originalFilename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeFilename = $this->slugger->slug($originalFilename);
        $extension = $file->guessExtension();
        
        // Create meaningful name based on entity
        $meaningfulName = $this->generateMeaningfulName($entityType, $fieldName, $entityName, $entityId);
        $fileName = $meaningfulName . '-' . uniqid() . '.' . $extension;

        try {
            $file->move($targetDirectory, $fileName);
            return $subfolder . '/' . $fileName;
        } catch (FileException $e) {
            throw new \Exception('Failed to upload file: ' . $e->getMessage());
        }
    }

    /**
     * Get subfolder name based on entity type
     */
    private function getSubfolderForEntity(string $entityType): string
    {
        return match($entityType) {
            'course' => 'courses',
            'instructor' => 'instructors',
            'market_analysis' => 'market-analysis',
            'partner' => 'partners',
            'promotional_banner' => 'banners',
            'admin' => 'admins',
            default => 'misc'
        };
    }

    /**
     * Generate meaningful filename based on entity and field
     */
    private function generateMeaningfulName(
        string $entityType, 
        string $fieldName, 
        ?string $entityName = null,
        ?int $entityId = null
    ): string {
        $timestamp = date('Y-m-d');
        
        // Base name from entity type and field
        $baseName = $entityType . '-' . $fieldName;
        
        // Add entity-specific naming
        if ($entityName) {
            $safeName = $this->slugger->slug($entityName);
            $baseName .= '-' . $safeName;
        } elseif ($entityId) {
            $baseName .= '-id-' . $entityId;
        }
        
        return $baseName . '-' . $timestamp;
    }

    /**
     * Delete file if it exists
     */
    public function deleteFile(string $filePath): bool
    {
        $fullPath = $this->uploadsDirectory . '/' . $filePath;
        
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        
        return true; // File doesn't exist, consider it "deleted"
    }

    /**
     * Get full file path for web access
     */
    public function getWebPath(string $filePath): string
    {
        return '/images/uploads/' . $filePath;
    }

    /**
     * Validate file type for specific entity fields
     */
    public function validateFileType(UploadedFile $file, string $entityType, string $fieldName): bool
    {
        $allowedTypes = $this->getAllowedTypesForField($entityType, $fieldName);
        $mimeType = $file->getMimeType();
        
        return in_array($mimeType, $allowedTypes);
    }

    /**
     * Get allowed MIME types for specific entity fields
     */
    private function getAllowedTypesForField(string $entityType, string $fieldName): array
    {
        $imageTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
        
        return match($entityType . '.' . $fieldName) {
            'course.thumbnail', 'course.banner' => $imageTypes,
            'instructor.profileImage' => $imageTypes,
            'market_analysis.thumbnail' => $imageTypes,
            'partner.logo' => $imageTypes,
            'promotional_banner.backgroundImage' => $imageTypes,
            'admin.profileImage' => $imageTypes,
            default => $imageTypes
        };
    }

    /**
     * Get maximum file size for specific entity fields (in bytes)
     */
    public function getMaxFileSize(string $entityType, string $fieldName): int
    {
        return match($entityType . '.' . $fieldName) {
            'course.thumbnail' => 5 * 1024 * 1024, // 5MB
            'course.banner' => 10 * 1024 * 1024, // 10MB
            'instructor.profileImage' => 5 * 1024 * 1024, // 5MB
            'market_analysis.thumbnail' => 5 * 1024 * 1024, // 5MB
            'partner.logo' => 2 * 1024 * 1024, // 2MB
            'promotional_banner.backgroundImage' => 10 * 1024 * 1024, // 10MB
            'admin.profileImage' => 5 * 1024 * 1024, // 5MB
            default => 5 * 1024 * 1024 // 5MB default
        };
    }

    /**
     * Validate file size
     */
    public function validateFileSize(UploadedFile $file, string $entityType, string $fieldName): bool
    {
        $maxSize = $this->getMaxFileSize($entityType, $fieldName);
        return $file->getSize() <= $maxSize;
    }

    /**
     * Get recommended dimensions for specific entity fields
     */
    public function getRecommendedDimensions(string $entityType, string $fieldName): array
    {
        return match($entityType . '.' . $fieldName) {
            'course.thumbnail' => ['width' => 300, 'height' => 200, 'ratio' => '3:2'],
            'course.banner' => ['width' => 1200, 'height' => 400, 'ratio' => '3:1'],
            'instructor.profileImage' => ['width' => 400, 'height' => 400, 'ratio' => '1:1'],
            'market_analysis.thumbnail' => ['width' => 400, 'height' => 250, 'ratio' => '8:5'],
            'partner.logo' => ['width' => 200, 'height' => 100, 'ratio' => '2:1'],
            'promotional_banner.backgroundImage' => ['width' => 1920, 'height' => 400, 'ratio' => '4.8:1'],
            'admin.profileImage' => ['width' => 200, 'height' => 200, 'ratio' => '1:1'],
            default => ['width' => 400, 'height' => 300, 'ratio' => '4:3']
        };
    }
}
