<?php

namespace App\Command;

use App\Entity\Admin;
use App\Repository\AdminRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:create-master-admin',
    description: 'Create the master admin account for Capitol Academy',
)]
class CreateMasterAdminCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AdminRepository $adminRepository,
        private UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Check if master admin already exists
        $existingAdmin = $this->adminRepository->findByUsername('admin1');
        if ($existingAdmin) {
            $io->warning('Master admin account already exists!');
            return Command::SUCCESS;
        }

        // Create master admin
        $admin = new Admin();
        $admin->setUsername('admin1');
        $admin->setEmail('<EMAIL>');
        $admin->setFirstName('Master');
        $admin->setLastName('Administrator');
        $admin->setPassword($this->passwordHasher->hashPassword($admin, 'Capitol/2025'));
        $admin->setRoles(['ROLE_ADMIN', 'ROLE_SUPER_ADMIN']);
        $admin->setIsMasterAdmin(true);
        $admin->setIsActive(true);
        $admin->setIpAddress('127.0.0.1');
        
        // Set all permissions for master admin
        $admin->setPermissions([
            'users.read',
            'users.edit',
            'users.delete',
            'users.add',
            'courses.read',
            'courses.edit',
            'courses.delete',
            'courses.add',
            'contacts.read',
            'contacts.edit',
            'contacts.delete',
            'contacts.add',
            'admin.create',
            'admin.permissions',
            'admin.manage'
        ]);

        $this->entityManager->persist($admin);
        $this->entityManager->flush();

        $io->success('Master admin account created successfully!');
        $io->table(['Field', 'Value'], [
            ['Username', 'admin1'],
            ['Password', 'Capitol/2025'],
            ['Email', '<EMAIL>'],
            ['Full Name', 'Master Administrator'],
            ['Master Admin', 'Yes'],
            ['Status', 'Active']
        ]);

        return Command::SUCCESS;
    }
}
