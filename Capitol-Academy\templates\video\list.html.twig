{% extends 'base.html.twig' %}

{% block title %}Professional Trading Videos - Capitol Academy{% endblock %}

{% block meta_description %}Master financial markets with Capitol Academy's comprehensive video library. Expert-led content covering technical analysis, risk management, and professional trading strategies.{% endblock %}

{% block stylesheets %}
{{ parent() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
:root {
    --ca-primary: #011a2d;
    --ca-accent: #a90418;
    --ca-light-gray: #F6F7F9;
    --ca-dark-gray: #343a40;
    --ca-medium-gray: #6c757d;
    --ca-white: #ffffff;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3a52 100%);
    padding: 60px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.video-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--ca-accent);
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Main Content */
.main-content {
    background: var(--ca-white);
    padding: 60px 0;
    min-height: 80vh;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    color: var(--ca-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.section-subtitle {
    color: var(--ca-medium-gray);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Video Grid */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Video Card */
.video-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.video-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.video-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.video-card:hover .video-image {
    transform: scale(1.05);
}

.video-placeholder {
    width: 100%;
    height: 220px;
    background: linear-gradient(135deg, var(--ca-primary) 0%, #1a3a52 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.video-placeholder i {
    margin-bottom: 1rem;
    opacity: 0.8;
}

.video-placeholder span {
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 1px;
}

.video-content {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Video Info Rows */
.video-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.video-title-row .video-title {
    color: var(--ca-primary);
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.3;
}

.video-type {
    background: var(--ca-primary);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.video-type.free {
    background: #28a745;
}

.video-type.premium {
    background: var(--ca-accent);
}

.video-category {
    color: var(--ca-medium-gray);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.video-duration {
    color: var(--ca-dark-gray);
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.video-duration i {
    color: var(--ca-primary);
}

.video-price {
    font-size: 1.2rem;
    font-weight: 700;
}

.price-paid {
    color: var(--ca-accent);
}

.price-free {
    color: var(--ca-primary);
}

.video-description {
    color: var(--ca-dark-gray);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 1rem 0;
    flex-grow: 1;
}

.video-footer {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.watch-btn {
    background: var(--ca-primary);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    border: 2px solid var(--ca-primary);
}

.watch-btn:hover {
    background: transparent;
    color: var(--ca-primary);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(1, 26, 45, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }

    .video-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .videos-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .video-content {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 40px 0;
    }

    .page-title {
        font-size: 2rem;
    }

    .main-content {
        padding: 40px 0;
    }

    .video-content {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block body %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="header-content">
            <h1 class="page-title">Professional Trading Videos</h1>
            <p class="page-subtitle">
                Master the financial markets with our comprehensive video library. Expert-led content designed to enhance your trading knowledge and skills.
            </p>
            <div class="video-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ videos|length > 0 ? videos|length : '50+' }}</span>
                    <span class="stat-label">Expert Videos</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">10K+</span>
                    <span class="stat-label">Views</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4.8★</span>
                    <span class="stat-label">Average Rating</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="main-content">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Available Videos</h2>
            <p class="section-subtitle">
                Choose from our comprehensive selection of professional trading videos designed to advance your financial market expertise.
            </p>
        </div>

        <div class="videos-grid">
            {% if videos and videos|length > 0 %}
                {% for video in videos %}
                <div class="video-card">
                    <!-- Video Image -->
                    {% if video.thumbnail %}
                        <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}" alt="{{ video.title }}" class="video-image">
                    {% else %}
                        <div class="video-placeholder">
                            <i class="fas fa-play-circle"></i>
                            <span>{{ video.category|default('VIDEO') }}</span>
                        </div>
                    {% endif %}

                    <!-- Video Content -->
                    <div class="video-content">
                        <!-- Title and Type Row -->
                        <div class="video-row video-title-row">
                            <h3 class="video-title">{{ video.title }}</h3>
                            <span class="video-type {{ video.isFree ? 'free' : 'premium' }}">
                                {{ video.isFree ? 'Free' : 'Premium' }}
                            </span>
                        </div>

                        <!-- Category and Duration Row -->
                        <div class="video-row">
                            <span class="video-category">{{ video.category|default('Trading') }}</span>
                            <div class="video-duration">
                                <i class="fas fa-clock"></i>
                                <span>5-10 min</span>
                            </div>
                        </div>

                        <!-- Price Row -->
                        <div class="video-row">
                            <div></div>
                            {% if video.price and video.price > 0 %}
                            <div class="video-price price-paid">${{ video.price|number_format(2) }}</div>
                            {% else %}
                            <div class="video-price price-free">Free</div>
                            {% endif %}
                        </div>

                        <!-- Video Description -->
                        {% if video.description %}
                        <p class="video-description">
                            {{ video.description|length > 150 ? video.description|slice(0, 150) ~ '...' : video.description }}
                        </p>
                        {% endif %}

                        <!-- Video Footer -->
                        <div class="video-footer">
                            <a href="{{ path('app_video_show', {id: video.id}) }}" class="watch-btn">
                                <i class="fas fa-play"></i>
                                Watch Video
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <!-- Default Video Cards when no videos in database -->
                {% set defaultVideos = [
                    {
                        'title': 'Technical Analysis Basics',
                        'description': 'Learn the fundamentals of technical analysis including chart patterns, support and resistance levels.',
                        'category': 'Technical Analysis',
                        'duration': '15',
                        'price': null,
                        'isFree': true,
                        'icon': 'fas fa-chart-line'
                    },
                    {
                        'title': 'Market Analysis Live Session',
                        'description': 'Join our expert traders for live market analysis and trading opportunities.',
                        'category': 'Market Analysis',
                        'duration': '45',
                        'price': 29.99,
                        'isFree': false,
                        'icon': 'fas fa-broadcast-tower'
                    },
                    {
                        'title': 'Risk Management Strategies',
                        'description': 'Master essential risk management techniques to protect your trading capital.',
                        'category': 'Risk Management',
                        'duration': '20',
                        'price': 19.99,
                        'isFree': false,
                        'icon': 'fas fa-shield-alt'
                    },
                    {
                        'title': 'Forex Trading Introduction',
                        'description': 'Get started with forex trading and understand currency pair movements.',
                        'category': 'Forex',
                        'duration': '12',
                        'price': null,
                        'isFree': true,
                        'icon': 'fas fa-globe'
                    },
                    {
                        'title': 'Cryptocurrency Trading',
                        'description': 'Learn how to trade cryptocurrencies and understand market dynamics.',
                        'category': 'Crypto',
                        'duration': '25',
                        'price': 39.99,
                        'isFree': false,
                        'icon': 'fab fa-bitcoin'
                    },
                    {
                        'title': 'Psychology of Trading',
                        'description': 'Develop the mental discipline required for successful trading.',
                        'category': 'Psychology',
                        'duration': '18',
                        'price': null,
                        'isFree': true,
                        'icon': 'fas fa-brain'
                    }
                ] %}

                {% for video in defaultVideos %}
                <div class="video-card">
                    <!-- Video Image -->
                    <div class="video-placeholder">
                        <i class="{{ video.icon }}"></i>
                        <span>{{ video.category|upper }}</span>
                    </div>

                    <!-- Video Content -->
                    <div class="video-content">
                        <!-- Title and Type Row -->
                        <div class="video-row video-title-row">
                            <h3 class="video-title">{{ video.title }}</h3>
                            <span class="video-type {{ video.isFree ? 'free' : 'premium' }}">
                                {{ video.isFree ? 'Free' : 'Premium' }}
                            </span>
                        </div>

                        <!-- Category and Duration Row -->
                        <div class="video-row">
                            <span class="video-category">{{ video.category }}</span>
                            <div class="video-duration">
                                <i class="fas fa-clock"></i>
                                <span>{{ video.duration }} min</span>
                            </div>
                        </div>

                        <!-- Price Row -->
                        <div class="video-row">
                            <div></div>
                            {% if video.price %}
                            <div class="video-price price-paid">${{ video.price|number_format(2) }}</div>
                            {% else %}
                            <div class="video-price price-free">Free</div>
                            {% endif %}
                        </div>

                        <!-- Video Description -->
                        <p class="video-description">{{ video.description }}</p>

                        <!-- Video Footer -->
                        <div class="video-footer">
                            <a href="#" class="watch-btn" onclick="alert('Video content will be available soon!')">
                                <i class="fas fa-play"></i>
                                Watch Video
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</section>

{% endblock %}
