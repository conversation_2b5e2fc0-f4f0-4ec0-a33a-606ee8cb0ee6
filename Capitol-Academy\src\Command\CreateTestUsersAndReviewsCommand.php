<?php

namespace App\Command;

use App\Entity\User;
use App\Entity\Course;
use App\Entity\CourseReview;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsCommand(
    name: 'app:create-test-users-reviews',
    description: 'Create 3 test users and add certified reviews for CS101 course',
)]
class CreateTestUsersAndReviewsCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        try {
            // Find CS101 course
            $course = $this->entityManager->getRepository(Course::class)->findOneBy(['code' => 'CS101']);
            if (!$course) {
                $io->error('Course CS101 not found. Please create the course first.');
                return Command::FAILURE;
            }

            // Create 3 test users
            $users = [];
            $userData = [
                [
                    'email' => '<EMAIL>',
                    'firstName' => 'John',
                    'lastName' => 'Trader',
                    'password' => 'TradingPro123!',
                    'phone' => '******-0101'
                ],
                [
                    'email' => '<EMAIL>',
                    'firstName' => 'Sarah',
                    'lastName' => 'Investor',
                    'password' => 'InvestSmart456!',
                    'phone' => '******-0102'
                ],
                [
                    'email' => '<EMAIL>',
                    'firstName' => 'Mike',
                    'lastName' => 'Analyst',
                    'password' => 'AnalyzeMarkets789!',
                    'phone' => '******-0103'
                ]
            ];

            foreach ($userData as $data) {
                // Check if user already exists
                $existingUser = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $data['email']]);
                if ($existingUser) {
                    $io->warning("User {$data['email']} already exists. Skipping creation.");
                    $users[] = $existingUser;
                    continue;
                }

                $user = new User();
                $user->setEmail($data['email']);
                $user->setFirstName($data['firstName']);
                $user->setLastName($data['lastName']);
                $user->setPhone($data['phone']);
                $user->setIsVerified(true);
                $user->setRoles(['ROLE_USER']);

                // Hash the password
                $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
                $user->setPassword($hashedPassword);

                $this->entityManager->persist($user);
                $users[] = $user;

                $io->success("Created user: {$data['email']} with password: {$data['password']}");
            }

            $this->entityManager->flush();

            // Create 3 certified reviews for CS101
            $reviewsData = [
                [
                    'rating' => 5,
                    'comment' => 'Excellent course! The content is comprehensive and well-structured. I learned so much about financial markets and trading strategies. The instructor explains complex concepts in an easy-to-understand manner. Highly recommended for anyone serious about trading.',
                    'userIndex' => 0
                ],
                [
                    'rating' => 4,
                    'comment' => 'Great course with practical insights into market analysis. The modules are well-organized and the examples are very helpful. I particularly enjoyed the risk management section. Would definitely recommend to fellow traders.',
                    'userIndex' => 1
                ],
                [
                    'rating' => 5,
                    'comment' => 'Outstanding educational content! This course has transformed my understanding of financial markets. The technical analysis section was particularly valuable. The instructor\'s expertise really shows through the quality of the material.',
                    'userIndex' => 2
                ]
            ];

            foreach ($reviewsData as $reviewData) {
                // Check if review already exists for this user and course
                $existingReview = $this->entityManager->getRepository(CourseReview::class)->findOneBy([
                    'course' => $course,
                    'user' => $users[$reviewData['userIndex']]
                ]);

                if ($existingReview) {
                    $io->warning("Review already exists for user {$users[$reviewData['userIndex']]->getEmail()} on course {$course->getCode()}. Skipping creation.");
                    continue;
                }

                $review = new CourseReview();
                $review->setCourse($course);
                $review->setUser($users[$reviewData['userIndex']]);
                $review->setRating($reviewData['rating']);
                $review->setComment($reviewData['comment']);
                $review->setIsCertified(true); // Mark as certified
                $review->setIsApproved(true);  // Mark as approved
                $review->setIsFeatured(false); // Not featured by default

                $this->entityManager->persist($review);

                $io->success("Created certified review by {$users[$reviewData['userIndex']]->getFirstName()} {$users[$reviewData['userIndex']]->getLastName()} - Rating: {$reviewData['rating']}/5");
            }

            $this->entityManager->flush();

            // Update course statistics
            $approvedReviews = $this->entityManager->getRepository(CourseReview::class)->findBy([
                'course' => $course,
                'is_approved' => true
            ]);

            $totalReviews = count($approvedReviews);
            $averageRating = 0;

            if ($totalReviews > 0) {
                $totalRating = array_sum(array_map(fn($review) => $review->getRating(), $approvedReviews));
                $averageRating = round($totalRating / $totalReviews, 2);
            }

            $course->setTotalReviews($totalReviews);
            $course->setAverageRating((string)$averageRating);

            $this->entityManager->flush();

            $io->success("Updated course statistics - Total Reviews: {$totalReviews}, Average Rating: {$averageRating}/5");

            $io->success('Successfully created 3 test users and 3 certified reviews for CS101 course!');

            $io->section('Login Credentials:');
            $io->table(
                ['Email', 'Password', 'Name'],
                [
                    ['<EMAIL>', 'TradingPro123!', 'John Trader'],
                    ['<EMAIL>', 'InvestSmart456!', 'Sarah Investor'],
                    ['<EMAIL>', 'AnalyzeMarkets789!', 'Mike Analyst']
                ]
            );

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Error creating test data: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
