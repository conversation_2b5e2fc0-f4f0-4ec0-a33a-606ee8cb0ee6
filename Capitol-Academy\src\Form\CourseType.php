<?php

namespace App\Form;

use App\Entity\Course;
use App\Repository\CategoryRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class CourseType extends AbstractType
{
    public function __construct(
        private CategoryRepository $categoryRepository
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Course Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter course title'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Enter course description'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => $this->getCategoryChoices(),
                'attr' => [
                    'class' => 'form-control'
                ],
                'required' => false,
                'placeholder' => 'Select a category'
            ])
            ->add('mode', ChoiceType::class, [
                'label' => 'Delivery Mode',
                'choices' => [
                    'On-site Training' => 'onsite',
                    'Online Course' => 'online',
                    'Hybrid (Online + On-site)' => 'hybrid'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Delivery mode is required'])
                ],
                'help' => 'Choose how this course will be delivered to students'
            ])
            ->add('moodleCourseId', TextType::class, [
                'label' => 'Moodle Course ID',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter Moodle course ID (for online courses)'
                ],
                'help' => 'Required for online and hybrid courses. Leave empty for on-site only courses.'
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Price',
                'currency' => 'USD',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Price is required']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ]
            ])
            ->add('duration', IntegerType::class, [
                'label' => 'Duration (minutes)',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Duration in minutes'
                ],
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 1, 'message' => 'Duration must be at least 1 minute'])
                ],
                'help' => 'Total course duration in minutes'
            ])
            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 5MB.'
                    ])
                ],
                'help' => 'Upload a thumbnail image (JPEG, PNG, WebP). Max size: 5MB'
            ])
            ->add('bannerFile', FileType::class, [
                'label' => 'Banner Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '10M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 10MB.'
                    ])
                ],
                'help' => 'Upload a banner image (JPEG, PNG, WebP). Max size: 10MB'
            ])
            ->add('learningOutcomes', TextareaType::class, [
                'label' => 'Learning Outcomes',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 3,
                    'placeholder' => 'Enter learning outcomes (one per line)'
                ],
                'help' => 'Enter each learning outcome on a separate line'
            ])
            ->add('features', TextareaType::class, [
                'label' => 'Course Features',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 3,
                    'placeholder' => 'Enter course features (one per line)'
                ],
                'help' => 'Enter each feature on a separate line'
            ])

            ->add('isActive', CheckboxType::class, [
                'label' => 'Active',
                'required' => false,
                'data' => true,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Uncheck to hide this course from public view'
            ]);
    }

    private function getCategoryChoices(): array
    {
        try {
            $categories = $this->categoryRepository->findForCourses();
            $choices = [];

            foreach ($categories as $category) {
                $choices[$category->getName()] = $category->getName();
            }

            return $choices;
        } catch (\Exception $e) {
            // Fallback in case of database error
            return [
                'Technical Analysis' => 'Technical Analysis',
                'Fundamental Analysis' => 'Fundamental Analysis',
                'Risk Management' => 'Risk Management',
                'Trading Psychology' => 'Trading Psychology'
            ];
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Course::class,
        ]);
    }
}
