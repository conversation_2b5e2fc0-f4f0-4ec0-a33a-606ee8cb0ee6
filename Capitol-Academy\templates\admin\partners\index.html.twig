{% extends 'admin/base.html.twig' %}

{% block title %}Partner Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Partner Management{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item active">Partners</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Partners Management',
    'page_icon': 'fas fa-handshake',
    'search_placeholder': 'Search partners by name or website...',
    'create_button': {
        'url': path('admin_partners_create'),
        'text': 'Add New Partner',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Partners',
            'value': partners|length,
            'icon': 'fas fa-handshake',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Partners',
            'value': partners|filter(partner => partner.isActive)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive Partners',
            'value': partners|filter(partner => not partner.isActive)|length,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': partners|filter(partner => partner.createdAt and partner.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-plus-circle',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Logo'},
            {'text': 'Name'},
            {'text': 'Order'},
            {'text': 'Status'},
            {'text': 'Added'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for partner in partners %}
            {% set row_cells = [
                {
                    'content': partner.logoPath ?
                        '<img src="' ~ partner.logoUrl ~ '" alt="' ~ partner.name ~ '" style="width: 60px; height: 40px; object-fit: contain; border-radius: 4px;" onerror="this.src=\'/images/placeholders/image-placeholder.png\'">' :
                        '<div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 40px; border-radius: 4px; border: 2px solid #f8f9fa;"><i class="fas fa-image text-muted"></i></div>'
                },
                {
                    'content': '<h6 class="partner-name mb-0 font-weight-bold text-dark">' ~ partner.name ~ '</h6>'
                },
                {
                    'content': '<span class="badge bg-secondary">' ~ (partner.displayOrder|default('0')) ~ '</span>'
                },
                {
                    'content': partner.isActive ?
                        '<span class="badge bg-success">Active</span>' :
                        '<span class="badge bg-secondary">Inactive</span>'
                },
                {
                    'content': '<span class="text-dark font-weight-medium">' ~ partner.createdAt|date('M d, Y') ~ '</span>'
                },
                {
                    'content': '<div class="btn-group" role="group">
                        <a href="' ~ path('admin_partners_show_by_slug', {'slug': partner.slug}) ~ '" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="View Partner"><i class="fas fa-eye"></i></a>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, ' ~ (partner.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (partner.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;" title="' ~ (partner.isActive ? 'Deactivate' : 'Activate') ~ ' Partner" onclick="togglePartnerStatus(' ~ partner.id ~ ', \'' ~ partner.name ~ '\', ' ~ partner.isActive ~ ')"><i class="fas fa-' ~ (partner.isActive ? 'pause' : 'play') ~ '"></i></button>
                        <button type="button" class="btn btn-sm shadow-sm" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;" title="Delete Partner" onclick="deletePartner(' ~ partner.id ~ ', \'' ~ partner.name ~ '\')"><i class="fas fa-trash"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'partner-row',
            'empty_message': 'No partners found',
            'empty_icon': 'fas fa-handshake',
            'empty_description': 'Get started by adding your first partner.',
            'search_config': {
                'fields': ['.partner-name']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.partner-row',
        ['.partner-name']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Partner management functions
function togglePartnerStatus(partnerId, partnerName, isActive) {
    showStatusModal(partnerName, isActive, function() {
        executePartnerStatusToggle(partnerId);
    });
}

function deletePartner(partnerId, partnerName) {
    showDeleteModal(partnerName, function() {
        executePartnerDelete(partnerId);
    });
}

// Actual execution functions
function executePartnerStatusToggle(partnerId) {
    fetch(`/admin/partners/${partnerId}/toggle`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the partner status');
    });
}

function executePartnerDelete(partnerId) {
    fetch(`/admin/partners/${partnerId}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the partner');
    });
}
</script>
{% endblock %}
