<?php

namespace App\Form;

use App\Entity\MarketAnalysis;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;


use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class MarketAnalysisType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Article Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter article title...',
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('assetType', ChoiceType::class, [
                'label' => 'Asset Type',
                'choices' => [
                    'Stocks' => 'stocks',
                    'Forex' => 'forex',
                    'Cryptocurrency' => 'crypto',
                    'Crude Oil' => 'crude_oil',
                    'Gold' => 'gold',
                    'Commodities' => 'commodities'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'placeholder' => 'Select asset type...',
                'constraints' => [
                    new NotBlank(['message' => 'Asset type is required'])
                ]
            ])
            ->add('isActive', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Status is required'])
                ]
            ])
            ->add('excerpt', TextareaType::class, [
                'label' => 'Article Excerpt',
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 3,
                    'placeholder' => 'Brief description of the article (max 500 characters)...',
                    'maxlength' => 500
                ],
                'help' => 'This will be displayed in the article preview cards.',
                'constraints' => [
                    new NotBlank(['message' => 'Excerpt is required']),
                    new Length(['max' => 500, 'maxMessage' => 'Excerpt cannot be longer than 500 characters'])
                ]
            ])
            ->add('content', TextareaType::class, [
                'label' => 'Full Content',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 10,
                    'placeholder' => 'Full article content (optional)...'
                ],
                'help' => 'Optional: Full article content for detailed view.'
            ])
            ->add('author', TextType::class, [
                'label' => 'Author',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter author name (leave empty for "Capitol Academy Analyst")...',
                    'maxlength' => 255
                ],
                'help' => 'Leave empty to automatically set as "Capitol Academy Analyst"',
                'constraints' => [
                    new Length(['max' => 255, 'maxMessage' => 'Author name cannot be longer than 255 characters'])
                ]
            ])
            ->add('publishDate', DateTimeType::class, [
                'label' => 'Publish Date',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'form-control'
                ],
                'help' => 'When this article should be published.',
                'constraints' => [
                    new NotBlank(['message' => 'Publish date is required'])
                ]
            ])



            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'help' => 'Upload a thumbnail image for article cards (JPG, PNG, GIF, WebP). Recommended size: 400x300px.',
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPG, PNG, GIF, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size allowed is 5MB.'
                    ])
                ]
            ])
            ->add('featuredImageFile', FileType::class, [
                'label' => 'Featured Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'help' => 'Upload a featured image for article detail page (JPG, PNG, GIF, WebP). Recommended size: 1200x600px.',
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPG, PNG, GIF, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size allowed is 5MB.'
                    ])
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => MarketAnalysis::class,
            'attr' => [
                'novalidate' => 'novalidate' // We'll handle validation on the server side
            ]
        ]);
    }
}
