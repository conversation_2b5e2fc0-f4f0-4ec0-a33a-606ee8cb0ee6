{% extends 'admin/base.html.twig' %}

{% block title %}Admin Dashboard - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="mb-2">Welcome to Capitol Academy Admin</h2>
                            <p class="mb-0">Trading Education Platform Management Dashboard</p>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Users Stats -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Users</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.users.total|number_format }}</div>
                            <div class="text-xs text-success">
                                <i class="fas fa-arrow-up"></i> {{ stats.users.new_this_month }} new this month
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Stats -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ stats.orders.total_revenue|number_format(2) }}</div>
                            <div class="text-xs text-info">
                                <i class="fas fa-calendar"></i> ${{ stats.orders.monthly_revenue|number_format(2) }} this month
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Stats -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.orders.total|number_format }}</div>
                            <div class="text-xs text-success">
                                {{ stats.orders.conversion_rate }}% conversion rate
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Stats -->
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Content Items</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.content.videos.total + stats.content.courses.total }}
                            </div>
                            <div class="text-xs text-info">
                                {{ stats.content.videos.total }} videos, {{ stats.content.courses.total }} courses
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-video fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="row">
        <!-- Revenue Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue Overview</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Breakdown -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Content Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="contentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Popular Content -->
    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
                    <a href="{{ path('admin_order_index') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_activity.recent_orders|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in recent_activity.recent_orders %}
                                        <tr>
                                            <td>
                                                <a href="{{ path('admin_order_show', {id: order.id}) }}" class="text-decoration-none">
                                                    #{{ order.orderNumber }}
                                                </a>
                                            </td>
                                            <td>{{ order.user.firstName }} {{ order.user.lastName }}</td>
                                            <td>${{ order.totalPrice|number_format(2) }}</td>
                                            <td>
                                                {% set status_class = 'secondary' %}
                                                {% if order.paymentStatus == 'completed' %}
                                                    {% set status_class = 'success' %}
                                                {% elseif order.paymentStatus == 'pending' %}
                                                    {% set status_class = 'warning' %}
                                                {% elseif order.paymentStatus == 'failed' %}
                                                    {% set status_class = 'danger' %}
                                                {% endif %}
                                                <span class="badge bg-{{ status_class }}">{{ order.statusLabel }}</span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center py-3">No recent orders</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Popular Content -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Popular Content</h6>
                </div>
                <div class="card-body">
                    {% if popular_content|length > 0 %}
                        {% for item in popular_content|slice(0, 5) %}
                            <div class="d-flex align-items-center mb-3">
                                <div class="mr-3">
                                    {% if item.type == 'video' %}
                                        <i class="fas fa-video text-primary"></i>
                                    {% elseif item.type == 'course' %}
                                        <i class="fas fa-graduation-cap text-success"></i>
                                    {% else %}
                                        <i class="fas fa-layer-group text-info"></i>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">{{ item.title }}</div>
                                    <div class="text-xs text-muted">{{ item.type|title }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="font-weight-bold">{{ item.count }} sales</div>
                                    <div class="text-xs text-success">${{ item.revenue|number_format(2) }}</div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center py-3">No sales data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('admin_video_new') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-plus me-2"></i>Add New Video
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('admin_courses_create') }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus me-2"></i>Add New Course
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ path('admin_order_index') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-line me-2"></i>View Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [{% for data in revenue_data %}'{{ data.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Revenue',
                    data: [{% for data in revenue_data %}{{ data.revenue }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Content Chart
        const contentCtx = document.getElementById('contentChart').getContext('2d');
        const contentChart = new Chart(contentCtx, {
            type: 'doughnut',
            data: {
                labels: ['Videos', 'Courses'],
                datasets: [{
                    data: [
                        {{ stats.content.videos.total }},
                        {{ stats.content.courses.total }}
                    ],
                    backgroundColor: ['#4e73df', '#1cc88a'],
                    hoverBackgroundColor: ['#2e59d9', '#17a673'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
{% endblock %}
