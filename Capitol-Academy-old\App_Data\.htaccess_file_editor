# invoke rewrite engine
RewriteEngine On
RewriteBase /scraping/files/live/capitol-academy.com_h.emna_capitol-academy.com_3702nk7nwe/

Options -Indexes
<IfModule mod_negotiation.c>
	Options -MultiViews
</IfModule>

# Specific redirections
RewriteRule ^index\.html$ /scraping/files/live/capitol-academy.com_h.emna_capitol-academy.com_3702nk7nwe/ [L,R=301]

# Redirect all extensions to html
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.shtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.phtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.jhtml$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.htm$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.php$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.aspx$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.asp$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.jsp$ $1.html [L,NC,R=301]
RewriteCond %{QUERY_STRING} ^$
RewriteRule ^(.*)\.apk$ $1.html [L,NC,R=301]
# End: Redirect all extensions to html
