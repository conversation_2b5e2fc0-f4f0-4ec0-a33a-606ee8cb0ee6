<?php

namespace App\Tests\Controller;

use App\Entity\User;
use App\Entity\Video;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class VideoControllerTest extends WebTestCase
{
    public function testStreamVideoMethodExists(): void
    {
        $client = static::createClient();
        
        // Test that the streamVideo method exists and is accessible
        $this->assertTrue(method_exists(\App\Controller\VideoController::class, 'streamVideo'));
        
        // Test that the route is properly configured
        $router = $client->getContainer()->get('router');
        $route = $router->getRouteCollection()->get('app_video_stream');
        
        $this->assertNotNull($route, 'Route app_video_stream should exist');
        $this->assertEquals(['POST'], $route->getMethods(), 'Route should only accept POST method');
        $this->assertStringContains('{id}/stream', $route->getPath(), 'Route path should contain {id}/stream');
    }

    public function testStreamVideoRequiresAuthentication(): void
    {
        $client = static::createClient();
        
        // Try to access stream endpoint without authentication
        $client->request('POST', '/videos/1/stream');
        
        // Should redirect to login or return 401/403
        $this->assertContains($client->getResponse()->getStatusCode(), [302, 401, 403]);
    }

    public function testStreamVideoWithInvalidVideo(): void
    {
        $client = static::createClient();
        
        // Create a test user and authenticate
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setFirstName('Test');
        $user->setLastName('User');
        $user->setPassword('password');
        
        $client->loginUser($user);
        
        // Try to access non-existent video
        $client->request('POST', '/videos/99999/stream');
        
        // Should return 404
        $this->assertEquals(404, $client->getResponse()->getStatusCode());
    }

    public function testVideoShowPageAccessible(): void
    {
        $client = static::createClient();
        
        // Test that video show pages are accessible
        $client->request('GET', '/videos/test-video-slug');
        
        // Should either show the video or return 404 if not found
        $this->assertContains($client->getResponse()->getStatusCode(), [200, 404]);
    }

    public function testFreeVideosPageAccessible(): void
    {
        $client = static::createClient();
        
        // Test that free videos page is accessible
        $client->request('GET', '/videos/free-courses');
        
        $this->assertEquals(200, $client->getResponse()->getStatusCode());
        $this->assertSelectorExists('h1', 'Page should have a heading');
    }

    public function testPremiumVideosRequiresAuthentication(): void
    {
        $client = static::createClient();
        
        // Try to access premium videos without authentication
        $client->request('GET', '/videos/');
        
        // Should redirect to login
        $this->assertEquals(302, $client->getResponse()->getStatusCode());
    }
}
