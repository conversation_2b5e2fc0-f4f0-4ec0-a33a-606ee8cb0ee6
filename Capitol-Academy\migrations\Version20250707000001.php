<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Add missing summary column to market_analysis table
 */
final class Version20250707000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add missing summary column to market_analysis table';
    }

    public function up(Schema $schema): void
    {
        // Add summary column to market_analysis table if it doesn't exist
        $this->addSql('ALTER TABLE market_analysis ADD COLUMN summary LONGTEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Remove the summary column if rolling back
        $this->addSql('ALTER TABLE market_analysis DROP COLUMN summary');
    }
}
