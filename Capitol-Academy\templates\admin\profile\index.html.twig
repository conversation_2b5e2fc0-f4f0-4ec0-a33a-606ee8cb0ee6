{% extends 'admin/base.html.twig' %}

{% block title %}Admin Profile - Capitol Academy Admin{% endblock %}

{% block page_title %}Admin Profile{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
<li class="breadcrumb-item active">Profile</li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- Professional Profile Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-4">
                            <img src="{{ admin.profileImageUrl }}"
                                 alt="Admin Profile"
                                 class="rounded-circle shadow-lg"
                                 style="width: 100px; height: 100px; object-fit: cover; border: 4px solid rgba(255,255,255,0.3);">
                        </div>
                        <div class="flex-grow-1">
                            <h2 class="mb-2 font-weight-bold">{{ admin.fullName }}</h2>
                            <p class="mb-1" style="font-size: 1.1rem; opacity: 0.9;">
                                <i class="fas fa-envelope me-2"></i>{{ admin.email }}
                            </p>
                            <p class="mb-0" style="opacity: 0.8;">
                                <i class="fas fa-user-shield me-2"></i>Capitol Academy Administrator
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <button type="button" class="btn btn-light" onclick="scrollToSettings()">
                                <i class="fas fa-cog me-2"></i>Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Information Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        Personal Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Full Name</label>
                                <p class="h6 mb-0">{{ admin.fullName }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Username</label>
                                <p class="h6 mb-0">{{ admin.username }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Email Address</label>
                                <p class="h6 mb-0">{{ admin.email }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Status Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-shield-alt me-2 text-success"></i>
                        Account Status
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Status</label>
                                <p class="h6 mb-0">
                                    {% if admin.isActive %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Account Created</label>
                                <p class="h6 mb-0">{{ admin.createdAt|date('M j, Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">Last Login</label>
                                <p class="h6 mb-0">
                                    {% if admin.lastLoginAt %}
                                        {{ admin.lastLoginAt|date('M j, Y g:i A') }}
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-group">
                                <label class="text-muted small font-weight-bold mb-1">IP Address</label>
                                <p class="h6 mb-0">
                                    {% if admin.ipAddress %}
                                        <code class="bg-light text-dark px-2 py-1 rounded">{{ admin.ipAddress }}</code>
                                    {% else %}
                                        <span class="text-muted">Not recorded</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Permissions Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-key me-2 text-warning"></i>
                        Admin Permissions
                    </h5>
                </div>
                <div class="card-body p-4">
                    {% if admin.isMasterAdmin %}
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-crown me-2"></i>
                            <strong>Master Administrator:</strong> You have full access to all system features and settings.
                        </div>
                    {% endif %}
                    
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="permission-group">
                                <h6 class="text-muted mb-2">Course Management</h6>
                                <div class="d-flex flex-column gap-1">
                                    {% if admin.hasPermission('courses.read') %}
                                        <span class="badge bg-success">View Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.add') %}
                                        <span class="badge bg-success">Add Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.edit') %}
                                        <span class="badge bg-success">Edit Courses</span>
                                    {% endif %}
                                    {% if admin.hasPermission('courses.delete') %}
                                        <span class="badge bg-success">Delete Courses</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="permission-group">
                                <h6 class="text-muted mb-2">User Management</h6>
                                <div class="d-flex flex-column gap-1">
                                    {% if admin.hasPermission('users.read') %}
                                        <span class="badge bg-info">View Users</span>
                                    {% endif %}
                                    {% if admin.hasPermission('users.edit') %}
                                        <span class="badge bg-info">Edit Users</span>
                                    {% endif %}
                                    {% if admin.hasPermission('users.delete') %}
                                        <span class="badge bg-info">Delete Users</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="permission-group">
                                <h6 class="text-muted mb-2">Contact Management</h6>
                                <div class="d-flex flex-column gap-1">
                                    {% if admin.hasPermission('contacts.read') %}
                                        <span class="badge bg-warning">View Contacts</span>
                                    {% endif %}
                                    {% if admin.hasPermission('contacts.edit') %}
                                        <span class="badge bg-warning">Edit Contacts</span>
                                    {% endif %}
                                    {% if admin.hasPermission('contacts.delete') %}
                                        <span class="badge bg-warning">Delete Contacts</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="permission-group">
                                <h6 class="text-muted mb-2">Admin Management</h6>
                                <div class="d-flex flex-column gap-1">
                                    {% if admin.hasPermission('admin.read') %}
                                        <span class="badge bg-danger">View Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.add') %}
                                        <span class="badge bg-danger">Add Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.edit') %}
                                        <span class="badge bg-danger">Edit Admins</span>
                                    {% endif %}
                                    {% if admin.hasPermission('admin.delete') %}
                                        <span class="badge bg-danger">Delete Admins</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-primary" onclick="scrollToSettings()">
                            <i class="fas fa-cog me-2"></i>Profile Settings
                        </button>
                        <a href="{{ path('admin_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="{{ path('admin_courses') }}" class="btn btn-success">
                            <i class="fas fa-graduation-cap me-2"></i>Manage Courses
                        </a>
                        <a href="{{ path('admin_users') }}" class="btn btn-info">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a href="{{ path('admin_contacts') }}" class="btn btn-warning">
                            <i class="fas fa-envelope me-2"></i>Manage Contacts
                        </a>
                        <a href="{{ path('admin_add_admin') }}" class="btn"
                           style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border: none; border-radius: 6px;"
                           onmouseover="this.style.background='linear-gradient(135deg, #2a5298 0%, #1e3c72 100%)'"
                           onmouseout="this.style.background='linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'">
                            <i class="fas fa-user-plus me-2"></i>Add New Admin
                        </a>
                        <a href="{{ path('admin_admins') }}" class="btn"
                           style="background: #2a5298; color: white; border: none; border-radius: 6px;"
                           onmouseover="this.style.background='#1e3c72'"
                           onmouseout="this.style.background='#2a5298'">
                            <i class="fas fa-users-cog me-2"></i>Manage Admins
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Section -->
    <div class="row" id="profile-settings-section">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; color: #343a40; border-radius: 8px 8px 0 0; border-bottom: 1px solid #dee2e6;">
                    <h5 class="mb-0 font-weight-bold">
                        <i class="fas fa-cog me-2" style="color: #1e3c72;"></i>
                        Profile Settings
                    </h5>
                </div>
                <div class="card-body p-0">
                    <!-- Profile Image Upload Section -->
                    <div class="p-4 border-bottom">
                        <h6 class="text-dark mb-3">
                            <i class="fas fa-camera me-2 text-success"></i>
                            Profile Image
                        </h6>
                        <form method="post" action="{{ path('admin_profile') }}" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload_profile_image">

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="profile_image_file" class="form-label font-weight-medium">
                                            <i class="fas fa-upload me-1"></i>
                                            Upload New Profile Image
                                        </label>
                                        <input type="file"
                                               class="form-control"
                                               id="profile_image_file"
                                               name="profile_image_file"
                                               accept="image/jpeg,image/png,image/jpg"
                                               style="border: 2px solid #1e3c72; border-radius: 8px;">
                                        <div class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                        </div>
                                    </div>

                                    <div class="image-preview mt-3" id="image-preview" style="display: none;">
                                        <label class="form-label font-weight-medium">Preview:</label>
                                        <div>
                                            <img src="" alt="Preview" class="rounded-circle shadow" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex justify-content-center">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-save me-2"></i>
                                            Update Image
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Change Password Section -->
                    <div class="p-4 border-bottom">
                        <h6 class="text-dark mb-3">
                            <i class="fas fa-lock me-2 text-warning"></i>
                            Change Password
                        </h6>
                        <form method="post" action="{{ path('admin_profile') }}" class="needs-validation" novalidate>
                            <input type="hidden" name="action" value="change_password">

                            <div class="row g-4">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label font-weight-medium">
                                            <i class="fas fa-key me-1"></i>
                                            Current Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password"
                                               class="form-control"
                                               id="current_password"
                                               name="current_password"
                                               required
                                               style="border: 2px solid #1e3c72; border-radius: 8px;">
                                        <div class="invalid-feedback">
                                            Please enter your current password.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label font-weight-medium">
                                            <i class="fas fa-lock me-1"></i>
                                            New Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password"
                                               class="form-control"
                                               id="new_password"
                                               name="new_password"
                                               minlength="6"
                                               required
                                               style="border: 2px solid #1e3c72; border-radius: 8px;">
                                        <div class="invalid-feedback">
                                            Password must be at least 6 characters long.
                                        </div>
                                        <div class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Password must be at least 6 characters long.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label font-weight-medium">
                                            <i class="fas fa-lock me-1"></i>
                                            Confirm New Password <span class="text-danger">*</span>
                                        </label>
                                        <input type="password"
                                               class="form-control"
                                               id="confirm_password"
                                               name="confirm_password"
                                               minlength="6"
                                               required
                                               style="border: 2px solid #1e3c72; border-radius: 8px;">
                                        <div class="invalid-feedback">
                                            Please confirm your new password.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-center mt-3">
                                        <button type="submit" class="btn btn-warning btn-lg">
                                            <i class="fas fa-save me-2"></i>
                                            Change Password
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block javascripts %}
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    $('#confirm_password').on('input', function() {
        var newPassword = $('#new_password').val();
        var confirmPassword = $(this).val();

        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    $('#profile_image_file').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $(previewSelector).show();
                $(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            $(previewSelector).hide();
        }
    }
});

// Smooth scroll to settings section
function scrollToSettings() {
    document.getElementById('profile-settings-section').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}
</script>
{% endblock %}

{% endblock %}
