{% extends 'admin/base.html.twig' %}

{% block title %}Profile Settings - Capitol Academy Admin{% endblock %}

{% block page_title %}Profile Settings{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_profile') }}">Profile</a></li>
<li class="breadcrumb-item active">Settings</li>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- Professional Settings Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; border-radius: 8px;">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h2 class="mb-1 font-weight-bold">Profile Settings</h2>
                            <p class="mb-0" style="opacity: 0.9;">Manage your admin account settings and preferences</p>
                        </div>
                        <div>
                            <a href="{{ path('admin_profile') }}" class="btn btn-light">
                                <i class="fas fa-arrow-left me-2"></i>Back to Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Profile Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-user-circle me-2 text-primary"></i>
                        Current Profile Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-4">
                            <img src="{{ admin.profileImageUrl }}"
                                 alt="Admin Profile"
                                 id="current-profile-image"
                                 class="rounded-circle shadow"
                                 style="width: 80px; height: 80px; object-fit: cover; border: 3px solid #1e3c72;">
                        </div>
                        <div class="flex-grow-1">
                            <h4 class="mb-1 text-primary">{{ admin.fullName }}</h4>
                            <p class="mb-1 text-muted">{{ admin.email }}</p>
                            <p class="mb-0 text-muted">
                                <i class="fas fa-user-tag me-1"></i>{{ admin.username }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Image Upload Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-camera me-2 text-success"></i>
                        Profile Image
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="upload_profile_image">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="profile_image" class="form-label font-weight-medium">
                                        <i class="fas fa-upload me-1"></i>
                                        Upload New Profile Image
                                    </label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="profile_image" 
                                           name="profile_image" 
                                           accept="image/jpeg,image/png,image/jpg"
                                           style="border: 2px solid #1e3c72; border-radius: 8px;">
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        JPEG/PNG only, max 2MB. Recommended: 400x400px square image.
                                    </div>
                                </div>
                                
                                <div class="image-preview mt-3" id="image-preview" style="display: none;">
                                    <label class="form-label font-weight-medium">Preview:</label>
                                    <div>
                                        <img src="" alt="Preview" class="rounded-circle shadow" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #1e3c72;">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-center">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        Update Image
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-lock me-2 text-warning"></i>
                        Change Password
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_password" class="form-label font-weight-medium">
                                        <i class="fas fa-key me-1"></i>
                                        Current Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="current_password" 
                                           name="current_password" 
                                           required
                                           style="border: 2px solid #1e3c72; border-radius: 8px;">
                                    <div class="invalid-feedback">
                                        Please enter your current password.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label font-weight-medium">
                                        <i class="fas fa-lock me-1"></i>
                                        New Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="new_password" 
                                           name="new_password" 
                                           minlength="6"
                                           required
                                           style="border: 2px solid #1e3c72; border-radius: 8px;">
                                    <div class="invalid-feedback">
                                        Password must be at least 6 characters long.
                                    </div>
                                    <div class="form-text text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Password must be at least 6 characters long.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label font-weight-medium">
                                        <i class="fas fa-lock me-1"></i>
                                        Confirm New Password <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           minlength="6"
                                           required
                                           style="border: 2px solid #1e3c72; border-radius: 8px;">
                                    <div class="invalid-feedback">
                                        Please confirm your new password.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-center mt-3">
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="fas fa-save me-2"></i>
                                        Change Password
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Security Section -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="border-radius: 8px;">
                <div class="card-header" style="background: #f8f9fa; border-bottom: 1px solid #dee2e6; border-radius: 8px 8px 0 0;">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-shield-alt me-2 text-danger"></i>
                        Account Security
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="security-item">
                                <h6 class="text-muted mb-2">Account Status</h6>
                                <p class="mb-0">
                                    {% if admin.isActive %}
                                        <span class="badge bg-success px-3 py-2">
                                            <i class="fas fa-check-circle me-1"></i>Active & Secure
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger px-3 py-2">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="security-item">
                                <h6 class="text-muted mb-2">Last Login</h6>
                                <p class="mb-0 font-weight-medium">
                                    {% if admin.lastLoginAt %}
                                        {{ admin.lastLoginAt|date('M j, Y g:i A') }}
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="security-item">
                                <h6 class="text-muted mb-2">IP Address</h6>
                                <p class="mb-0">
                                    {% if admin.ipAddress %}
                                        <code class="bg-light text-dark px-2 py-1 rounded">{{ admin.ipAddress }}</code>
                                    {% else %}
                                        <span class="text-muted">Not recorded</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // Password confirmation validation
    $('#confirm_password').on('input', function() {
        var newPassword = $('#new_password').val();
        var confirmPassword = $(this).val();
        
        if (newPassword !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });

    // Image preview functionality
    $('#profile_image').on('change', function() {
        previewImage(this, '#image-preview');
    });

    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $(previewSelector).show();
                $(previewSelector + ' img').attr('src', e.target.result);
                
                // Also update the current profile image preview
                $('#current-profile-image').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            $(previewSelector).hide();
        }
    }
});
</script>
{% endblock %}
