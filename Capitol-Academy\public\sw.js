// Capitol Academy Service Worker
// Version 1.0.0

const CACHE_NAME = 'capitol-academy-v1';
const STATIC_CACHE_URLS = [
    '/',
    '/favicons/favicon-32x32.png',
    '/favicons/favicon-16x16.png',
    '/favicons/apple-touch-icon.png',
    '/favicons/android-chrome-192x192.png',
    '/favicons/android-chrome-512x512.png',
    '/images/logo-horizontal.png',
    '/images/logo-round.png',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// Install event - cache static assets
self.addEventListener('install', event => {
    console.log('Capitol Academy SW: Installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Capitol Academy SW: Caching static assets');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Capitol Academy SW: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Capitol Academy SW: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Capitol Academy SW: Activating...');
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Capitol Academy SW: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Capitol Academy SW: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', event => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip admin routes
    if (event.request.url.includes('/admin/')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version if available
                if (response) {
                    console.log('Capitol Academy SW: Serving from cache', event.request.url);
                    return response;
                }

                // Otherwise fetch from network
                console.log('Capitol Academy SW: Fetching from network', event.request.url);
                return fetch(event.request)
                    .then(response => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // Clone the response
                        const responseToCache = response.clone();

                        // Cache static assets
                        if (event.request.url.includes('/favicons/') || 
                            event.request.url.includes('/images/') ||
                            event.request.url.includes('.css') ||
                            event.request.url.includes('.js')) {
                            
                            caches.open(CACHE_NAME)
                                .then(cache => {
                                    cache.put(event.request, responseToCache);
                                });
                        }

                        return response;
                    })
                    .catch(error => {
                        console.error('Capitol Academy SW: Fetch failed', error);
                        
                        // Return offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match('/');
                        }
                        
                        throw error;
                    });
            })
    );
});

// Background sync for offline form submissions
self.addEventListener('sync', event => {
    if (event.tag === 'contact-form-sync') {
        console.log('Capitol Academy SW: Background sync for contact form');
        event.waitUntil(syncContactForm());
    }
});

// Function to sync contact form data
async function syncContactForm() {
    try {
        // Get stored form data from IndexedDB
        const formData = await getStoredFormData();
        
        if (formData) {
            // Try to submit the form
            const response = await fetch('/contact', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                // Clear stored data on successful submission
                await clearStoredFormData();
                console.log('Capitol Academy SW: Contact form synced successfully');
            }
        }
    } catch (error) {
        console.error('Capitol Academy SW: Contact form sync failed', error);
    }
}

// Helper functions for IndexedDB operations
async function getStoredFormData() {
    // Implementation would depend on your IndexedDB setup
    return null;
}

async function clearStoredFormData() {
    // Implementation would depend on your IndexedDB setup
    return true;
}

// Push notification handling
self.addEventListener('push', event => {
    console.log('Capitol Academy SW: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'New update from Capitol Academy',
        icon: '/favicons/android-chrome-192x192.png',
        badge: '/favicons/favicon-32x32.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Explore Courses',
                icon: '/favicons/favicon-32x32.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/favicons/favicon-32x32.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('Capitol Academy', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Capitol Academy SW: Notification clicked');
    
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/courses')
        );
    } else if (event.action === 'close') {
        // Just close the notification
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

console.log('Capitol Academy Service Worker loaded successfully');
