<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles.php"/>
<link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standardwhite/styles.php"/>
<!--[if IE 7]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie7.css" />
<![endif]-->
<!--[if IE 6]>
    <link rel="stylesheet" type="text/css" href="http://www.capitol-academy.com/moodle/theme/standard/styles_ie6.css" />
<![endif]-->
    <meta name="keywords" content="moodle, New account "/>
    <title>New account</title>
	<link rel="canonical" href="http://capitol-academy.com/moodle/login/signup.html" />
    <link rel="shortcut icon" href="http://www.capitol-academy.com/moodle/theme/standardwhite/favicon.ico"/>
    <!--<style type="text/css">/*<![CDATA[*/ body{behavior:url(http://www.capitol-academy.com/moodle/lib/csshover.htc);} /*]]>*/</style>-->
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/javascript-static.js"></script>
<script type="text/javascript" src="../../moodle/lib/javascript-mod_php.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/overlib/overlib_cssstyle.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/cookies.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/ufo.js"></script>
<script type="text/javascript" src="http://www.capitol-academy.com/moodle/lib/dropdown.js"></script>  
<script type="text/javascript" defer="defer">
//<![CDATA[
setTimeout('fix_column_widths()', 20);
//]]>
</script>
<script type="text/javascript">
//<![CDATA[
function openpopup(url, name, options, fullscreen) {
    var fullurl = "http://www.capitol-academy.com/moodle" + url;
    var windowobj = window.open(fullurl, name, options);
    if (!windowobj) {
        return true;
    }
    if (fullscreen) {
        windowobj.moveTo(0, 0);
        windowobj.resizeTo(screen.availWidth, screen.availHeight);
    }
    windowobj.focus();
    return false;
}
function uncheckall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = false;
    }
}
function checkall() {
    var inputs = document.getElementsByTagName('input');
    for(var i = 0; i < inputs.length; i++) {
        inputs[i].checked = true;
    }
}
function inserttext(text) {
  text = ' ' + text + ' ';
  if ( opener.document.forms['theform'].message.createTextRange && opener.document.forms['theform'].message.caretPos) {
    var caretPos = opener.document.forms['theform'].message.caretPos;
    caretPos.text = caretPos.text.charAt(caretPos.text.length - 1) == ' ' ? text + ' ' : text;
  } else {
    opener.document.forms['theform'].message.value  += text;
  }
  opener.document.forms['theform'].message.focus();
}
addonload(function() { if(document.forms['mform1']) document.forms['mform1'].elements['username'].focus(); });
function getElementsByClassName(oElm, strTagName, oClassNames){
	var arrElements = (strTagName == "*" && oElm.all)? oElm.all : oElm.getElementsByTagName(strTagName);
	var arrReturnElements = new Array();
	var arrRegExpClassNames = new Array();
	if(typeof oClassNames == "object"){
		for(var i=0; i<oClassNames.length; i++){
			arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames[i].replace(/\-/g, "\\-") + "(\\s|$)"));
		}
	}
	else{
		arrRegExpClassNames.push(new RegExp("(^|\\s)" + oClassNames.replace(/\-/g, "\\-") + "(\\s|$)"));
	}
	var oElement;
	var bMatchesAll;
	for(var j=0; j<arrElements.length; j++){
		oElement = arrElements[j];
		bMatchesAll = true;
		for(var k=0; k<arrRegExpClassNames.length; k++){
			if(!arrRegExpClassNames[k].test(oElement.className)){
				bMatchesAll = false;
				break;
			}
		}
		if(bMatchesAll){
			arrReturnElements.push(oElement);
		}
	}
	return (arrReturnElements)
}
//]]>
</script>
</head>
<body class="login course-1 notloggedin dir-ltr lang-en_utf8" id="login-signup">
<div id="page">
    <div id="header" class=" clearfix">        <h1 class="headermain">New account</h1>
        <div class="headermenu"><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div></div>
    </div>    <div class="navbar clearfix">
        <div class="breadcrumb"><h2 class="accesshide ">You are here</h2> <ul>
<li class="first"><a onclick="this.target='_top'" href="../../moodle.html">Academy</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> <a onclick="this.target='_top'" href="../../moodle/login/index.html">Login</a></li><li> <span class="accesshide ">/&nbsp;</span><span class="arrow sep">&#x25BA;</span> New account</li></ul></div>
        <div class="navbutton"><div class="langmenu"><form action="/" method="get" id="chooselang" class="popupform"><div><select id="chooselang_jump" name="jump" onchange="self.location=document.getElementById('chooselang').jump.options[document.getElementById('chooselang').jump.selectedIndex].value;">
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=ar_utf8">عربي (ar)</option>
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=en_utf8" selected="selected">English (en)</option>
   <option value="http://www.capitol-academy.com/moodle/login/signup.php?lang=fr_utf8">Français (fr)</option>
</select><input type="hidden" name="sesskey" value="DE8xPOE5Wk"/><div id="noscriptchooselang" style="display: inline;"><input type="submit" value="Go"/></div><script type="text/javascript">
//<![CDATA[
document.getElementById("noscriptchooselang").style.display = "none";
//]]>
</script></div></form></div></div>
    </div>
    <!-- END OF HEADER -->
    <div id="content" class=" clearfix">
<form autocomplete="off" action="../../moodle/login/signup.html" method="post" accept-charset="utf-8" id="mform1" class="mform">
	<div style="display: none;"><input name="MAX_FILE_SIZE" type="hidden" value="8388608"/>
<input name="sesskey" type="hidden" value="DE8xPOE5Wk"/>
<input name="_qf__login_signup_form" type="hidden" value="1"/>
</div>
	<fieldset class="clearfix">
		<legend class="ftoggler">Choose your username and password</legend>
		<div class="advancedbutton"></div><div class="fcontainer clearfix">
		<div class="fitem required"><div class="fitemtitle"><label for="id_username">Username </label></div><div class="felement ftext"><input maxlength="100" size="12" name="username" type="text" id="id_username"/></div></div>
		<div class="fitem"><div class="fitemtitle"><div class="fstaticlabel"><label> </label></div></div><div class="felement fstatic">The password must have at least 8 characters, at least 1 digit(s), at least 1 lower case letter(s), at least 1 upper case letter(s), at least 1 non-alphanumeric character(s)&nbsp;</div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_password">Password </label></div><div class="felement fpassword"><input maxlength="32" size="12" autocomplete="off" name="password" type="password" id="id_password"/><script type="text/javascript">
//<![CDATA[
document.write('<div class="unmask"><input id="id_passwordunmask" value="1" type="checkbox" onclick="unmaskPassword(\'/')"/><label for="id_passwordunmask">Unmask<\/label><\/div>');
document.getElementById("id_password").setAttribute("autocomplete", "off");
//]]>
</script></div></div>
		</div></fieldset>
	<fieldset class="clearfix">
		<legend class="ftoggler">More details</legend>
		<div class="advancedbutton"></div><div class="fcontainer clearfix">
		<div class="fitem required"><div class="fitemtitle"><label for="id_email">Email address </label></div><div class="felement ftext"><input maxlength="100" size="25" name="email" type="text" id="id_email"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_email2">Email (again) </label></div><div class="felement ftext"><input maxlength="100" size="25" name="email2" type="text" id="id_email2"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_firstname">First name </label></div><div class="felement ftext"><input maxlength="100" size="30" name="firstname" type="text" id="id_firstname"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_lastname">Surname </label></div><div class="felement ftext"><input maxlength="100" size="30" name="lastname" type="text" id="id_lastname"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_city">City/town </label></div><div class="felement ftext"><input maxlength="20" size="20" name="city" type="text" id="id_city"/></div></div>
		<div class="fitem required"><div class="fitemtitle"><label for="id_country">Country </label></div><div class="felement fselect"><select name="country" id="id_country">
	<option value="" selected="selected">Select a country</option>
	<option value="AX">Åland Islands</option>
	<option value="AF">Afghanistan</option>
	<option value="AL">Albania</option>
	<option value="DZ">Algeria</option>
	<option value="AS">American Samoa</option>
	<option value="AD">Andorra</option>
	<option value="AO">Angola</option>
	<option value="AI">Anguilla</option>
	<option value="AQ">Antarctica</option>
	<option value="AG">Antigua And Barbuda</option>
	<option value="AR">Argentina</option>
	<option value="AM">Armenia</option>
	<option value="AW">Aruba</option>
	<option value="AU">Australia</option>
	<option value="AT">Austria</option>
	<option value="AZ">Azerbaijan</option>
	<option value="BS">Bahamas</option>
	<option value="BH">Bahrain</option>
	<option value="BD">Bangladesh</option>
	<option value="BB">Barbados</option>
	<option value="BY">Belarus</option>
	<option value="BE">Belgium</option>
	<option value="BZ">Belize</option>
	<option value="BJ">Benin</option>
	<option value="BM">Bermuda</option>
	<option value="BT">Bhutan</option>
	<option value="BO">Bolivia</option>
	<option value="BA">Bosnia And Herzegovina</option>
	<option value="BW">Botswana</option>
	<option value="BV">Bouvet Island</option>
	<option value="BR">Brazil</option>
	<option value="IO">British Indian Ocean Territory</option>
	<option value="BN">Brunei Darussalam</option>
	<option value="BG">Bulgaria</option>
	<option value="BF">Burkina Faso</option>
	<option value="BI">Burundi</option>
	<option value="CI">Côte D'Ivoire</option>
	<option value="KH">Cambodia</option>
	<option value="CM">Cameroon</option>
	<option value="CA">Canada</option>
	<option value="CV">Cape Verde</option>
	<option value="KY">Cayman Islands</option>
	<option value="CF">Central African Republic</option>
	<option value="TD">Chad</option>
	<option value="CL">Chile</option>
	<option value="CN">China</option>
	<option value="CX">Christmas Island</option>
	<option value="CC">Cocos (Keeling) Islands</option>
	<option value="CO">Colombia</option>
	<option value="KM">Comoros</option>
	<option value="CG">Congo</option>
	<option value="CD">Congo, The Democratic Republic Of The</option>
	<option value="CK">Cook Islands</option>
	<option value="CR">Costa Rica</option>
	<option value="HR">Croatia</option>
	<option value="CU">Cuba</option>
	<option value="CY">Cyprus</option>
	<option value="CZ">Czech Republic</option>
	<option value="DK">Denmark</option>
	<option value="DJ">Djibouti</option>
	<option value="DM">Dominica</option>
	<option value="DO">Dominican Republic</option>
	<option value="EC">Ecuador</option>
	<option value="EG">Egypt</option>
	<option value="SV">El Salvador</option>
	<option value="GQ">Equatorial Guinea</option>
	<option value="ER">Eritrea</option>
	<option value="EE">Estonia</option>
	<option value="ET">Ethiopia</option>
	<option value="FK">Falkland Islands (Malvinas)</option>
	<option value="FO">Faroe Islands</option>
	<option value="FJ">Fiji</option>
	<option value="FI">Finland</option>
	<option value="FR">France</option>
	<option value="GF">French Guiana</option>
	<option value="PF">French Polynesia</option>
	<option value="TF">French Southern Territories</option>
	<option value="GA">Gabon</option>
	<option value="GM">Gambia</option>
	<option value="GE">Georgia</option>
	<option value="DE">Germany</option>
	<option value="GH">Ghana</option>
	<option value="GI">Gibraltar</option>
	<option value="GR">Greece</option>
	<option value="GL">Greenland</option>
	<option value="GD">Grenada</option>
	<option value="GP">Guadeloupe</option>
	<option value="GU">Guam</option>
	<option value="GT">Guatemala</option>
	<option value="GG">Guernsey</option>
	<option value="GN">Guinea</option>
	<option value="GW">Guinea-Bissau</option>
	<option value="GY">Guyana</option>
	<option value="HT">Haiti</option>
	<option value="HM">Heard Island And Mcdonald Islands</option>
	<option value="VA">Holy See (Vatican City State)</option>
	<option value="HN">Honduras</option>
	<option value="HK">Hong Kong</option>
	<option value="HU">Hungary</option>
	<option value="IS">Iceland</option>
	<option value="IN">India</option>
	<option value="ID">Indonesia</option>
	<option value="IR">Iran, Islamic Republic Of</option>
	<option value="IQ">Iraq</option>
	<option value="IE">Ireland</option>
	<option value="IM">Isle Of Man</option>
	<option value="IL">Israel</option>
	<option value="IT">Italy</option>
	<option value="JM">Jamaica</option>
	<option value="JP">Japan</option>
	<option value="JE">Jersey</option>
	<option value="JO">Jordan</option>
	<option value="KZ">Kazakhstan</option>
	<option value="KE">Kenya</option>
	<option value="KI">Kiribati</option>
	<option value="KP">Korea, Democratic People's Republic Of</option>
	<option value="KR">Korea, Republic Of</option>
	<option value="KW">Kuwait</option>
	<option value="KG">Kyrgyzstan</option>
	<option value="LA">Lao People's Democratic Republic</option>
	<option value="LV">Latvia</option>
	<option value="LB">Lebanon</option>
	<option value="LS">Lesotho</option>
	<option value="LR">Liberia</option>
	<option value="LY">Libyan Arab Jamahiriya</option>
	<option value="LI">Liechtenstein</option>
	<option value="LT">Lithuania</option>
	<option value="LU">Luxembourg</option>
	<option value="MO">Macao</option>
	<option value="MK">Macedonia, The Former Yugoslav Republic Of</option>
	<option value="MG">Madagascar</option>
	<option value="MW">Malawi</option>
	<option value="MY">Malaysia</option>
	<option value="MV">Maldives</option>
	<option value="ML">Mali</option>
	<option value="MT">Malta</option>
	<option value="MH">Marshall Islands</option>
	<option value="MQ">Martinique</option>
	<option value="MR">Mauritania</option>
	<option value="MU">Mauritius</option>
	<option value="YT">Mayotte</option>
	<option value="MX">Mexico</option>
	<option value="FM">Micronesia, Federated States Of</option>
	<option value="MD">Moldova, Republic Of</option>
	<option value="MC">Monaco</option>
	<option value="MN">Mongolia</option>
	<option value="ME">Montenegro</option>
	<option value="MS">Montserrat</option>
	<option value="MA">Morocco</option>
	<option value="MZ">Mozambique</option>
	<option value="MM">Myanmar</option>
	<option value="NA">Namibia</option>
	<option value="NR">Nauru</option>
	<option value="NP">Nepal</option>
	<option value="NL">Netherlands</option>
	<option value="AN">Netherlands Antilles</option>
	<option value="NC">New Caledonia</option>
	<option value="NZ">New Zealand</option>
	<option value="NI">Nicaragua</option>
	<option value="NE">Niger</option>
	<option value="NG">Nigeria</option>
	<option value="NU">Niue</option>
	<option value="NF">Norfolk Island</option>
	<option value="MP">Northern Mariana Islands</option>
	<option value="NO">Norway</option>
	<option value="OM">Oman</option>
	<option value="PK">Pakistan</option>
	<option value="PW">Palau</option>
	<option value="PS">Palestinian Territory, Occupied</option>
	<option value="PA">Panama</option>
	<option value="PG">Papua New Guinea</option>
	<option value="PY">Paraguay</option>
	<option value="PE">Peru</option>
	<option value="PH">Philippines</option>
	<option value="PN">Pitcairn</option>
	<option value="PL">Poland</option>
	<option value="PT">Portugal</option>
	<option value="PR">Puerto Rico</option>
	<option value="QA">Qatar</option>
	<option value="RE">Réunion</option>
	<option value="RO">Romania</option>
	<option value="RU">Russian Federation</option>
	<option value="RW">Rwanda</option>
	<option value="BL">Saint Barthélemy</option>
	<option value="SH">Saint Helena</option>
	<option value="KN">Saint Kitts And Nevis</option>
	<option value="LC">Saint Lucia</option>
	<option value="MF">Saint Martin</option>
	<option value="PM">Saint Pierre And Miquelon</option>
	<option value="VC">Saint Vincent And The Grenadines</option>
	<option value="WS">Samoa</option>
	<option value="SM">San Marino</option>
	<option value="ST">Sao Tome And Principe</option>
	<option value="SA">Saudi Arabia</option>
	<option value="SN">Senegal</option>
	<option value="RS">Serbia</option>
	<option value="SC">Seychelles</option>
	<option value="SL">Sierra Leone</option>
	<option value="SG">Singapore</option>
	<option value="SK">Slovakia</option>
	<option value="SI">Slovenia</option>
	<option value="SB">Solomon Islands</option>
	<option value="SO">Somalia</option>
	<option value="ZA">South Africa</option>
	<option value="GS">South Georgia And The South Sandwich Islands</option>
	<option value="ES">Spain</option>
	<option value="LK">Sri Lanka</option>
	<option value="SD">Sudan</option>
	<option value="SR">Suriname</option>
	<option value="SJ">Svalbard And Jan Mayen</option>
	<option value="SZ">Swaziland</option>
	<option value="SE">Sweden</option>
	<option value="CH">Switzerland</option>
	<option value="SY">Syrian Arab Republic</option>
	<option value="TW">Taiwan</option>
	<option value="TJ">Tajikistan</option>
	<option value="TZ">Tanzania, United Republic Of</option>
	<option value="TH">Thailand</option>
	<option value="TL">Timor-Leste</option>
	<option value="TG">Togo</option>
	<option value="TK">Tokelau</option>
	<option value="TO">Tonga</option>
	<option value="TT">Trinidad And Tobago</option>
	<option value="TN">Tunisia</option>
	<option value="TR">Turkey</option>
	<option value="TM">Turkmenistan</option>
	<option value="TC">Turks And Caicos Islands</option>
	<option value="TV">Tuvalu</option>
	<option value="UG">Uganda</option>
	<option value="UA">Ukraine</option>
	<option value="AE">United Arab Emirates</option>
	<option value="GB">United Kingdom</option>
	<option value="US">United States</option>
	<option value="UM">United States Minor Outlying Islands</option>
	<option value="UY">Uruguay</option>
	<option value="UZ">Uzbekistan</option>
	<option value="VU">Vanuatu</option>
	<option value="VE">Venezuela</option>
	<option value="VN">Viet Nam</option>
	<option value="VG">Virgin Islands, British</option>
	<option value="VI">Virgin Islands, U.S.</option>
	<option value="WF">Wallis And Futuna</option>
	<option value="EH">Western Sahara</option>
	<option value="YE">Yemen</option>
	<option value="ZM">Zambia</option>
	<option value="ZW">Zimbabwe</option>
</select></div></div>
		</div></fieldset>
	<fieldset class="hidden"><div>
		<div class="fitem"><div class="fitemtitle"><div class="fgrouplabel"><label> </label></div></div><fieldset class="felement fgroup"><input name="submitbutton" value="Create my new account" type="submit" id="id_submitbutton"/> <input name="cancel" value="Cancel" type="submit" onclick="skipClientValidation = true; return true;" id="id_cancel"/></fieldset></div>
		<div class="fdescription required">There are required fields in this form marked.</div>
		</div></fieldset>
</form>
<script type="text/javascript">
//<![CDATA[
var mform1items = Array();
lockoptionsallsetup('mform1');
//]]>
</script>
</div><div id="footer"><hr/><p class="helplink"></p><div class="logininfo">You are not logged in. (<a href="../../moodle/login/index.html">Login</a>)</div><div class="homelink"><a href="../../moodle.html">Home</a></div></div>
</div>
</body>
</html>