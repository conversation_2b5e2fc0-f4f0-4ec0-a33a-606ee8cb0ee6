{% extends 'admin/base.html.twig' %}

{% block title %}Edit Plan - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Plan: {{ plan.code }}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_plans') }}">Plans</a></li>
<li class="breadcrumb-item active">Edit Plan</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-layer-group mr-3" style="font-size: 2rem;"></i>
                        Edit Plan: {{ plan.code }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Plans Button -->
                        <a href="{{ path('admin_plans') }}"
                           class="btn mb-2 mb-md-0"
                           style="background: rgba(255, 255, 255, 0.2); color: white; border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 500; transition: all 0.3s ease;"
                           onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                           onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" class="needs-validation" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('plan_edit') }}">
            <input type="hidden" name="is_active" value="1">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Plan Code and Title Row -->
                            <div class="row">
                                <!-- Plan Code -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code" class="form-label">
                                            <i class="fas fa-hashtag text-primary mr-1"></i>
                                            Plan Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="code"
                                               name="code"
                                               value="{{ plan.code }}"
                                               placeholder="e.g., PLAN001, VB200"
                                               required
                                               maxlength="10"
                                               pattern="[A-Za-z]{2,4}[0-9]{1,4}"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please provide a valid plan code (2-4 letters followed by 1-4 numbers).
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Title -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-layer-group text-primary mr-1"></i>
                                            Plan Title <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="title"
                                               name="title"
                                               value="{{ plan.title }}"
                                               placeholder="Enter plan title"
                                               required
                                               maxlength="255"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please provide a plan title.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Duration and Price Row -->
                            <div class="row">
                                <!-- Duration -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="duration" class="form-label">
                                            <i class="fas fa-clock text-primary mr-1"></i>
                                            Duration (Days) <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control enhanced-field"
                                               id="duration"
                                               name="duration"
                                               value="{{ plan.duration }}"
                                               placeholder="30"
                                               min="1"
                                               max="365"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please enter a valid duration between 1 and 365 days.
                                        </div>
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price" class="form-label">
                                            <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                            Price (USD) <span class="text-danger">*</span>
                                        </label>
                                        <input type="number"
                                               class="form-control enhanced-field"
                                               id="price"
                                               name="price"
                                               value="{{ plan.price }}"
                                               placeholder="99.99"
                                               min="0"
                                               step="0.01"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                        <div class="invalid-feedback">
                                            Please enter a valid price.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Plan Description -->
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left text-primary mr-1"></i>
                                    Plan Description <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control enhanced-field"
                                          id="description"
                                          name="description"
                                          rows="4"
                                          placeholder="Enter detailed plan description..."
                                          required
                                          style="min-height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">{{ plan.description }}</textarea>
                                <div class="invalid-feedback">
                                    Please provide a plan description.
                                </div>
                            </div>

                            <!-- Video Selection -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-video text-primary mr-1"></i>
                                    Select Videos <span class="text-danger">*</span>
                                </label>

                                <div id="video-selection-container">
                                    {% if plan.videos and plan.videos|length > 0 %}
                                        {% for selectedVideo in plan.videos %}
                                        <div class="input-group mb-2 video-selection-item">
                                            <select class="form-control enhanced-field video-dropdown"
                                                    name="videos[]"
                                                    required
                                                    style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                                <option value="">Select a video...</option>
                                                {% for video in videos %}
                                                    <option value="{{ video.id }}"
                                                            {% if video.id == selectedVideo.id %}selected{% endif %}
                                                            data-category="{{ video.category|default('General') }}">
                                                        {{ video.title }}
                                                        {% if video.category %} - {{ video.category }}{% endif %}
                                                        {% if video.isFree %}(Free){% else %}(${{ video.price }}){% endif %}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <div class="input-group-append">
                                                {% if loop.first %}
                                                <button type="button" class="btn btn-success add-video-selection">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                {% else %}
                                                <button type="button" class="btn btn-danger remove-video-selection">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <button type="button" class="btn btn-success add-video-selection">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="input-group mb-2 video-selection-item">
                                            <select class="form-control enhanced-field video-dropdown"
                                                    name="videos[]"
                                                    required
                                                    style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                                                <option value="">Select a video...</option>
                                                {% for video in videos %}
                                                    <option value="{{ video.id }}"
                                                            data-category="{{ video.category|default('General') }}">
                                                        {{ video.title }}
                                                        {% if video.category %} - {{ video.category }}{% endif %}
                                                        {% if video.isFree %}(Free){% else %}(${{ video.price }}){% endif %}
                                                    </option>
                                                {% endfor %}
                                            </select>
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-success add-video-selection">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="invalid-feedback">
                                    Please select at least one video.
                                </div>
                                <small class="form-text text-muted">
                                    Click the + button to add more video selections. Each video can only be selected once.
                                </small>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6;">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none;">
                                <i class="fas fa-save mr-2"></i>
                                Update Plan
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <a href="{{ path('admin_plans') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times mr-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
        </form>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
<style>
/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-field:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--multiple {
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--multiple .select2-selection__rendered {
    padding-left: 8px !important;
    padding-right: 8px !important;
    padding-top: 4px !important;
    padding-bottom: 4px !important;
}

.select2-container--bootstrap4 .select2-selection--multiple:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice {
    background-color: #1e3c72 !important;
    border: 1px solid #1e3c72 !important;
    color: white !important;
    border-radius: 4px !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove {
    color: white !important;
}

.select2-container--bootstrap4 .select2-selection__choice__remove:hover {
    color: #ff6b6b !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Dynamic Video Selection Management
    $(document).on('click', '.add-video-selection', function() {
        var container = $('#video-selection-container');
        var newItem = `
            <div class="input-group mb-2 video-selection-item">
                <select class="form-control enhanced-field video-dropdown"
                        name="videos[]"
                        required
                        style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                    <option value="">Select a video...</option>
                    {% for video in videos %}
                        <option value="{{ video.id }}"
                                data-category="{{ video.category|default('General') }}">
                            {{ video.title }}
                            {% if video.category %} - {{ video.category }}{% endif %}
                            {% if video.isFree %}(Free){% else %}(${{ video.price }}){% endif %}
                        </option>
                    {% endfor %}
                </select>
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-video-selection">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-video-selection">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
        updateVideoOptions();
    });

    // Remove video selection
    $(document).on('click', '.remove-video-selection', function() {
        $(this).closest('.video-selection-item').remove();
        updateVideoOptions();
    });

    // Update video options when selection changes
    $(document).on('change', '.video-dropdown', function() {
        updateVideoOptions();
    });

    // Function to disable already selected videos in other dropdowns
    function updateVideoOptions() {
        var selectedVideos = [];
        $('.video-dropdown').each(function() {
            var value = $(this).val();
            if (value) {
                selectedVideos.push(value);
            }
        });

        $('.video-dropdown').each(function() {
            var currentValue = $(this).val();
            $(this).find('option').each(function() {
                var optionValue = $(this).val();
                if (optionValue && selectedVideos.includes(optionValue) && optionValue !== currentValue) {
                    $(this).prop('disabled', true);
                } else {
                    $(this).prop('disabled', false);
                }
            });
        });
    }

    // Initialize video options on page load
    updateVideoOptions();

    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
