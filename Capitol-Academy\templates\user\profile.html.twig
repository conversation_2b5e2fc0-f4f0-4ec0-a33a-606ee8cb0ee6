{% extends 'base.html.twig' %}

{% block title %}My Profile - Capitol Academy{% endblock %}

{% block meta_description %}Manage your Capitol Academy profile, update personal information, and track your trading education progress.{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- Profile Header Section -->
    <section class="py-5" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-user-circle me-3"></i>
                        My Profile
                    </h1>
                    <p class="lead mb-4">
                        Manage your Capitol Academy account settings and track your trading education journey.
                    </p>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-transparent p-0 mb-0">
                            <li class="breadcrumb-item">
                                <a href="{{ path('app_user_home') }}" class="text-white-50 text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item active text-white" aria-current="page">Profile</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-lg-4 text-center">
                    <div class="user-avatar-large">
                        {% if user.profilePicture %}
                            <img src="{{ asset('images/uploads/profiles/' ~ user.profilePicture) }}" 
                                 alt="Profile Picture" 
                                 class="rounded-circle img-fluid shadow-lg"
                                 style="max-width: 200px; border: 5px solid rgba(255,255,255,0.3);">
                        {% else %}
                            <img src="{{ asset('images/user-default-pp.png') }}" 
                                 alt="Default Profile" 
                                 class="rounded-circle img-fluid shadow-lg"
                                 style="max-width: 200px; border: 5px solid rgba(255,255,255,0.3);">
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Profile Information Card -->
                <div class="col-lg-8 mb-4">
                    <div class="card shadow-sm border-0">
                        <div class="card-header" style="background: #011a2d; color: white;">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                Profile Information
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">First Name</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        {{ user.firstName }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">Last Name</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        {{ user.lastName }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">Email Address</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        {{ user.email }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">Phone Number</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        {% if user.phone %}
                                            <i class="fas fa-phone me-2 text-success"></i>
                                            {{ user.phone }}
                                        {% else %}
                                            <span class="text-muted">Not provided</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">Country</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        {% if user.country %}
                                            <i class="fas fa-flag me-2 text-info"></i>
                                            {{ user.country.name }}
                                        {% else %}
                                            <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label class="form-label fw-bold text-muted">Member Since</label>
                                    <div class="form-control-plaintext border rounded p-3 bg-light">
                                        <i class="fas fa-calendar me-2 text-warning"></i>
                                        {{ user.createdAt|date('F j, Y') }}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="d-flex flex-wrap gap-3 mt-4">
                                <button class="btn btn-primary btn-lg" onclick="enableEditMode()">
                                    <i class="fas fa-edit me-2"></i>
                                    Edit Profile
                                </button>
                                <button class="btn btn-outline-secondary btn-lg" onclick="changePassword()">
                                    <i class="fas fa-key me-2"></i>
                                    Change Password
                                </button>
                                <button class="btn btn-outline-info btn-lg" onclick="uploadProfilePicture()">
                                    <i class="fas fa-camera me-2"></i>
                                    Update Photo
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Status & Statistics -->
                <div class="col-lg-4">
                    <!-- Account Status Card -->
                    <div class="card shadow-sm border-0 mb-4">
                        <div class="card-header" style="background: #a90418; color: white;">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                Account Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Verification Status:</strong><br>
                                {% if user.isVerified %}
                                    <span class="badge bg-success fs-6 mt-1">
                                        <i class="fas fa-check-circle me-1"></i>Verified
                                    </span>
                                {% else %}
                                    <span class="badge bg-warning fs-6 mt-1">
                                        <i class="fas fa-clock me-1"></i>Pending Verification
                                    </span>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>Account Status:</strong><br>
                                {% if user.isBlocked %}
                                    <span class="badge bg-danger fs-6 mt-1">
                                        <i class="fas fa-ban me-1"></i>Blocked
                                    </span>
                                {% else %}
                                    <span class="badge bg-success fs-6 mt-1">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>User Role:</strong><br>
                                <span class="badge bg-primary fs-6 mt-1">
                                    <i class="fas fa-user me-1"></i>Student
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="card shadow-sm border-0 mb-4">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 text-dark">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ path('app_courses') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-graduation-cap me-2"></i>
                                    Browse Courses
                                </a>
                                <a href="{{ path('app_user_courses') }}" class="btn btn-outline-success">
                                    <i class="fas fa-book me-2"></i>
                                    My Courses
                                </a>
                                <a href="{{ path('app_user_orders') }}" class="btn btn-outline-info">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    Order History
                                </a>
                                <a href="{{ path('app_free_videos') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-play me-2"></i>
                                    Free Videos
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Trading Progress Card -->
                    <div class="card shadow-sm border-0">
                        <div class="card-header" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                Trading Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Courses Enrolled</small>
                                <div class="progress mt-1" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted">0 courses</small>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">Videos Watched</small>
                                <div class="progress mt-1" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted">0 videos</small>
                            </div>
                            <div class="text-center mt-3">
                                <a href="{{ path('app_user_home') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    View Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Profile Edit Modal (Placeholder for future enhancement) -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #011a2d; color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    Edit Profile
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">Profile editing functionality will be available soon. Please contact support for any profile changes.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function enableEditMode() {
    // Show modal for now - can be enhanced later with actual edit form
    var editModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
    editModal.show();
}

function changePassword() {
    alert('Password change functionality will be available soon. Please contact support.');
}

function uploadProfilePicture() {
    alert('Profile picture upload will be available soon. Please contact support.');
}
</script>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.form-control-plaintext {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
}

.badge {
    font-size: 0.875rem;
}

.progress {
    background-color: #e9ecef;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
