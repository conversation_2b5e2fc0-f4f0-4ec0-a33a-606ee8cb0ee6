<?php

namespace App\Controller;

use App\Entity\MarketAnalysis;
use App\Form\MarketAnalysisType;
use App\Repository\MarketAnalysisRepository;
use App\Service\AdminPermissionService;
use App\Service\ErrorHandlingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/admin/market_analysis')]
#[IsGranted('ROLE_ADMIN')]
class AdminMarketAnalysisController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private MarketAnalysisRepository $marketAnalysisRepository,
        private AdminPermissionService $permissionService,
        private ErrorHandlingService $errorHandlingService,
        private ValidatorInterface $validator,
        private SluggerInterface $slugger
    ) {}

    #[Route('/', name: 'admin_market_analysis_index', methods: ['GET'])]
    public function index(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.view')) {
            $this->permissionService->denyAccess('market analysis management');
        }

        $search = $request->query->get('search', '');
        $articles = $this->marketAnalysisRepository->findWithSearch($search);
        $stats = $this->marketAnalysisRepository->getMarketAnalysisStats();

        return $this->render('admin/market_analysis/index.html.twig', [
            'articles' => $articles,
            'search' => $search,
            'stats' => $stats
        ]);
    }

    #[Route('/create', name: 'admin_market_analysis_create', methods: ['GET', 'POST'])]
    public function create(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.create')) {
            $this->permissionService->denyAccess('creating market analysis articles');
        }

        $article = new MarketAnalysis();

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('market_analysis_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->render('admin/market_analysis/create.html.twig', ['article' => $article]);
            }

            try {
                // Set article properties from form data
                $article->setTitle($request->request->get('title'));
                $article->setAssetType($request->request->get('asset_type'));
                $article->setExcerpt($request->request->get('excerpt'));
                $article->setContent($request->request->get('content'));
                $article->setAuthor($request->request->get('author'));



                // Handle publish date
                $publishDateString = $request->request->get('publish_date');
                if ($publishDateString) {
                    $publishDate = new \DateTime($publishDateString);
                    $article->setPublishDate($publishDate);
                }

                // Set default author if empty
                if (empty($article->getAuthor())) {
                    $article->setAuthor('Capitol Academy Analyst');
                }

                // Handle thumbnail image upload
                $thumbnailFile = $request->files->get('thumbnailImage');
                if ($thumbnailFile) {
                    $extension = $thumbnailFile->guessExtension();
                    $newFilename = $this->generateProfessionalFilename($extension, 'thumbnail');

                    try {
                        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/thumbnails';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        $thumbnailFile->move($uploadDir, basename($newFilename));
                        $article->setThumbnailImage($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Failed to upload thumbnail image.');
                        return $this->render('admin/market_analysis/create.html.twig', ['article' => $article]);
                    }
                }

                // Handle featured image upload
                $featuredImageFile = $request->files->get('featured_image');
                if ($featuredImageFile) {
                    $extension = $featuredImageFile->guessExtension();
                    $newFilename = $this->generateProfessionalFilename($extension, 'featured');

                    try {
                        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/featured';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        $featuredImageFile->move($uploadDir, basename($newFilename));
                        $article->setFeaturedImage($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Failed to upload featured image.');
                        return $this->render('admin/market_analysis/create.html.twig', ['article' => $article]);
                    }
                }

                // Set default status to active
                $article->setIsActive(true);

                // Validate the entity
                $errors = $this->validator->validate($article);
                if (count($errors) > 0) {
                    foreach ($errors as $error) {
                        $this->addFlash('error', $error->getMessage());
                    }
                    return $this->render('admin/market_analysis/create.html.twig', ['article' => $article]);
                }

                $this->entityManager->persist($article);
                $this->entityManager->flush();

                $this->addFlash('success', 'Market analysis article created successfully!');
                return $this->redirectToRoute('admin_market_analysis_index');

            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while creating the article. Please try again.');
            }
        }

        return $this->render('admin/market_analysis/create.html.twig', ['article' => $article]);
    }

    #[Route('/{slug}/edit', name: 'admin_market_analysis_edit_readable', methods: ['GET', 'POST'])]
    public function editBySlug(Request $request, string $slug): Response
    {
        // Find article by slug
        $article = $this->marketAnalysisRepository->findBySlug($slug);
        if (!$article) {
            throw $this->createNotFoundException('Market analysis article not found.');
        }

        return $this->edit($request, $article);
    }

    #[Route('/{id}/edit', name: 'admin_market_analysis_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, MarketAnalysis $article): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.edit')) {
            $this->permissionService->denyAccess('editing market analysis articles');
        }

        $form = $this->createForm(MarketAnalysisType::class, $article);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Set default author if empty
                if (empty($article->getAuthor())) {
                    $article->setAuthor('Capitol Academy Analyst');
                }

                // Handle thumbnail image upload
                $thumbnailFile = $request->files->get('thumbnailImage');
                if ($thumbnailFile) {
                    // Delete old thumbnail if exists
                    if ($article->getThumbnailImage()) {
                        $oldFile = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/' . $article->getThumbnailImage();
                        if (file_exists($oldFile)) {
                            unlink($oldFile);
                        }
                    }

                    $extension = $thumbnailFile->guessExtension();
                    $newFilename = $this->generateProfessionalFilename($extension, 'thumbnail');

                    try {
                        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/thumbnails';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        $thumbnailFile->move($uploadDir, basename($newFilename));
                        $article->setThumbnailImage($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Failed to upload thumbnail image.');
                        return $this->render('admin/market_analysis/edit.html.twig', [
                            'form' => $form->createView(),
                            'article' => $article
                        ]);
                    }
                }

                // Handle featured image upload
                $featuredImageFile = $request->files->get('featured_image');
                if ($featuredImageFile) {
                    // Delete old featured image if exists
                    if ($article->getFeaturedImage()) {
                        $oldFile = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/' . $article->getFeaturedImage();
                        if (file_exists($oldFile)) {
                            unlink($oldFile);
                        }
                    }

                    $extension = $featuredImageFile->guessExtension();
                    $newFilename = $this->generateProfessionalFilename($extension, 'featured');

                    try {
                        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/featured';
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        $featuredImageFile->move($uploadDir, basename($newFilename));
                        $article->setFeaturedImage($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Failed to upload featured image.');
                        return $this->render('admin/market_analysis/edit.html.twig', [
                            'form' => $form->createView(),
                            'article' => $article
                        ]);
                    }
                }



                $article->setUpdatedAt(new \DateTimeImmutable());
                $this->entityManager->flush();

                $this->addFlash('success', 'Market analysis article updated successfully!');
                return $this->redirectToRoute('admin_market_analysis_index');

            } catch (\Exception $e) {
                $this->errorHandlingService->logError('Market Analysis Update Error', $e);
                $this->addFlash('error', 'An error occurred while updating the article. Please try again.');
            }
        }

        return $this->render('admin/market_analysis/edit.html.twig', [
            'form' => $form->createView(),
            'article' => $article
        ]);
    }

    #[Route('/{slug}', name: 'admin_market_analysis_show_readable', methods: ['GET'])]
    public function showBySlug(string $slug): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.view')) {
            $this->permissionService->denyAccess('viewing market analysis articles');
        }

        // Find article by slug
        $article = $this->marketAnalysisRepository->findBySlug($slug);
        if (!$article) {
            throw $this->createNotFoundException('Market analysis article not found.');
        }

        return $this->render('admin/market_analysis/show.html.twig', [
            'article' => $article
        ]);
    }

    #[Route('/{id}', name: 'admin_market_analysis_show', methods: ['GET'], requirements: ['id' => '\d+'])]
    public function show(MarketAnalysis $article): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.view')) {
            $this->permissionService->denyAccess('viewing market analysis articles');
        }

        return $this->render('admin/market_analysis/show.html.twig', [
            'article' => $article
        ]);
    }



    #[Route('/{id}/delete', name: 'admin_market_analysis_delete', methods: ['POST'])]
    public function delete(Request $request, MarketAnalysis $article): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.delete')) {
            return $this->json(['success' => false, 'message' => 'You do not have permission to delete market analysis articles.']);
        }

        if ($this->isCsrfTokenValid('delete' . $article->getId(), $request->request->get('_token'))) {
            try {
                // Delete thumbnail file if exists
                if ($article->getThumbnailImage()) {
                    $thumbnailPath = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/' . $article->getThumbnailImage();
                    if (file_exists($thumbnailPath)) {
                        unlink($thumbnailPath);
                    }
                }

                $this->entityManager->remove($article);
                $this->entityManager->flush();

                return $this->json(['success' => true, 'message' => 'Market analysis article deleted successfully!']);
            } catch (\Exception $e) {
                $this->errorHandlingService->logError('Market Analysis Deletion Error', $e);
                return $this->json(['success' => false, 'message' => 'An error occurred while deleting the article.']);
            }
        }

        return $this->json(['success' => false, 'message' => 'Invalid CSRF token.']);
    }

    #[Route('/{id}/toggle-status', name: 'admin_market_analysis_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, MarketAnalysis $article): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('market_analysis.edit')) {
            return $this->json(['success' => false, 'message' => 'You do not have permission to modify market analysis articles.']);
        }

        if ($this->isCsrfTokenValid('toggle' . $article->getId(), $request->request->get('_token'))) {
            try {
                $newStatus = !$article->isActive();
                $article->setIsActive($newStatus);
                $article->setUpdatedAt(new \DateTimeImmutable());
                $this->entityManager->flush();

                $statusLabel = $newStatus ? 'active' : 'inactive';
                return $this->json([
                    'success' => true,
                    'message' => "Article status changed to {$statusLabel}!",
                    'newStatus' => $statusLabel,
                    'isActive' => $newStatus
                ]);
            } catch (\Exception $e) {
                $this->errorHandlingService->logError('Market Analysis Status Toggle Error', $e);
                return $this->json(['success' => false, 'message' => 'An error occurred while updating the article status.']);
            }
        }

        return $this->json(['success' => false, 'message' => 'Invalid CSRF token.']);
    }



    /**
     * Generate professional filename for market analysis images
     */
    private function generateProfessionalFilename(string $extension, string $imageType = 'general'): string
    {
        $baseName = 'marketanalysis_' . $imageType;

        // Determine the upload directory based on image type
        $subDir = $imageType === 'thumbnail' ? 'thumbnails' : 'featured';
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/market_analysis/' . $subDir;

        // Ensure directory exists
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $counter = 1;

        do {
            $fileName = $baseName . '_' . sprintf('%04d', $counter) . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            $counter++;
        } while (file_exists($filePath));

        return $subDir . '/' . $fileName;
    }
}
