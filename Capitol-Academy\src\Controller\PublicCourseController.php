<?php

namespace App\Controller;

use App\Entity\Course;
use App\Entity\Contact;
use App\Repository\CourseRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/courses')]
class PublicCourseController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private CourseRepository $courseRepository
    ) {}

    #[Route('/', name: 'public_courses_index')]
    public function index(): Response
    {
        $courses = $this->courseRepository->findBy(
            ['is_active' => true],
            ['created_at' => 'DESC']
        );

        return $this->render('public/courses/index.html.twig', [
            'courses' => $courses,
        ]);
    }

    #[Route('/{code}', name: 'public_course_detail')]
    public function detail(string $code): Response
    {
        $course = $this->courseRepository->findOneBy([
            'code' => $code,
            'is_active' => true
        ]);

        if (!$course) {
            throw $this->createNotFoundException('Course not found.');
        }

        return $this->render('public/courses/detail.html.twig', [
            'course' => $course,
        ]);
    }

    #[Route('/{code}/contact', name: 'public_course_contact', methods: ['POST'])]
    public function contact(string $code, Request $request, ValidatorInterface $validator): Response
    {
        $course = $this->courseRepository->findOneBy([
            'code' => $code,
            'is_active' => true
        ]);

        if (!$course) {
            throw $this->createNotFoundException('Course not found.');
        }

        $name = $request->request->get('name');
        $email = $request->request->get('email');
        $message = $request->request->get('message');

        // Create contact entry
        $contact = new Contact();
        $contact->setName($name);
        $contact->setEmail($email);
        $contact->setSubject('Course Interest: ' . $course->getTitle());
        $contact->setMessage($message . "\n\nCourse Code: " . $course->getCode());
        $contact->setSource('course_detail');
        $contact->setIpAddress($request->getClientIp());
        $contact->setCreatedAt(new \DateTimeImmutable());

        // Validate
        $errors = $validator->validate($contact);
        if (count($errors) > 0) {
            $this->addFlash('error', 'Please check your form data and try again.');
            return $this->redirectToRoute('public_course_detail', ['code' => $code]);
        }

        $this->entityManager->persist($contact);
        $this->entityManager->flush();

        $this->addFlash('success', 'Thank you for your interest! We will contact you soon.');
        return $this->redirectToRoute('public_course_detail', ['code' => $code]);
    }
}
