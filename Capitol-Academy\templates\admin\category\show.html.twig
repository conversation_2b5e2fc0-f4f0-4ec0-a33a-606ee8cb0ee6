{% extends 'admin/base.html.twig' %}

{% block title %}Category Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Category Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_category_index') }}">Categories</a></li>
<li class="breadcrumb-item active">{{ category.name }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-folder mr-3" style="font-size: 2rem;"></i>
                        Category Details: {{ category.name }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">


                        <!-- Print Category Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Category Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Categories Button -->
                        <a href="{{ path('admin_category_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
                <!-- Single Column Layout -->
                <div class="row">
                    <div class="col-12">

                        <!-- Category Name and Status Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Category Name -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-folder text-primary mr-1"></i>
                                        Category Name
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {{ category.name }}
                                    </div>
                                </div>
                            </div>

                            <!-- Category Status -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-toggle-on text-primary mr-1"></i>
                                        Status
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {% if category.active %}
                                            <span class="badge" style="background: #28a745; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        {% else %}
                                            <span class="badge" style="background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-pause-circle mr-1"></i> Inactive
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Display Options Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Display in Courses -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-graduation-cap text-primary mr-1"></i>
                                        Display in Courses
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {% if category.displayInCourses %}
                                            <span class="badge" style="background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-check mr-1"></i> Yes
                                            </span>
                                        {% else %}
                                            <span class="badge" style="background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-times mr-1"></i> No
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Display in Videos -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-video text-primary mr-1"></i>
                                        Display in Videos
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {% if category.displayInVideos %}
                                            <span class="badge" style="background: #17a2b8; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-check mr-1"></i> Yes
                                            </span>
                                        {% else %}
                                            <span class="badge" style="background: #6c757d; color: white; padding: 0.5rem 0.75rem; border-radius: 6px; font-weight: 500;">
                                                <i class="fas fa-times mr-1"></i> No
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Description -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-align-left text-primary mr-1"></i>
                                Category Description
                            </label>
                            <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 100px;">
                                {{ category.description ?? 'No description provided' }}
                            </div>
                        </div>

                        <!-- Timestamps Row -->
                        <div class="row print-two-column clearfix">
                            <!-- Created At -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                        Created At
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {{ category.createdAt ? category.createdAt|date('F j, Y \\a\\t g:i A') : 'Not available' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Updated At -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-calendar-edit text-primary mr-1"></i>
                                        Last Updated
                                    </label>
                                    <div class="form-control-plaintext enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; height: calc(1.6em + 1.25rem + 4px); display: flex; align-items: center;">
                                        {{ category.updatedAt ? category.updatedAt|date('F j, Y \\a\\t g:i A') : 'Not available' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
    </div>
</div>

<style>
/* Enhanced Display Field Styling */
.enhanced-display-field {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: normal;
}

.enhanced-display-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Form Label Icons */
.form-label i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

/* Professional Badge Styling */
.badge {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Print Styles */
@media print {
    .print-two-column {
        page-break-inside: avoid;
    }
    
    .btn, .alert {
        display: none !important;
    }
    
    .card-header {
        background: #011a2d !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>

<script>
function printCategoryDetails() {
    window.print();
}
</script>
{% endblock %}
