<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Fix CourseModule entity field names to camelCase
 */
final class Version20250618000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix CourseModule entity field names from snake_case to camelCase';
    }

    public function up(Schema $schema): void
    {
        // Rename columns to match entity properties (camelCase)
        $this->addSql('ALTER TABLE course_module CHANGE created_at createdAt DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
        $this->addSql('ALTER TABLE course_module CHANGE updated_at updatedAt DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
    }

    public function down(Schema $schema): void
    {
        // Revert column names back to snake_case
        $this->addSql('ALTER TABLE course_module <PERSON>AN<PERSON> createdAt created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
        $this->addSql('ALTER TABLE course_module CHANGE updatedAt updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)"');
    }
}
