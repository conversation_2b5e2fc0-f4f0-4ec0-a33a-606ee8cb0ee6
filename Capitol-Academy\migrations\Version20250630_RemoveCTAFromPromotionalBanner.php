<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove CTA fields from promotional_banner table
 */
final class Version20250630_RemoveCTAFromPromotionalBanner extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove cta_text and cta_link columns from promotional_banner table';
    }

    public function up(Schema $schema): void
    {
        // Check if columns exist before dropping them
        $this->addSql('ALTER TABLE promotional_banner DROP COLUMN IF EXISTS cta_text');
        $this->addSql('ALTER TABLE promotional_banner DROP COLUMN IF EXISTS cta_link');
    }

    public function down(Schema $schema): void
    {
        // Re-add the columns if needed for rollback
        $this->addSql('ALTER TABLE promotional_banner ADD COLUMN cta_text VARCHAR(100) NULL');
        $this->addSql('ALTER TABLE promotional_banner ADD COLUMN cta_link VARCHAR(500) NULL');
    }
}
